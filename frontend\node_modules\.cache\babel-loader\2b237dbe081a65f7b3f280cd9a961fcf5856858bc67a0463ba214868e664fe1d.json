{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\aiplanet\\\\frontend\\\\src\\\\pages\\\\SimpleWorkflowBuilder.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Button } from '../components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';\nimport { Input } from '../components/ui/input';\nimport { MessageCircle, Database, Brain, Monitor, Save, Trash2, Play, Plus, Settings } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleWorkflowBuilder = () => {\n  _s();\n  const [workflow, setWorkflow] = useState({\n    nodes: [{\n      id: 1,\n      type: 'userQuery',\n      label: 'User Query',\n      config: {\n        placeholder: 'Enter your question...'\n      }\n    }, {\n      id: 2,\n      type: 'knowledgeBase',\n      label: 'Knowledge Base',\n      config: {\n        documentId: null,\n        maxResults: 5\n      }\n    }, {\n      id: 3,\n      type: 'llmEngine',\n      label: 'LLM Engine',\n      config: {\n        model: 'gpt-3.5-turbo',\n        temperature: 0.7\n      }\n    }, {\n      id: 4,\n      type: 'output',\n      label: 'Output',\n      config: {\n        format: 'text',\n        showSources: true\n      }\n    }]\n  });\n  const [selectedNode, setSelectedNode] = useState(null);\n  const nodeTypes = [{\n    type: 'userQuery',\n    label: 'User Query',\n    description: 'Entry point for user questions',\n    icon: MessageCircle,\n    color: 'bg-blue-500'\n  }, {\n    type: 'knowledgeBase',\n    label: 'Knowledge Base',\n    description: 'Upload PDFs and search context',\n    icon: Database,\n    color: 'bg-green-500'\n  }, {\n    type: 'llmEngine',\n    label: 'LLM Engine',\n    description: 'AI model for generating responses',\n    icon: Brain,\n    color: 'bg-yellow-500'\n  }, {\n    type: 'output',\n    label: 'Output',\n    description: 'Display final AI response',\n    icon: Monitor,\n    color: 'bg-red-500'\n  }];\n  const addNode = type => {\n    var _nodeTypes$find;\n    const newNode = {\n      id: Date.now(),\n      type,\n      label: ((_nodeTypes$find = nodeTypes.find(nt => nt.type === type)) === null || _nodeTypes$find === void 0 ? void 0 : _nodeTypes$find.label) || 'New Node',\n      config: getDefaultConfig(type)\n    };\n    setWorkflow(prev => ({\n      ...prev,\n      nodes: [...prev.nodes, newNode]\n    }));\n  };\n  const getDefaultConfig = type => {\n    const configs = {\n      userQuery: {\n        placeholder: 'Enter your question...'\n      },\n      knowledgeBase: {\n        documentId: null,\n        maxResults: 5\n      },\n      llmEngine: {\n        model: 'gpt-3.5-turbo',\n        temperature: 0.7,\n        maxTokens: 1000\n      },\n      output: {\n        format: 'text',\n        showSources: true\n      }\n    };\n    return configs[type] || {};\n  };\n  const updateNodeConfig = (nodeId, newConfig) => {\n    setWorkflow(prev => ({\n      ...prev,\n      nodes: prev.nodes.map(node => node.id === nodeId ? {\n        ...node,\n        config: {\n          ...node.config,\n          ...newConfig\n        }\n      } : node)\n    }));\n  };\n  const removeNode = nodeId => {\n    setWorkflow(prev => ({\n      ...prev,\n      nodes: prev.nodes.filter(node => node.id !== nodeId)\n    }));\n    if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n      setSelectedNode(null);\n    }\n  };\n  const clearWorkflow = () => {\n    setWorkflow({\n      nodes: []\n    });\n    setSelectedNode(null);\n  };\n  const saveWorkflow = () => {\n    localStorage.setItem('workflow', JSON.stringify(workflow));\n    alert('Workflow saved successfully!');\n  };\n  const executeWorkflow = () => {\n    console.log('Executing workflow:', workflow);\n    alert('Workflow execution started! Check the Chat interface for results.');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-full bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-80 xl:w-96 bg-background border-r border-border flex flex-col shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-foreground\",\n          children: \"Workflow Builder\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-muted-foreground mt-1\",\n          children: \"Build your AI workflow step by step\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-4 space-y-3 overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-sm font-medium text-foreground mb-2\",\n          children: \"Available Components\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), nodeTypes.map(nodeType => {\n          const Icon = nodeType.icon;\n          return /*#__PURE__*/_jsxDEV(Card, {\n            className: \"cursor-pointer hover:shadow-md transition-shadow\",\n            onClick: () => addNode(nodeType.type),\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              className: \"p-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `p-2 rounded-lg ${nodeType.color} text-white flex-shrink-0`,\n                  children: /*#__PURE__*/_jsxDEV(Icon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-medium text-foreground text-sm\",\n                    children: nodeType.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-muted-foreground mt-1\",\n                    children: nodeType.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Plus, {\n                  className: \"w-4 h-4 text-muted-foreground\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)\n          }, nodeType.type, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-border space-y-2\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: executeWorkflow,\n          className: \"w-full\",\n          size: \"sm\",\n          children: [/*#__PURE__*/_jsxDEV(Play, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), \"Execute Workflow\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: saveWorkflow,\n            variant: \"outline\",\n            size: \"sm\",\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), \"Save\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: clearWorkflow,\n            variant: \"outline\",\n            size: \"sm\",\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(Trash2, {\n              className: \"w-4 h-4 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), \"Clear\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-6 overflow-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-6\",\n            children: \"Current Workflow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), workflow.nodes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(Settings, {\n              className: \"w-12 h-12 text-muted-foreground mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-foreground mb-2\",\n              children: \"No Components Added\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-muted-foreground\",\n              children: \"Click on components in the sidebar to add them to your workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: workflow.nodes.map((node, index) => {\n              const nodeType = nodeTypes.find(nt => nt.type === node.type);\n              const Icon = (nodeType === null || nodeType === void 0 ? void 0 : nodeType.icon) || Settings;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [index > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -top-2 left-1/2 transform -translate-x-1/2 w-0.5 h-4 bg-border\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Card, {\n                  className: `cursor-pointer transition-all ${(selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === node.id ? 'ring-2 ring-primary' : ''}`,\n                  onClick: () => setSelectedNode(node),\n                  children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n                    className: \"pb-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `p-2 rounded-lg ${(nodeType === null || nodeType === void 0 ? void 0 : nodeType.color) || 'bg-gray-500'} text-white`,\n                          children: /*#__PURE__*/_jsxDEV(Icon, {\n                            className: \"w-4 h-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 222,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 221,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(CardTitle, {\n                            className: \"text-base\",\n                            children: node.label\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 225,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(CardDescription, {\n                            children: nodeType === null || nodeType === void 0 ? void 0 : nodeType.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 226,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 224,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: e => {\n                          e.stopPropagation();\n                          removeNode(node.id);\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Trash2, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 237,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 229,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-muted-foreground\",\n                      children: Object.entries(node.config).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"capitalize\",\n                          children: [key, \":\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 246,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: String(value)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 247,\n                          columnNumber: 33\n                        }, this)]\n                      }, key, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 245,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 23\n                }, this), index < workflow.nodes.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center py-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-0.5 h-6 bg-border\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 25\n                }, this)]\n              }, node.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), selectedNode && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-80 xl:w-96 bg-background border-l border-border flex flex-col shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground\",\n          children: \"Configure Node\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-muted-foreground\",\n          children: selectedNode.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: Object.entries(selectedNode.config).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"text-sm font-medium text-foreground capitalize\",\n              children: key.replace(/([A-Z])/g, ' $1').trim()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              value: value || '',\n              onChange: e => updateNodeConfig(selectedNode.id, {\n                [key]: e.target.value\n              }),\n              className: \"mt-1\",\n              placeholder: `Enter ${key}...`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 19\n            }, this)]\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleWorkflowBuilder, \"VO2RRSk6tjM6IvaTJG7/j6wWUoo=\");\n_c = SimpleWorkflowBuilder;\nexport default SimpleWorkflowBuilder;\nvar _c;\n$RefreshReg$(_c, \"SimpleWorkflowBuilder\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardDescription", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Input", "MessageCircle", "Database", "Brain", "Monitor", "Save", "Trash2", "Play", "Plus", "Settings", "jsxDEV", "_jsxDEV", "SimpleWorkflowBuilder", "_s", "workflow", "setWorkflow", "nodes", "id", "type", "label", "config", "placeholder", "documentId", "maxResults", "model", "temperature", "format", "showSources", "selectedNode", "setSelectedNode", "nodeTypes", "description", "icon", "color", "addNode", "_nodeTypes$find", "newNode", "Date", "now", "find", "nt", "getDefaultConfig", "prev", "configs", "userQuery", "knowledgeBase", "llmEngine", "maxTokens", "output", "updateNodeConfig", "nodeId", "newConfig", "map", "node", "removeNode", "filter", "clearWorkflow", "saveWorkflow", "localStorage", "setItem", "JSON", "stringify", "alert", "executeWorkflow", "console", "log", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nodeType", "Icon", "onClick", "size", "variant", "length", "index", "e", "stopPropagation", "Object", "entries", "key", "value", "String", "replace", "trim", "onChange", "target", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/aiplanet/frontend/src/pages/SimpleWorkflowBuilder.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Button } from '../components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';\nimport { Input } from '../components/ui/input';\nimport { \n  MessageCircle, \n  Database, \n  Brain, \n  Monitor,\n  Save,\n  Trash2,\n  Play,\n  Plus,\n  Settings\n} from 'lucide-react';\n\nconst SimpleWorkflowBuilder = () => {\n  const [workflow, setWorkflow] = useState({\n    nodes: [\n      { id: 1, type: 'userQuery', label: 'User Query', config: { placeholder: 'Enter your question...' } },\n      { id: 2, type: 'knowledgeBase', label: 'Knowledge Base', config: { documentId: null, maxResults: 5 } },\n      { id: 3, type: 'llmEngine', label: 'LLM Engine', config: { model: 'gpt-3.5-turbo', temperature: 0.7 } },\n      { id: 4, type: 'output', label: 'Output', config: { format: 'text', showSources: true } }\n    ]\n  });\n\n  const [selectedNode, setSelectedNode] = useState(null);\n\n  const nodeTypes = [\n    {\n      type: 'userQuery',\n      label: 'User Query',\n      description: 'Entry point for user questions',\n      icon: MessageCircle,\n      color: 'bg-blue-500',\n    },\n    {\n      type: 'knowledgeBase',\n      label: 'Knowledge Base',\n      description: 'Upload PDFs and search context',\n      icon: Database,\n      color: 'bg-green-500',\n    },\n    {\n      type: 'llmEngine',\n      label: 'LLM Engine',\n      description: 'AI model for generating responses',\n      icon: Brain,\n      color: 'bg-yellow-500',\n    },\n    {\n      type: 'output',\n      label: 'Output',\n      description: 'Display final AI response',\n      icon: Monitor,\n      color: 'bg-red-500',\n    },\n  ];\n\n  const addNode = (type) => {\n    const newNode = {\n      id: Date.now(),\n      type,\n      label: nodeTypes.find(nt => nt.type === type)?.label || 'New Node',\n      config: getDefaultConfig(type)\n    };\n    setWorkflow(prev => ({\n      ...prev,\n      nodes: [...prev.nodes, newNode]\n    }));\n  };\n\n  const getDefaultConfig = (type) => {\n    const configs = {\n      userQuery: { placeholder: 'Enter your question...' },\n      knowledgeBase: { documentId: null, maxResults: 5 },\n      llmEngine: { model: 'gpt-3.5-turbo', temperature: 0.7, maxTokens: 1000 },\n      output: { format: 'text', showSources: true },\n    };\n    return configs[type] || {};\n  };\n\n  const updateNodeConfig = (nodeId, newConfig) => {\n    setWorkflow(prev => ({\n      ...prev,\n      nodes: prev.nodes.map(node => \n        node.id === nodeId ? { ...node, config: { ...node.config, ...newConfig } } : node\n      )\n    }));\n  };\n\n  const removeNode = (nodeId) => {\n    setWorkflow(prev => ({\n      ...prev,\n      nodes: prev.nodes.filter(node => node.id !== nodeId)\n    }));\n    if (selectedNode?.id === nodeId) {\n      setSelectedNode(null);\n    }\n  };\n\n  const clearWorkflow = () => {\n    setWorkflow({ nodes: [] });\n    setSelectedNode(null);\n  };\n\n  const saveWorkflow = () => {\n    localStorage.setItem('workflow', JSON.stringify(workflow));\n    alert('Workflow saved successfully!');\n  };\n\n  const executeWorkflow = () => {\n    console.log('Executing workflow:', workflow);\n    alert('Workflow execution started! Check the Chat interface for results.');\n  };\n\n  return (\n    <div className=\"flex h-full bg-background\">\n      {/* Sidebar */}\n      <div className=\"w-80 xl:w-96 bg-background border-r border-border flex flex-col shadow-lg\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-border\">\n          <h2 className=\"text-lg font-semibold text-foreground\">Workflow Builder</h2>\n          <p className=\"text-sm text-muted-foreground mt-1\">\n            Build your AI workflow step by step\n          </p>\n        </div>\n\n        {/* Node Types */}\n        <div className=\"flex-1 p-4 space-y-3 overflow-y-auto\">\n          <h3 className=\"text-sm font-medium text-foreground mb-2\">Available Components</h3>\n          {nodeTypes.map((nodeType) => {\n            const Icon = nodeType.icon;\n            \n            return (\n              <Card\n                key={nodeType.type}\n                className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                onClick={() => addNode(nodeType.type)}\n              >\n                <CardContent className=\"p-4\">\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`p-2 rounded-lg ${nodeType.color} text-white flex-shrink-0`}>\n                      <Icon className=\"w-4 h-4\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"font-medium text-foreground text-sm\">\n                        {nodeType.label}\n                      </h3>\n                      <p className=\"text-xs text-muted-foreground mt-1\">\n                        {nodeType.description}\n                      </p>\n                    </div>\n                    <Plus className=\"w-4 h-4 text-muted-foreground\" />\n                  </div>\n                </CardContent>\n              </Card>\n            );\n          })}\n        </div>\n\n        {/* Actions */}\n        <div className=\"p-4 border-t border-border space-y-2\">\n          <Button onClick={executeWorkflow} className=\"w-full\" size=\"sm\">\n            <Play className=\"w-4 h-4 mr-2\" />\n            Execute Workflow\n          </Button>\n          \n          <div className=\"flex space-x-2\">\n            <Button onClick={saveWorkflow} variant=\"outline\" size=\"sm\" className=\"flex-1\">\n              <Save className=\"w-4 h-4 mr-2\" />\n              Save\n            </Button>\n            \n            <Button onClick={clearWorkflow} variant=\"outline\" size=\"sm\" className=\"flex-1\">\n              <Trash2 className=\"w-4 h-4 mr-2\" />\n              Clear\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Workflow Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Workflow Canvas */}\n        <div className=\"flex-1 p-6 overflow-auto\">\n          <div className=\"max-w-4xl mx-auto\">\n            <h2 className=\"text-xl font-semibold mb-6\">Current Workflow</h2>\n            \n            {workflow.nodes.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Settings className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-foreground mb-2\">\n                  No Components Added\n                </h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  Click on components in the sidebar to add them to your workflow\n                </p>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {workflow.nodes.map((node, index) => {\n                  const nodeType = nodeTypes.find(nt => nt.type === node.type);\n                  const Icon = nodeType?.icon || Settings;\n                  \n                  return (\n                    <div key={node.id} className=\"relative\">\n                      {index > 0 && (\n                        <div className=\"absolute -top-2 left-1/2 transform -translate-x-1/2 w-0.5 h-4 bg-border\"></div>\n                      )}\n                      \n                      <Card \n                        className={`cursor-pointer transition-all ${\n                          selectedNode?.id === node.id ? 'ring-2 ring-primary' : ''\n                        }`}\n                        onClick={() => setSelectedNode(node)}\n                      >\n                        <CardHeader className=\"pb-3\">\n                          <div className=\"flex items-center justify-between\">\n                            <div className=\"flex items-center space-x-3\">\n                              <div className={`p-2 rounded-lg ${nodeType?.color || 'bg-gray-500'} text-white`}>\n                                <Icon className=\"w-4 h-4\" />\n                              </div>\n                              <div>\n                                <CardTitle className=\"text-base\">{node.label}</CardTitle>\n                                <CardDescription>{nodeType?.description}</CardDescription>\n                              </div>\n                            </div>\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={(e) => {\n                                e.stopPropagation();\n                                removeNode(node.id);\n                              }}\n                            >\n                              <Trash2 className=\"w-4 h-4\" />\n                            </Button>\n                          </div>\n                        </CardHeader>\n                        \n                        <CardContent>\n                          <div className=\"text-sm text-muted-foreground\">\n                            {Object.entries(node.config).map(([key, value]) => (\n                              <div key={key} className=\"flex justify-between\">\n                                <span className=\"capitalize\">{key}:</span>\n                                <span>{String(value)}</span>\n                              </div>\n                            ))}\n                          </div>\n                        </CardContent>\n                      </Card>\n                      \n                      {index < workflow.nodes.length - 1 && (\n                        <div className=\"flex justify-center py-2\">\n                          <div className=\"w-0.5 h-6 bg-border\"></div>\n                        </div>\n                      )}\n                    </div>\n                  );\n                })}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Configuration Panel */}\n      {selectedNode && (\n        <div className=\"w-80 xl:w-96 bg-background border-l border-border flex flex-col shadow-lg\">\n          <div className=\"p-4 border-b border-border\">\n            <h3 className=\"text-lg font-semibold text-foreground\">Configure Node</h3>\n            <p className=\"text-sm text-muted-foreground\">{selectedNode.label}</p>\n          </div>\n          \n          <div className=\"flex-1 p-4 overflow-y-auto\">\n            <div className=\"space-y-4\">\n              {Object.entries(selectedNode.config).map(([key, value]) => (\n                <div key={key}>\n                  <label className=\"text-sm font-medium text-foreground capitalize\">\n                    {key.replace(/([A-Z])/g, ' $1').trim()}\n                  </label>\n                  <Input\n                    value={value || ''}\n                    onChange={(e) => updateNodeConfig(selectedNode.id, { [key]: e.target.value })}\n                    className=\"mt-1\"\n                    placeholder={`Enter ${key}...`}\n                  />\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SimpleWorkflowBuilder;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,IAAI,EAAEC,WAAW,EAAEC,eAAe,EAAEC,UAAU,EAAEC,SAAS,QAAQ,uBAAuB;AACjG,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SACEC,aAAa,EACbC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,QAAQ,QACH,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC;IACvCuB,KAAK,EAAE,CACL;MAAEC,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,YAAY;MAAEC,MAAM,EAAE;QAAEC,WAAW,EAAE;MAAyB;IAAE,CAAC,EACpG;MAAEJ,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,MAAM,EAAE;QAAEE,UAAU,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAE;IAAE,CAAC,EACtG;MAAEN,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,YAAY;MAAEC,MAAM,EAAE;QAAEI,KAAK,EAAE,eAAe;QAAEC,WAAW,EAAE;MAAI;IAAE,CAAC,EACvG;MAAER,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE;QAAEM,MAAM,EAAE,MAAM;QAAEC,WAAW,EAAE;MAAK;IAAE,CAAC;EAE7F,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMqC,SAAS,GAAG,CAChB;IACEZ,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,YAAY;IACnBY,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE/B,aAAa;IACnBgC,KAAK,EAAE;EACT,CAAC,EACD;IACEf,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,gBAAgB;IACvBY,WAAW,EAAE,gCAAgC;IAC7CC,IAAI,EAAE9B,QAAQ;IACd+B,KAAK,EAAE;EACT,CAAC,EACD;IACEf,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,YAAY;IACnBY,WAAW,EAAE,mCAAmC;IAChDC,IAAI,EAAE7B,KAAK;IACX8B,KAAK,EAAE;EACT,CAAC,EACD;IACEf,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,QAAQ;IACfY,WAAW,EAAE,2BAA2B;IACxCC,IAAI,EAAE5B,OAAO;IACb6B,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,OAAO,GAAIhB,IAAI,IAAK;IAAA,IAAAiB,eAAA;IACxB,MAAMC,OAAO,GAAG;MACdnB,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdpB,IAAI;MACJC,KAAK,EAAE,EAAAgB,eAAA,GAAAL,SAAS,CAACS,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACtB,IAAI,KAAKA,IAAI,CAAC,cAAAiB,eAAA,uBAAtCA,eAAA,CAAwChB,KAAK,KAAI,UAAU;MAClEC,MAAM,EAAEqB,gBAAgB,CAACvB,IAAI;IAC/B,CAAC;IACDH,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP1B,KAAK,EAAE,CAAC,GAAG0B,IAAI,CAAC1B,KAAK,EAAEoB,OAAO;IAChC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,gBAAgB,GAAIvB,IAAI,IAAK;IACjC,MAAMyB,OAAO,GAAG;MACdC,SAAS,EAAE;QAAEvB,WAAW,EAAE;MAAyB,CAAC;MACpDwB,aAAa,EAAE;QAAEvB,UAAU,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAE,CAAC;MAClDuB,SAAS,EAAE;QAAEtB,KAAK,EAAE,eAAe;QAAEC,WAAW,EAAE,GAAG;QAAEsB,SAAS,EAAE;MAAK,CAAC;MACxEC,MAAM,EAAE;QAAEtB,MAAM,EAAE,MAAM;QAAEC,WAAW,EAAE;MAAK;IAC9C,CAAC;IACD,OAAOgB,OAAO,CAACzB,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5B,CAAC;EAED,MAAM+B,gBAAgB,GAAGA,CAACC,MAAM,EAAEC,SAAS,KAAK;IAC9CpC,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP1B,KAAK,EAAE0B,IAAI,CAAC1B,KAAK,CAACoC,GAAG,CAACC,IAAI,IACxBA,IAAI,CAACpC,EAAE,KAAKiC,MAAM,GAAG;QAAE,GAAGG,IAAI;QAAEjC,MAAM,EAAE;UAAE,GAAGiC,IAAI,CAACjC,MAAM;UAAE,GAAG+B;QAAU;MAAE,CAAC,GAAGE,IAC/E;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,UAAU,GAAIJ,MAAM,IAAK;IAC7BnC,WAAW,CAAC2B,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP1B,KAAK,EAAE0B,IAAI,CAAC1B,KAAK,CAACuC,MAAM,CAACF,IAAI,IAAIA,IAAI,CAACpC,EAAE,KAAKiC,MAAM;IACrD,CAAC,CAAC,CAAC;IACH,IAAI,CAAAtB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEX,EAAE,MAAKiC,MAAM,EAAE;MAC/BrB,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAM2B,aAAa,GAAGA,CAAA,KAAM;IAC1BzC,WAAW,CAAC;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;IAC1Ba,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4B,YAAY,GAAGA,CAAA,KAAM;IACzBC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAAC/C,QAAQ,CAAC,CAAC;IAC1DgD,KAAK,CAAC,8BAA8B,CAAC;EACvC,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEnD,QAAQ,CAAC;IAC5CgD,KAAK,CAAC,mEAAmE,CAAC;EAC5E,CAAC;EAED,oBACEnD,OAAA;IAAKuD,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAExCxD,OAAA;MAAKuD,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBAExFxD,OAAA;QAAKuD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCxD,OAAA;UAAIuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E5D,OAAA;UAAGuD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGN5D,OAAA;QAAKuD,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDxD,OAAA;UAAIuD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACjFzC,SAAS,CAACsB,GAAG,CAAEoB,QAAQ,IAAK;UAC3B,MAAMC,IAAI,GAAGD,QAAQ,CAACxC,IAAI;UAE1B,oBACErB,OAAA,CAAChB,IAAI;YAEHuE,SAAS,EAAC,kDAAkD;YAC5DQ,OAAO,EAAEA,CAAA,KAAMxC,OAAO,CAACsC,QAAQ,CAACtD,IAAI,CAAE;YAAAiD,QAAA,eAEtCxD,OAAA,CAACf,WAAW;cAACsE,SAAS,EAAC,KAAK;cAAAC,QAAA,eAC1BxD,OAAA;gBAAKuD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCxD,OAAA;kBAAKuD,SAAS,EAAE,kBAAkBM,QAAQ,CAACvC,KAAK,2BAA4B;kBAAAkC,QAAA,eAC1ExD,OAAA,CAAC8D,IAAI;oBAACP,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACN5D,OAAA;kBAAKuD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BxD,OAAA;oBAAIuD,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAChDK,QAAQ,CAACrD;kBAAK;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC,eACL5D,OAAA;oBAAGuD,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,EAC9CK,QAAQ,CAACzC;kBAAW;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN5D,OAAA,CAACH,IAAI;kBAAC0D,SAAS,EAAC;gBAA+B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC,GAnBTC,QAAQ,CAACtD,IAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBd,CAAC;QAEX,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5D,OAAA;QAAKuD,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDxD,OAAA,CAACjB,MAAM;UAACgF,OAAO,EAAEX,eAAgB;UAACG,SAAS,EAAC,QAAQ;UAACS,IAAI,EAAC,IAAI;UAAAR,QAAA,gBAC5DxD,OAAA,CAACJ,IAAI;YAAC2D,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEnC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET5D,OAAA;UAAKuD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BxD,OAAA,CAACjB,MAAM;YAACgF,OAAO,EAAEjB,YAAa;YAACmB,OAAO,EAAC,SAAS;YAACD,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBAC3ExD,OAAA,CAACN,IAAI;cAAC6D,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAEnC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET5D,OAAA,CAACjB,MAAM;YAACgF,OAAO,EAAElB,aAAc;YAACoB,OAAO,EAAC,SAAS;YAACD,IAAI,EAAC,IAAI;YAACT,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBAC5ExD,OAAA,CAACL,MAAM;cAAC4D,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,SAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eAEnCxD,OAAA;QAAKuD,SAAS,EAAC,0BAA0B;QAAAC,QAAA,eACvCxD,OAAA;UAAKuD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCxD,OAAA;YAAIuD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAE/DzD,QAAQ,CAACE,KAAK,CAAC6D,MAAM,KAAK,CAAC,gBAC1BlE,OAAA;YAAKuD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCxD,OAAA,CAACF,QAAQ;cAACyD,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrE5D,OAAA;cAAIuD,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5D,OAAA;cAAGuD,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAC;YAE7C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,gBAEN5D,OAAA;YAAKuD,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBrD,QAAQ,CAACE,KAAK,CAACoC,GAAG,CAAC,CAACC,IAAI,EAAEyB,KAAK,KAAK;cACnC,MAAMN,QAAQ,GAAG1C,SAAS,CAACS,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACtB,IAAI,KAAKmC,IAAI,CAACnC,IAAI,CAAC;cAC5D,MAAMuD,IAAI,GAAG,CAAAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAExC,IAAI,KAAIvB,QAAQ;cAEvC,oBACEE,OAAA;gBAAmBuD,SAAS,EAAC,UAAU;gBAAAC,QAAA,GACpCW,KAAK,GAAG,CAAC,iBACRnE,OAAA;kBAAKuD,SAAS,EAAC;gBAAyE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC/F,eAED5D,OAAA,CAAChB,IAAI;kBACHuE,SAAS,EAAE,iCACT,CAAAtC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEX,EAAE,MAAKoC,IAAI,CAACpC,EAAE,GAAG,qBAAqB,GAAG,EAAE,EACxD;kBACHyD,OAAO,EAAEA,CAAA,KAAM7C,eAAe,CAACwB,IAAI,CAAE;kBAAAc,QAAA,gBAErCxD,OAAA,CAACb,UAAU;oBAACoE,SAAS,EAAC,MAAM;oBAAAC,QAAA,eAC1BxD,OAAA;sBAAKuD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,gBAChDxD,OAAA;wBAAKuD,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,gBAC1CxD,OAAA;0BAAKuD,SAAS,EAAE,kBAAkB,CAAAM,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEvC,KAAK,KAAI,aAAa,aAAc;0BAAAkC,QAAA,eAC9ExD,OAAA,CAAC8D,IAAI;4BAACP,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzB,CAAC,eACN5D,OAAA;0BAAAwD,QAAA,gBACExD,OAAA,CAACZ,SAAS;4BAACmE,SAAS,EAAC,WAAW;4BAAAC,QAAA,EAAEd,IAAI,CAAClC;0BAAK;4BAAAiD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY,CAAC,eACzD5D,OAAA,CAACd,eAAe;4BAAAsE,QAAA,EAAEK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEzC;0BAAW;4BAAAqC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAkB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvD,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN5D,OAAA,CAACjB,MAAM;wBACLkF,OAAO,EAAC,OAAO;wBACfD,IAAI,EAAC,IAAI;wBACTD,OAAO,EAAGK,CAAC,IAAK;0BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;0BACnB1B,UAAU,CAACD,IAAI,CAACpC,EAAE,CAAC;wBACrB,CAAE;wBAAAkD,QAAA,eAEFxD,OAAA,CAACL,MAAM;0BAAC4D,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eAEb5D,OAAA,CAACf,WAAW;oBAAAuE,QAAA,eACVxD,OAAA;sBAAKuD,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,EAC3Cc,MAAM,CAACC,OAAO,CAAC7B,IAAI,CAACjC,MAAM,CAAC,CAACgC,GAAG,CAAC,CAAC,CAAC+B,GAAG,EAAEC,KAAK,CAAC,kBAC5CzE,OAAA;wBAAeuD,SAAS,EAAC,sBAAsB;wBAAAC,QAAA,gBAC7CxD,OAAA;0BAAMuD,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAEgB,GAAG,EAAC,GAAC;wBAAA;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC1C5D,OAAA;0BAAAwD,QAAA,EAAOkB,MAAM,CAACD,KAAK;wBAAC;0BAAAhB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA,GAFpBY,GAAG;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGR,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,EAENO,KAAK,GAAGhE,QAAQ,CAACE,KAAK,CAAC6D,MAAM,GAAG,CAAC,iBAChClE,OAAA;kBAAKuD,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,eACvCxD,OAAA;oBAAKuD,SAAS,EAAC;kBAAqB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CACN;cAAA,GAnDOlB,IAAI,CAACpC,EAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoDZ,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3C,YAAY,iBACXjB,OAAA;MAAKuD,SAAS,EAAC,2EAA2E;MAAAC,QAAA,gBACxFxD,OAAA;QAAKuD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCxD,OAAA;UAAIuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE5D,OAAA;UAAGuD,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAEvC,YAAY,CAACT;QAAK;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCxD,OAAA;UAAKuD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBc,MAAM,CAACC,OAAO,CAACtD,YAAY,CAACR,MAAM,CAAC,CAACgC,GAAG,CAAC,CAAC,CAAC+B,GAAG,EAAEC,KAAK,CAAC,kBACpDzE,OAAA;YAAAwD,QAAA,gBACExD,OAAA;cAAOuD,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAC9DgB,GAAG,CAACG,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAACC,IAAI,CAAC;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACR5D,OAAA,CAACX,KAAK;cACJoF,KAAK,EAAEA,KAAK,IAAI,EAAG;cACnBI,QAAQ,EAAGT,CAAC,IAAK9B,gBAAgB,CAACrB,YAAY,CAACX,EAAE,EAAE;gBAAE,CAACkE,GAAG,GAAGJ,CAAC,CAACU,MAAM,CAACL;cAAM,CAAC,CAAE;cAC9ElB,SAAS,EAAC,MAAM;cAChB7C,WAAW,EAAE,SAAS8D,GAAG;YAAM;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA,GATMY,GAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUR,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAxRID,qBAAqB;AAAA8E,EAAA,GAArB9E,qBAAqB;AA0R3B,eAAeA,qBAAqB;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}