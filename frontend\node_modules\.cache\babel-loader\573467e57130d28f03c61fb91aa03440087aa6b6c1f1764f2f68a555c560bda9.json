{"ast": null, "code": "import { tpmt } from \"./math.js\";\nvar tau = 2 * Math.PI,\n  amplitude = 1,\n  period = 0.3;\nexport var elasticIn = function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n  function elasticIn(t) {\n    return a * tpmt(- --t) * Math.sin((s - t) / p);\n  }\n  elasticIn.amplitude = function (a) {\n    return custom(a, p * tau);\n  };\n  elasticIn.period = function (p) {\n    return custom(a, p);\n  };\n  return elasticIn;\n}(amplitude, period);\nexport var elasticOut = function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n  function elasticOut(t) {\n    return 1 - a * tpmt(t = +t) * Math.sin((t + s) / p);\n  }\n  elasticOut.amplitude = function (a) {\n    return custom(a, p * tau);\n  };\n  elasticOut.period = function (p) {\n    return custom(a, p);\n  };\n  return elasticOut;\n}(amplitude, period);\nexport var elasticInOut = function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n  function elasticInOut(t) {\n    return ((t = t * 2 - 1) < 0 ? a * tpmt(-t) * Math.sin((s - t) / p) : 2 - a * tpmt(t) * Math.sin((s + t) / p)) / 2;\n  }\n  elasticInOut.amplitude = function (a) {\n    return custom(a, p * tau);\n  };\n  elasticInOut.period = function (p) {\n    return custom(a, p);\n  };\n  return elasticInOut;\n}(amplitude, period);", "map": {"version": 3, "names": ["tpmt", "tau", "Math", "PI", "amplitude", "period", "elasticIn", "custom", "a", "p", "s", "asin", "max", "t", "sin", "elasticOut", "elasticInOut"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-ease/src/elastic.js"], "sourcesContent": ["import {tpmt} from \"./math.js\";\n\nvar tau = 2 * Math.PI,\n    amplitude = 1,\n    period = 0.3;\n\nexport var elasticIn = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticIn(t) {\n    return a * tpmt(-(--t)) * Math.sin((s - t) / p);\n  }\n\n  elasticIn.amplitude = function(a) { return custom(a, p * tau); };\n  elasticIn.period = function(p) { return custom(a, p); };\n\n  return elasticIn;\n})(amplitude, period);\n\nexport var elasticOut = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticOut(t) {\n    return 1 - a * tpmt(t = +t) * Math.sin((t + s) / p);\n  }\n\n  elasticOut.amplitude = function(a) { return custom(a, p * tau); };\n  elasticOut.period = function(p) { return custom(a, p); };\n\n  return elasticOut;\n})(amplitude, period);\n\nexport var elasticInOut = (function custom(a, p) {\n  var s = Math.asin(1 / (a = Math.max(1, a))) * (p /= tau);\n\n  function elasticInOut(t) {\n    return ((t = t * 2 - 1) < 0\n        ? a * tpmt(-t) * Math.sin((s - t) / p)\n        : 2 - a * tpmt(t) * Math.sin((s + t) / p)) / 2;\n  }\n\n  elasticInOut.amplitude = function(a) { return custom(a, p * tau); };\n  elasticInOut.period = function(p) { return custom(a, p); };\n\n  return elasticInOut;\n})(amplitude, period);\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,WAAW;AAE9B,IAAIC,GAAG,GAAG,CAAC,GAAGC,IAAI,CAACC,EAAE;EACjBC,SAAS,GAAG,CAAC;EACbC,MAAM,GAAG,GAAG;AAEhB,OAAO,IAAIC,SAAS,GAAI,SAASC,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC5C,IAAIC,CAAC,GAAGR,IAAI,CAACS,IAAI,CAAC,CAAC,IAAIH,CAAC,GAAGN,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,IAAIR,GAAG,CAAC;EAExD,SAASK,SAASA,CAACO,CAAC,EAAE;IACpB,OAAOL,CAAC,GAAGR,IAAI,CAAC,CAAE,GAAEa,CAAE,CAAC,GAAGX,IAAI,CAACY,GAAG,CAAC,CAACJ,CAAC,GAAGG,CAAC,IAAIJ,CAAC,CAAC;EACjD;EAEAH,SAAS,CAACF,SAAS,GAAG,UAASI,CAAC,EAAE;IAAE,OAAOD,MAAM,CAACC,CAAC,EAAEC,CAAC,GAAGR,GAAG,CAAC;EAAE,CAAC;EAChEK,SAAS,CAACD,MAAM,GAAG,UAASI,CAAC,EAAE;IAAE,OAAOF,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC;EAAE,CAAC;EAEvD,OAAOH,SAAS;AAClB,CAAC,CAAEF,SAAS,EAAEC,MAAM,CAAC;AAErB,OAAO,IAAIU,UAAU,GAAI,SAASR,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7C,IAAIC,CAAC,GAAGR,IAAI,CAACS,IAAI,CAAC,CAAC,IAAIH,CAAC,GAAGN,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,IAAIR,GAAG,CAAC;EAExD,SAASc,UAAUA,CAACF,CAAC,EAAE;IACrB,OAAO,CAAC,GAAGL,CAAC,GAAGR,IAAI,CAACa,CAAC,GAAG,CAACA,CAAC,CAAC,GAAGX,IAAI,CAACY,GAAG,CAAC,CAACD,CAAC,GAAGH,CAAC,IAAID,CAAC,CAAC;EACrD;EAEAM,UAAU,CAACX,SAAS,GAAG,UAASI,CAAC,EAAE;IAAE,OAAOD,MAAM,CAACC,CAAC,EAAEC,CAAC,GAAGR,GAAG,CAAC;EAAE,CAAC;EACjEc,UAAU,CAACV,MAAM,GAAG,UAASI,CAAC,EAAE;IAAE,OAAOF,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC;EAAE,CAAC;EAExD,OAAOM,UAAU;AACnB,CAAC,CAAEX,SAAS,EAAEC,MAAM,CAAC;AAErB,OAAO,IAAIW,YAAY,GAAI,SAAST,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC/C,IAAIC,CAAC,GAAGR,IAAI,CAACS,IAAI,CAAC,CAAC,IAAIH,CAAC,GAAGN,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,CAAC,IAAIC,CAAC,IAAIR,GAAG,CAAC;EAExD,SAASe,YAAYA,CAACH,CAAC,EAAE;IACvB,OAAO,CAAC,CAACA,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GACrBL,CAAC,GAAGR,IAAI,CAAC,CAACa,CAAC,CAAC,GAAGX,IAAI,CAACY,GAAG,CAAC,CAACJ,CAAC,GAAGG,CAAC,IAAIJ,CAAC,CAAC,GACpC,CAAC,GAAGD,CAAC,GAAGR,IAAI,CAACa,CAAC,CAAC,GAAGX,IAAI,CAACY,GAAG,CAAC,CAACJ,CAAC,GAAGG,CAAC,IAAIJ,CAAC,CAAC,IAAI,CAAC;EACpD;EAEAO,YAAY,CAACZ,SAAS,GAAG,UAASI,CAAC,EAAE;IAAE,OAAOD,MAAM,CAACC,CAAC,EAAEC,CAAC,GAAGR,GAAG,CAAC;EAAE,CAAC;EACnEe,YAAY,CAACX,MAAM,GAAG,UAASI,CAAC,EAAE;IAAE,OAAOF,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC;EAAE,CAAC;EAE1D,OAAOO,YAAY;AACrB,CAAC,CAAEZ,SAAS,EAAEC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}