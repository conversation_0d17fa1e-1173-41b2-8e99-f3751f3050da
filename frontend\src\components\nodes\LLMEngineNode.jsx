import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent } from '../ui/card';
import { Brain } from 'lucide-react';

const LLMEngineNode = ({ data, selected }) => {
  return (
    <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-yellow-500 text-white rounded-lg">
            <Brain className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-foreground text-sm">
              {data.label}
            </h3>
            <p className="text-xs text-muted-foreground mt-1">
              AI model for generating responses
            </p>
          </div>
        </div>
        
        <div className="mt-3 space-y-2">
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Model:</span>
            <span className="text-foreground">
              {data.config?.model || 'gpt-3.5-turbo'}
            </span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Temperature:</span>
            <span className="text-foreground">
              {data.config?.temperature || 0.7}
            </span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Max Tokens:</span>
            <span className="text-foreground">
              {data.config?.maxTokens || 1000}
            </span>
          </div>
          {data.config?.enableWebSearch && (
            <div className="text-xs text-green-600">
              ✓ Web Search Enabled
            </div>
          )}
        </div>
      </CardContent>
      
      {/* Input handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="query"
        style={{ top: '30%' }}
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />
      <Handle
        type="target"
        position={Position.Left}
        id="context"
        style={{ top: '70%' }}
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />
      
      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />
    </Card>
  );
};

export default LLMEngineNode;
