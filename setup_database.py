#!/usr/bin/env python3
"""
Database setup script for GenAI Workflow Builder
This script sets up PostgreSQL database and creates necessary tables
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_database():
    """Create the PostgreSQL database if it doesn't exist"""
    
    # Database connection parameters
    host = "localhost"
    port = "5432"
    user = "postgres"
    password = "postgres"  # Change this to your PostgreSQL password
    database_name = "genai_workflow"
    
    print("🔧 Setting up PostgreSQL database...")
    
    try:
        # Connect to PostgreSQL server (not to a specific database)
        print(f"📡 Connecting to PostgreSQL server at {host}:{port}")
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database="postgres"  # Connect to default postgres database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if database exists
        cursor.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s", (database_name,))
        exists = cursor.fetchone()
        
        if not exists:
            print(f"🗄️  Creating database '{database_name}'...")
            cursor.execute(f'CREATE DATABASE "{database_name}"')
            print(f"✅ Database '{database_name}' created successfully!")
        else:
            print(f"✅ Database '{database_name}' already exists!")
        
        cursor.close()
        conn.close()
        
        # Test connection to the new database
        print(f"🔍 Testing connection to '{database_name}'...")
        test_conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database_name
        )
        test_conn.close()
        print("✅ Database connection test successful!")
        
        # Update .env file
        env_path = os.path.join("backend", ".env")
        database_url = f"postgresql://{user}:{password}@{host}:{port}/{database_name}"
        
        print(f"📝 Updating .env file with database URL...")
        
        # Read current .env content
        env_content = []
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                env_content = f.readlines()
        
        # Update DATABASE_URL
        updated = False
        for i, line in enumerate(env_content):
            if line.startswith('DATABASE_URL='):
                env_content[i] = f'DATABASE_URL={database_url}\n'
                updated = True
                break
        
        if not updated:
            env_content.append(f'DATABASE_URL={database_url}\n')
        
        # Write back to .env
        with open(env_path, 'w') as f:
            f.writelines(env_content)
        
        print("✅ .env file updated successfully!")
        print(f"🔗 Database URL: {database_url}")
        
        return True
        
    except psycopg2.Error as e:
        print(f"❌ PostgreSQL Error: {e}")
        print("\n💡 Troubleshooting tips:")
        print("1. Make sure PostgreSQL is installed and running")
        print("2. Check if the username and password are correct")
        print("3. Verify PostgreSQL is listening on localhost:5432")
        print("4. Update the password in this script if needed")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    print("🚀 GenAI Workflow Builder - Database Setup")
    print("=" * 50)
    
    if create_database():
        print("\n🎉 Database setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Start the backend server: cd backend && python -m uvicorn app.main:app --reload")
        print("2. The FastAPI app will automatically create tables")
        print("3. Start the frontend: cd frontend && npm start")
    else:
        print("\n❌ Database setup failed!")
        print("Please check the error messages above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
