{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst RemoveFormatting = createLucideIcon(\"RemoveFormatting\", [[\"path\", {\n  d: \"M4 7V4h16v3\",\n  key: \"9msm58\"\n}], [\"path\", {\n  d: \"M5 20h6\",\n  key: \"1h6pxn\"\n}], [\"path\", {\n  d: \"M13 4 8 20\",\n  key: \"kqq6aj\"\n}], [\"path\", {\n  d: \"m15 15 5 5\",\n  key: \"me55sn\"\n}], [\"path\", {\n  d: \"m20 15-5 5\",\n  key: \"11p7ol\"\n}]]);\nexport { RemoveFormatting as default };", "map": {"version": 3, "names": ["RemoveFormatting", "createLucideIcon", "d", "key"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\remove-formatting.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RemoveFormatting\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCA3VjRoMTZ2MyIgLz4KICA8cGF0aCBkPSJNNSAyMGg2IiAvPgogIDxwYXRoIGQ9Ik0xMyA0IDggMjAiIC8+CiAgPHBhdGggZD0ibTE1IDE1IDUgNSIgLz4KICA8cGF0aCBkPSJtMjAgMTUtNSA1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/remove-formatting\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RemoveFormatting = createLucideIcon('RemoveFormatting', [\n  ['path', { d: 'M4 7V4h16v3', key: '9msm58' }],\n  ['path', { d: 'M5 20h6', key: '1h6pxn' }],\n  ['path', { d: 'M13 4 8 20', key: 'kqq6aj' }],\n  ['path', { d: 'm15 15 5 5', key: 'me55sn' }],\n  ['path', { d: 'm20 15-5 5', key: '11p7ol' }],\n]);\n\nexport default RemoveFormatting;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,gBAAA,GAAmBC,gBAAA,CAAiB,kBAAoB,GAC5D,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}