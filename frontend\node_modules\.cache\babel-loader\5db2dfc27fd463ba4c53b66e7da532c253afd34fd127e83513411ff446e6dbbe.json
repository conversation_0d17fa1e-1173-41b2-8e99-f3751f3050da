{"ast": null, "code": "function lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\nexport default function () {\n  return this.each(lower);\n}", "map": {"version": 3, "names": ["lower", "previousSibling", "parentNode", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "each"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/selection/lower.js"], "sourcesContent": ["function lower() {\n  if (this.previousSibling) this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\n\nexport default function() {\n  return this.each(lower);\n}\n"], "mappings": "AAAA,SAASA,KAAKA,CAAA,EAAG;EACf,IAAI,IAAI,CAACC,eAAe,EAAE,IAAI,CAACC,UAAU,CAACC,YAAY,CAAC,IAAI,EAAE,IAAI,CAACD,UAAU,CAACE,UAAU,CAAC;AAC1F;AAEA,eAAe,YAAW;EACxB,OAAO,IAAI,CAACC,IAAI,CAACL,KAAK,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}