version: '3.8'

services:
  # PostgreSQL Database Service
  postgres:
    image: postgres:15-alpine
    container_name: genai_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: genai_workflow
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - genai_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d genai_workflow"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: genai_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - genai_network
    depends_on:
      postgres:
        condition: service_healthy

  # FastAPI Backend Service (optional - for full containerization)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: genai_backend
    restart: unless-stopped
    environment:
      DATABASE_URL: ***********************************************/genai_workflow
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      CHROMA_PERSIST_DIRECTORY: /app/chroma_db
      UPLOAD_DIRECTORY: /app/uploads
    ports:
      - "8000:8000"
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/chroma_db:/app/chroma_db
    networks:
      - genai_network
    depends_on:
      postgres:
        condition: service_healthy
    profiles:
      - full-stack  # Only start with --profile full-stack

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  genai_network:
    driver: bridge
