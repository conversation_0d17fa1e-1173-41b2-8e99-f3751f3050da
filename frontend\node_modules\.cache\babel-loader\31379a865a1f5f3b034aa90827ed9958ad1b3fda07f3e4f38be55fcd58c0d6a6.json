{"ast": null, "code": "export function cubicIn(t) {\n  return t * t * t;\n}\nexport function cubicOut(t) {\n  return --t * t * t + 1;\n}\nexport function cubicInOut(t) {\n  return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2;\n}", "map": {"version": 3, "names": ["cubicIn", "t", "cubicOut", "cubicInOut"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-ease/src/cubic.js"], "sourcesContent": ["export function cubicIn(t) {\n  return t * t * t;\n}\n\nexport function cubicOut(t) {\n  return --t * t * t + 1;\n}\n\nexport function cubicInOut(t) {\n  return ((t *= 2) <= 1 ? t * t * t : (t -= 2) * t * t + 2) / 2;\n}\n"], "mappings": "AAAA,OAAO,SAASA,OAAOA,CAACC,CAAC,EAAE;EACzB,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC;AAClB;AAEA,OAAO,SAASC,QAAQA,CAACD,CAAC,EAAE;EAC1B,OAAO,EAAEA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAAC;AACxB;AAEA,OAAO,SAASE,UAAUA,CAACF,CAAC,EAAE;EAC5B,OAAO,CAAC,CAACA,CAAC,IAAI,CAAC,KAAK,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGA,CAAC,GAAG,CAAC,IAAI,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}