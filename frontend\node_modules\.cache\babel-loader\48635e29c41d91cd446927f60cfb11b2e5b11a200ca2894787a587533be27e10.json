{"ast": null, "code": "/**\n * React Router v6.8.1\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { invariant, joinPaths, matchPath, UNSAFE_getPathContributingMatches, warning, resolveTo, parsePath, matchRoutes, Action, isRouteErrorResponse, createMemoryHistory, stripBasename, AbortedDeferredError, createRouter } from '@remix-run/router';\nexport { AbortedDeferredError, Action as NavigationType, createPath, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, resolvePath } from '@remix-run/router';\nimport * as React from 'react';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\n\nfunction isPolyfill(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\nconst is = typeof Object.is === \"function\" ? Object.is : isPolyfill; // Intentionally not using named imports because Rollup uses dynamic\n// dispatch for CommonJS interop named imports.\n\nconst {\n  useState,\n  useEffect,\n  useLayoutEffect,\n  useDebugValue\n} = React;\nlet didWarnOld18Alpha = false;\nlet didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore$2(subscribe, getSnapshot,\n// Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!didWarnOld18Alpha) {\n      if (\"startTransition\" in React) {\n        didWarnOld18Alpha = true;\n        console.error(\"You are using an outdated, pre-release alpha of React 18 that \" + \"does not support useSyncExternalStore. The \" + \"use-sync-external-store shim will not work correctly. Upgrade \" + \"to a newer pre-release.\");\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n  const value = getSnapshot();\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!didWarnUncachedGetSnapshot) {\n      const cachedValue = getSnapshot();\n      if (!is(value, cachedValue)) {\n        console.error(\"The result of getSnapshot should be cached to avoid an infinite loop\");\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n  const [{\n    inst\n  }, forceUpdate] = useState({\n    inst: {\n      value,\n      getSnapshot\n    }\n  }); // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n  useLayoutEffect(() => {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst\n      });\n    } // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe, value, getSnapshot]);\n  useEffect(() => {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst\n      });\n    }\n    const handleStoreChange = () => {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n    return subscribe(handleStoreChange); // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\nfunction checkIfSnapshotChanged(inst) {\n  const latestGetSnapshot = inst.getSnapshot;\n  const prevValue = inst.value;\n  try {\n    const nextValue = latestGetSnapshot();\n    return !is(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\n/**\n * Inlined into the react-router repo since use-sync-external-store does not\n * provide a UMD-compatible package, so we need this to be able to distribute\n * UMD react-router bundles\n */\nconst canUseDOM = !!(typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\");\nconst isServerEnvironment = !canUseDOM;\nconst shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore$2;\nconst useSyncExternalStore = \"useSyncExternalStore\" in React ? (module => module.useSyncExternalStore)(React) : shim;\nconst DataRouterContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterContext.displayName = \"DataRouter\";\n}\nconst DataRouterStateContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\nconst AwaitContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  AwaitContext.displayName = \"Await\";\n}\nconst NavigationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  NavigationContext.displayName = \"Navigation\";\n}\nconst LocationContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  LocationContext.displayName = \"Location\";\n}\nconst RouteContext = /*#__PURE__*/React.createContext({\n  outlet: null,\n  matches: []\n});\nif (process.env.NODE_ENV !== \"production\") {\n  RouteContext.displayName = \"Route\";\n}\nconst RouteErrorContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== \"production\") {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\n\nfunction useHref(to, _temp) {\n  let {\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useHref() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    hash,\n    pathname,\n    search\n  } = useResolvedPath(to, {\n    relative\n  });\n  let joinedPathname = pathname; // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n\n  if (basename !== \"/\") {\n    joinedPathname = pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n  return navigator.createHref({\n    pathname: joinedPathname,\n    search,\n    hash\n  });\n}\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\n\nfunction useInRouterContext() {\n  return React.useContext(LocationContext) != null;\n}\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\n\nfunction useLocation() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useLocation() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  return React.useContext(LocationContext).location;\n}\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\n\nfunction useNavigationType() {\n  return React.useContext(LocationContext).navigationType;\n}\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\n\nfunction useMatch(pattern) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useMatch() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    pathname\n  } = useLocation();\n  return React.useMemo(() => matchPath(pattern, pathname), [pathname, pattern]);\n}\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nfunction useNavigate() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useNavigate() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getPathContributingMatches(matches).map(match => match.pathnameBase));\n  let activeRef = React.useRef(false);\n  React.useEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    process.env.NODE_ENV !== \"production\" ? warning(activeRef.current, \"You should call navigate() in a React.useEffect(), not when \" + \"your component is first rendered.\") : void 0;\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      navigator.go(to);\n      return;\n    }\n    let path = resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, options.relative === \"path\"); // If we're operating within a basename, prepend it to the pathname prior\n    // to handing off to history.  If this is a root navigation, then we\n    // navigate to the raw basename which allows the basename to have full\n    // control over the presence of a trailing slash on root links\n\n    if (basename !== \"/\") {\n      path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n    }\n    (!!options.replace ? navigator.replace : navigator.push)(path, options.state, options);\n  }, [basename, navigator, routePathnamesJson, locationPathname]);\n  return navigate;\n}\nconst OutletContext = /*#__PURE__*/React.createContext(null);\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\n\nfunction useOutletContext() {\n  return React.useContext(OutletContext);\n}\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\n\nfunction useOutlet(context) {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return /*#__PURE__*/React.createElement(OutletContext.Provider, {\n      value: context\n    }, outlet);\n  }\n  return outlet;\n}\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\n\nfunction useParams() {\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? routeMatch.params : {};\n}\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\n\nfunction useResolvedPath(to, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getPathContributingMatches(matches).map(match => match.pathnameBase));\n  return React.useMemo(() => resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, relative === \"path\"), [to, routePathnamesJson, locationPathname, relative]);\n}\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\n\nfunction useRoutes(routes, locationArg) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useRoutes() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    navigator\n  } = React.useContext(NavigationContext);\n  let dataRouterStateContext = React.useContext(DataRouterStateContext);\n  let {\n    matches: parentMatches\n  } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n  if (process.env.NODE_ENV !== \"production\") {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = parentRoute && parentRoute.path || \"\";\n    warningOnce(parentPathname, !parentRoute || parentPath.endsWith(\"*\"), \"You rendered descendant <Routes> (or called `useRoutes()`) at \" + (\"\\\"\" + parentPathname + \"\\\" (under <Route path=\\\"\" + parentPath + \"\\\">) but the \") + \"parent route path has no trailing \\\"*\\\". This means if you navigate \" + \"deeper, the parent won't match anymore and therefore the child \" + \"routes will never render.\\n\\n\" + (\"Please change the parent <Route path=\\\"\" + parentPath + \"\\\"> to <Route \") + (\"path=\\\"\" + (parentPath === \"/\" ? \"*\" : parentPath + \"/*\") + \"\\\">.\"));\n  }\n  let locationFromContext = useLocation();\n  let location;\n  if (locationArg) {\n    var _parsedLocationArg$pa;\n    let parsedLocationArg = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n    !(parentPathnameBase === \"/\" || ((_parsedLocationArg$pa = parsedLocationArg.pathname) == null ? void 0 : _parsedLocationArg$pa.startsWith(parentPathnameBase))) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, \" + \"the location pathname must begin with the portion of the URL pathname that was \" + (\"matched by all parent routes. The current pathname base is \\\"\" + parentPathnameBase + \"\\\" \") + (\"but pathname \\\"\" + parsedLocationArg.pathname + \"\\\" was given in the `location` prop.\")) : invariant(false) : void 0;\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n  let pathname = location.pathname || \"/\";\n  let remainingPathname = parentPathnameBase === \"/\" ? pathname : pathname.slice(parentPathnameBase.length) || \"/\";\n  let matches = matchRoutes(routes, {\n    pathname: remainingPathname\n  });\n  if (process.env.NODE_ENV !== \"production\") {\n    process.env.NODE_ENV !== \"production\" ? warning(parentRoute || matches != null, \"No routes matched location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \") : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(matches == null || matches[matches.length - 1].route.element !== undefined, \"Matched leaf route at location \\\"\" + location.pathname + location.search + location.hash + \"\\\" does not have an element. \" + \"This means it will render an <Outlet /> with a null value by default resulting in an \\\"empty\\\" page.\") : void 0;\n  }\n  let renderedMatches = _renderMatches(matches && matches.map(match => Object.assign({}, match, {\n    params: Object.assign({}, parentParams, match.params),\n    pathname: joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathname).pathname : match.pathname]),\n    pathnameBase: match.pathnameBase === \"/\" ? parentPathnameBase : joinPaths([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathnameBase).pathname : match.pathnameBase])\n  })), parentMatches, dataRouterStateContext || undefined); // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n\n  if (locationArg && renderedMatches) {\n    return /*#__PURE__*/React.createElement(LocationContext.Provider, {\n      value: {\n        location: _extends({\n          pathname: \"/\",\n          search: \"\",\n          hash: \"\",\n          state: null,\n          key: \"default\"\n        }, location),\n        navigationType: Action.Pop\n      }\n    }, renderedMatches);\n  }\n  return renderedMatches;\n}\nfunction DefaultErrorElement() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error) ? error.status + \" \" + error.statusText : error instanceof Error ? error.message : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = {\n    padding: \"0.5rem\",\n    backgroundColor: lightgrey\n  };\n  let codeStyles = {\n    padding: \"2px 4px\",\n    backgroundColor: lightgrey\n  };\n  let devInfo = null;\n  if (process.env.NODE_ENV !== \"production\") {\n    devInfo = /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"p\", null, \"\\uD83D\\uDCBF Hey developer \\uD83D\\uDC4B\"), /*#__PURE__*/React.createElement(\"p\", null, \"You can provide a way better UX than this when your app throws errors by providing your own\\xA0\", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"errorElement\"), \" props on\\xA0\", /*#__PURE__*/React.createElement(\"code\", {\n      style: codeStyles\n    }, \"<Route>\")));\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"h2\", null, \"Unexpected Application Error!\"), /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      fontStyle: \"italic\"\n    }\n  }, message), stack ? /*#__PURE__*/React.createElement(\"pre\", {\n    style: preStyles\n  }, stack) : null, devInfo);\n}\nclass RenderErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      location: props.location,\n      error: props.error\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location) {\n      return {\n        error: props.error,\n        location: props.location\n      };\n    } // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n\n    return {\n      error: props.error || state.error,\n      location: state.location\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"React Router caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    return this.state.error ? /*#__PURE__*/React.createElement(RouteContext.Provider, {\n      value: this.props.routeContext\n    }, /*#__PURE__*/React.createElement(RouteErrorContext.Provider, {\n      value: this.state.error,\n      children: this.props.component\n    })) : this.props.children;\n  }\n}\nfunction RenderedRoute(_ref) {\n  let {\n    routeContext,\n    match,\n    children\n  } = _ref;\n  let dataRouterContext = React.useContext(DataRouterContext); // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n\n  if (dataRouterContext && dataRouterContext.static && dataRouterContext.staticContext && match.route.errorElement) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n  return /*#__PURE__*/React.createElement(RouteContext.Provider, {\n    value: routeContext\n  }, children);\n}\nfunction _renderMatches(matches, parentMatches, dataRouterState) {\n  if (parentMatches === void 0) {\n    parentMatches = [];\n  }\n  if (matches == null) {\n    if (dataRouterState != null && dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches;\n    } else {\n      return null;\n    }\n  }\n  let renderedMatches = matches; // If we have data errors, trim matches to the highest error boundary\n\n  let errors = dataRouterState == null ? void 0 : dataRouterState.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(m => m.route.id && (errors == null ? void 0 : errors[m.route.id]));\n    !(errorIndex >= 0) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Could not find a matching route for the current errors: \" + errors) : invariant(false) : void 0;\n    renderedMatches = renderedMatches.slice(0, Math.min(renderedMatches.length, errorIndex + 1));\n  }\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    let error = match.route.id ? errors == null ? void 0 : errors[match.route.id] : null; // Only data routers handle errors\n\n    let errorElement = dataRouterState ? match.route.errorElement || /*#__PURE__*/React.createElement(DefaultErrorElement, null) : null;\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => /*#__PURE__*/React.createElement(RenderedRoute, {\n      match: match,\n      routeContext: {\n        outlet,\n        matches\n      }\n    }, error ? errorElement : match.route.element !== undefined ? match.route.element : outlet); // Only wrap in an error boundary within data router usages when we have an\n    // errorElement on this route.  Otherwise let it bubble up to an ancestor\n    // errorElement\n\n    return dataRouterState && (match.route.errorElement || index === 0) ? /*#__PURE__*/React.createElement(RenderErrorBoundary, {\n      location: dataRouterState.location,\n      component: errorElement,\n      error: error,\n      children: getChildren(),\n      routeContext: {\n        outlet: null,\n        matches\n      }\n    }) : getChildren();\n  }, null);\n}\nvar DataRouterHook;\n(function (DataRouterHook) {\n  DataRouterHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterHook[\"UseRevalidator\"] = \"useRevalidator\";\n})(DataRouterHook || (DataRouterHook = {}));\nvar DataRouterStateHook;\n(function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseLoaderData\"] = \"useLoaderData\";\n  DataRouterStateHook[\"UseActionData\"] = \"useActionData\";\n  DataRouterStateHook[\"UseRouteError\"] = \"useRouteError\";\n  DataRouterStateHook[\"UseNavigation\"] = \"useNavigation\";\n  DataRouterStateHook[\"UseRouteLoaderData\"] = \"useRouteLoaderData\";\n  DataRouterStateHook[\"UseMatches\"] = \"useMatches\";\n  DataRouterStateHook[\"UseRevalidator\"] = \"useRevalidator\";\n})(DataRouterStateHook || (DataRouterStateHook = {}));\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return state;\n}\nfunction useRouteContext(hookName) {\n  let route = React.useContext(RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return route;\n}\nfunction useCurrentRouteId(hookName) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  !thisRoute.route.id ? process.env.NODE_ENV !== \"production\" ? invariant(false, hookName + \" can only be used on routes that contain a unique \\\"id\\\"\") : invariant(false) : void 0;\n  return thisRoute.route.id;\n}\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\n\nfunction useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\n\nfunction useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return {\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation\n  };\n}\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\n\nfunction useMatches() {\n  let {\n    matches,\n    loaderData\n  } = useDataRouterState(DataRouterStateHook.UseMatches);\n  return React.useMemo(() => matches.map(match => {\n    let {\n      pathname,\n      params\n    } = match; // Note: This structure matches that created by createUseMatchesMatch\n    // in the @remix-run/router , so if you change this please also change\n    // that :)  Eventually we'll DRY this up\n\n    return {\n      id: match.route.id,\n      pathname,\n      params,\n      data: loaderData[match.route.id],\n      handle: match.route.handle\n    };\n  }), [matches, loaderData]);\n}\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\n\nfunction useLoaderData() {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\"You cannot `useLoaderData` in an errorElement (routeId: \" + routeId + \")\");\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n/**\n * Returns the loaderData for the given routeId\n */\n\nfunction useRouteLoaderData(routeId) {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n/**\n * Returns the action data for the nearest ancestor Route action\n */\n\nfunction useActionData() {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let route = React.useContext(RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useActionData must be used inside a RouteContext\") : invariant(false) : void 0;\n  return Object.values((state == null ? void 0 : state.actionData) || {})[0];\n}\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * errorElement to display a proper error message.\n */\n\nfunction useRouteError() {\n  var _state$errors;\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError); // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n\n  if (error) {\n    return error;\n  } // Otherwise look for errors from our data router state\n\n  return (_state$errors = state.errors) == null ? void 0 : _state$errors[routeId];\n}\n/**\n * Returns the happy-path data from the nearest ancestor <Await /> value\n */\n\nfunction useAsyncValue() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._data;\n}\n/**\n * Returns the error from the nearest ancestor <Await /> value\n */\n\nfunction useAsyncError() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._error;\n}\nlet blockerId = 0;\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\n\nfunction useBlocker(shouldBlock) {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let [blockerKey] = React.useState(() => String(++blockerId));\n  let blockerFunction = React.useCallback(args => {\n    return typeof shouldBlock === \"function\" ? !!shouldBlock(args) : !!shouldBlock;\n  }, [shouldBlock]);\n  let blocker = router.getBlocker(blockerKey, blockerFunction); // Cleanup on unmount\n\n  React.useEffect(() => () => router.deleteBlocker(blockerKey), [router, blockerKey]);\n  return blocker;\n}\nconst alreadyWarned = {};\nfunction warningOnce(key, cond, message) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    process.env.NODE_ENV !== \"production\" ? warning(false, message) : void 0;\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router\n  } = _ref;\n  // Sync router state to our component state to force re-renders\n  let state = useSyncExternalStore(router.subscribe, () => router.state,\n  // We have to provide this so React@18 doesn't complain during hydration,\n  // but we pass our serialized hydration data into the router so state here\n  // is already synced with what the server saw\n  () => router.state);\n  let navigator = React.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\"; // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(DataRouterContext.Provider, {\n    value: {\n      router,\n      navigator,\n      static: false,\n      // Do we need this?\n      basename\n    }\n  }, /*#__PURE__*/React.createElement(DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: router.basename,\n    location: router.state.location,\n    navigationType: router.state.historyAction,\n    navigator: navigator\n  }, router.state.initialized ? /*#__PURE__*/React.createElement(Routes, null) : fallbackElement))), null);\n}\n\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nfunction MemoryRouter(_ref2) {\n  let {\n    basename,\n    children,\n    initialEntries,\n    initialIndex\n  } = _ref2;\n  let historyRef = React.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nfunction Navigate(_ref3) {\n  let {\n    to,\n    replace,\n    state,\n    relative\n  } = _ref3;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false,\n  // TODO: This error is probably because they somehow have 2 versions of\n  // the router loaded. We can help them understand how to avoid that.\n  \"<Navigate> may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  process.env.NODE_ENV !== \"production\" ? warning(!React.useContext(NavigationContext).static, \"<Navigate> must not be used on the initial render in a <StaticRouter>. \" + \"This is a no-op, but you should modify your code so the <Navigate> is \" + \"only ever rendered in response to some user interaction or state change.\") : void 0;\n  let dataRouterState = React.useContext(DataRouterStateContext);\n  let navigate = useNavigate();\n  React.useEffect(() => {\n    // Avoid kicking off multiple navigations if we're in the middle of a\n    // data-router navigation, since components get re-rendered when we enter\n    // a submitting/loading state\n    if (dataRouterState && dataRouterState.navigation.state !== \"idle\") {\n      return;\n    }\n    navigate(to, {\n      replace,\n      state,\n      relative\n    });\n  });\n  return null;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nfunction Outlet(props) {\n  return useOutlet(props.context);\n}\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nfunction Route(_props) {\n  process.env.NODE_ENV !== \"production\" ? invariant(false, \"A <Route> is only ever to be used as the child of <Routes> element, \" + \"never rendered directly. Please wrap your <Route> in a <Routes>.\") : invariant(false);\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nfunction Router(_ref4) {\n  let {\n    basename: basenameProp = \"/\",\n    children = null,\n    location: locationProp,\n    navigationType = Action.Pop,\n    navigator,\n    static: staticProp = false\n  } = _ref4;\n  !!useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You cannot render a <Router> inside another <Router>.\" + \" You should never have more than one in your app.\") : invariant(false) : void 0; // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(() => ({\n    basename,\n    navigator,\n    static: staticProp\n  }), [basename, navigator, staticProp]);\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\"\n  } = locationProp;\n  let location = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n    if (trailingPathname == null) {\n      return null;\n    }\n    return {\n      pathname: trailingPathname,\n      search,\n      hash,\n      state,\n      key\n    };\n  }, [basename, pathname, search, hash, state, key]);\n  process.env.NODE_ENV !== \"production\" ? warning(location != null, \"<Router basename=\\\"\" + basename + \"\\\"> is not able to match the URL \" + (\"\\\"\" + pathname + search + hash + \"\\\" because it does not start with the \") + \"basename, so the <Router> won't render anything.\") : void 0;\n  if (location == null) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(NavigationContext.Provider, {\n    value: navigationContext\n  }, /*#__PURE__*/React.createElement(LocationContext.Provider, {\n    children: children,\n    value: {\n      location,\n      navigationType\n    }\n  }));\n}\n\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nfunction Routes(_ref5) {\n  let {\n    children,\n    location\n  } = _ref5;\n  let dataRouterContext = React.useContext(DataRouterContext); // When in a DataRouterContext _without_ children, we use the router routes\n  // directly.  If we have children, then we're in a descendant tree and we\n  // need to use child routes.\n\n  let routes = dataRouterContext && !children ? dataRouterContext.router.routes : createRoutesFromChildren(children);\n  return useRoutes(routes, location);\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nfunction Await(_ref6) {\n  let {\n    children,\n    errorElement,\n    resolve\n  } = _ref6;\n  return /*#__PURE__*/React.createElement(AwaitErrorBoundary, {\n    resolve: resolve,\n    errorElement: errorElement\n  }, /*#__PURE__*/React.createElement(ResolveAwait, null, children));\n}\nvar AwaitRenderStatus;\n(function (AwaitRenderStatus) {\n  AwaitRenderStatus[AwaitRenderStatus[\"pending\"] = 0] = \"pending\";\n  AwaitRenderStatus[AwaitRenderStatus[\"success\"] = 1] = \"success\";\n  AwaitRenderStatus[AwaitRenderStatus[\"error\"] = 2] = \"error\";\n})(AwaitRenderStatus || (AwaitRenderStatus = {}));\nconst neverSettledPromise = new Promise(() => {});\nclass AwaitErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"<Await> caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    let {\n      children,\n      errorElement,\n      resolve\n    } = this.props;\n    let promise = null;\n    let status = AwaitRenderStatus.pending;\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_data\", {\n        get: () => resolve\n      });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_error\", {\n        get: () => renderError\n      });\n    } else if (resolve._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status = promise._error !== undefined ? AwaitRenderStatus.error : promise._data !== undefined ? AwaitRenderStatus.success : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", {\n        get: () => true\n      });\n      promise = resolve.then(data => Object.defineProperty(resolve, \"_data\", {\n        get: () => data\n      }), error => Object.defineProperty(resolve, \"_error\", {\n        get: () => error\n      }));\n    }\n    if (status === AwaitRenderStatus.error && promise._error instanceof AbortedDeferredError) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: errorElement\n      });\n    }\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: children\n      });\n    } // Throw to the suspense boundary\n\n    throw promise;\n  }\n}\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on <Await>\n */\n\nfunction ResolveAwait(_ref7) {\n  let {\n    children\n  } = _ref7;\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, toRender);\n} ///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\n\nfunction createRoutesFromChildren(children, parentPath) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  let routes = [];\n  React.Children.forEach(children, (element, index) => {\n    if (! /*#__PURE__*/React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(routes, createRoutesFromChildren(element.props.children, parentPath));\n      return;\n    }\n    !(element.type === Route) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"[\" + (typeof element.type === \"string\" ? element.type : element.type.name) + \"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>\") : invariant(false) : void 0;\n    !(!element.props.index || !element.props.children) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"An index route cannot have child routes.\") : invariant(false) : void 0;\n    let treePath = [...parentPath, index];\n    let route = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      hasErrorBoundary: element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle\n    };\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children, treePath);\n    }\n    routes.push(route);\n  });\n  return routes;\n}\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\n\nfunction renderMatches(matches) {\n  return _renderMatches(matches);\n}\n/**\n * @private\n * Walk the route tree and add hasErrorBoundary if it's not provided, so that\n * users providing manual route arrays can just specify errorElement\n */\n\nfunction enhanceManualRouteObjects(routes) {\n  return routes.map(route => {\n    let routeClone = _extends({}, route);\n    if (routeClone.hasErrorBoundary == null) {\n      routeClone.hasErrorBoundary = routeClone.errorElement != null;\n    }\n    if (routeClone.children) {\n      routeClone.children = enhanceManualRouteObjects(routeClone.children);\n    }\n    return routeClone;\n  });\n}\nfunction createMemoryRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    history: createMemoryHistory({\n      initialEntries: opts == null ? void 0 : opts.initialEntries,\n      initialIndex: opts == null ? void 0 : opts.initialIndex\n    }),\n    hydrationData: opts == null ? void 0 : opts.hydrationData,\n    routes: enhanceManualRouteObjects(routes)\n  }).initialize();\n} ///////////////////////////////////////////////////////////////////////////////\n\nexport { Await, MemoryRouter, Navigate, Outlet, Route, Router, RouterProvider, Routes, DataRouterContext as UNSAFE_DataRouterContext, DataRouterStateContext as UNSAFE_DataRouterStateContext, LocationContext as UNSAFE_LocationContext, NavigationContext as UNSAFE_NavigationContext, RouteContext as UNSAFE_RouteContext, enhanceManualRouteObjects as UNSAFE_enhanceManualRouteObjects, createMemoryRouter, createRoutesFromChildren, createRoutesFromChildren as createRoutesFromElements, renderMatches, useBlocker as unstable_useBlocker, useActionData, useAsyncError, useAsyncValue, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes };", "map": {"version": 3, "names": ["isPolyfill", "x", "y", "is", "Object", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "React", "didWarnOld18Alpha", "didWarnUncachedGetSnapshot", "useSyncExternalStore$2", "useSyncExternalStore", "subscribe", "getSnapshot", "getServerSnapshot", "process", "env", "NODE_ENV", "console", "error", "value", "cachedValue", "inst", "forceUpdate", "checkIfSnapshotChanged", "handleStoreChange", "latestGetSnapshot", "prevValue", "nextValue", "useSyncExternalStore$1", "canUseDOM", "window", "document", "createElement", "isServerEnvironment", "shim", "module", "DataRouterContext", "createContext", "displayName", "DataRouterStateContext", "AwaitContext", "NavigationContext", "LocationContext", "RouteContext", "outlet", "matches", "RouteErrorContext", "useHref", "to", "_temp", "relative", "useInRouterContext", "invariant", "basename", "navigator", "useContext", "hash", "pathname", "search", "useResolvedPath", "joinedPathname", "joinPaths", "createHref", "useLocation", "location", "useNavigationType", "navigationType", "useMatch", "pattern", "useMemo", "matchPath", "useNavigate", "locationPathname", "routePathnamesJson", "JSON", "stringify", "UNSAFE_getPathContributingMatches", "map", "match", "pathnameBase", "activeRef", "useRef", "current", "navigate", "useCallback", "options", "warning", "go", "path", "resolveTo", "parse", "replace", "push", "state", "OutletContext", "useOutletContext", "useOutlet", "context", "Provider", "useParams", "routeMatch", "length", "params", "_temp2", "useRoutes", "routes", "locationArg", "dataRouterStateContext", "parentMatches", "parentParams", "parentPathname", "parentPathnameBase", "parentRoute", "route", "parentPath", "warningOnce", "endsWith", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "parsePath", "startsWith", "remainingPathname", "slice", "matchRoutes", "element", "undefined", "renderedMatches", "_renderMatches", "assign", "encodeLocation", "_extends", "key", "Action", "Pop", "DefaultErrorElement", "useRouteError", "message", "isRouteErrorResponse", "status", "statusText", "Error", "stack", "<PERSON><PERSON>rey", "preStyles", "padding", "backgroundColor", "codeStyles", "devInfo", "Fragment", "style", "fontStyle", "RenderErrorBoundary", "Component", "constructor", "props", "getDerivedStateFromError", "getDerivedStateFromProps", "componentDidCatch", "errorInfo", "render", "routeContext", "children", "component", "RenderedRoute", "_ref", "dataRouterContext", "static", "staticContext", "errorElement", "_deepestRenderedBoundaryId", "id", "dataRouterState", "errors", "errorIndex", "findIndex", "m", "Math", "min", "reduceRight", "index", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "useDataRouterState", "useRouteContext", "useCurrentRouteId", "thisRoute", "useNavigation", "UseNavigation", "navigation", "useRevalidator", "UseRevalidator", "revalidate", "router", "revalidation", "useMatches", "loaderData", "UseMatches", "data", "handle", "useLoaderData", "UseLoaderData", "routeId", "useRouteLoaderData", "UseRouteLoaderData", "useActionData", "UseActionData", "values", "actionData", "_state$errors", "UseRouteError", "useAsyncValue", "_data", "useAsyncError", "_error", "blockerId", "useBlocker", "shouldBlock", "UseBlocker", "blockerKey", "String", "blockerFunction", "args", "blocker", "get<PERSON><PERSON>er", "deleteBlocker", "alreadyWarned", "cond", "RouterProvider", "fallbackElement", "n", "opts", "preventScrollReset", "Router", "historyAction", "initialized", "Routes", "MemoryRouter", "_ref2", "initialEntries", "initialIndex", "historyRef", "createMemoryHistory", "v5Compat", "history", "setState", "action", "listen", "Navigate", "_ref3", "Outlet", "Route", "_props", "_ref4", "basenameProp", "locationProp", "staticProp", "navigationContext", "trailingPathname", "stripBasename", "_ref5", "createRoutesFromChildren", "Await", "_ref6", "resolve", "Await<PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>", "ResolveAwait", "AwaitRenderStatus", "neverSettledPromise", "Promise", "promise", "pending", "success", "defineProperty", "get", "renderError", "reject", "catch", "_tracked", "then", "Aborted<PERSON>eferredError", "_ref7", "to<PERSON><PERSON>", "Children", "for<PERSON>ach", "isValidElement", "type", "apply", "name", "treePath", "join", "caseSensitive", "loader", "hasErrorBou<PERSON>ry", "shouldRevalidate", "renderMatches", "enhanceManualRouteObjects", "routeClone", "createMemoryRouter", "createRouter", "hydrationData", "initialize"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\react-router\\lib\\use-sync-external-store-shim\\useSyncExternalStoreShimClient.ts", "D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\react-router\\lib\\use-sync-external-store-shim\\useSyncExternalStoreShimServer.ts", "D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\react-router\\lib\\use-sync-external-store-shim\\index.ts", "D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\react-router\\lib\\context.ts", "D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\react-router\\lib\\hooks.tsx", "D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\react-router\\lib\\components.tsx", "D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\react-router\\index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport * as React from \"react\";\n\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\nfunction isPolyfill(x: any, y: any) {\n  return (\n    (x === y && (x !== 0 || 1 / x === 1 / y)) || (x !== x && y !== y) // eslint-disable-line no-self-compare\n  );\n}\n\nconst is: (x: any, y: any) => boolean =\n  typeof Object.is === \"function\" ? Object.is : isPolyfill;\n\n// Intentionally not using named imports because Roll<PERSON> uses dynamic\n// dispatch for CommonJS interop named imports.\nconst { useState, useEffect, useLayoutEffect, useDebugValue } = React;\n\nlet didWarnOld18Alpha = false;\nlet didWarnUncachedGetSnapshot = false;\n\n// Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\nexport function useSyncExternalStore<T>(\n  subscribe: (fn: () => void) => () => void,\n  getSnapshot: () => T,\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  getServerSnapshot?: () => T\n): T {\n  if (__DEV__) {\n    if (!didWarnOld18Alpha) {\n      if (\"startTransition\" in React) {\n        didWarnOld18Alpha = true;\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that \" +\n            \"does not support useSyncExternalStore. The \" +\n            \"use-sync-external-store shim will not work correctly. Upgrade \" +\n            \"to a newer pre-release.\"\n        );\n      }\n    }\n  }\n\n  // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n  const value = getSnapshot();\n  if (__DEV__) {\n    if (!didWarnUncachedGetSnapshot) {\n      const cachedValue = getSnapshot();\n      if (!is(value, cachedValue)) {\n        console.error(\n          \"The result of getSnapshot should be cached to avoid an infinite loop\"\n        );\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  }\n\n  // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n  const [{ inst }, forceUpdate] = useState({ inst: { value, getSnapshot } });\n\n  // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n  useLayoutEffect(() => {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot;\n\n    // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({ inst });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe, value, getSnapshot]);\n\n  useEffect(() => {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({ inst });\n    }\n    const handleStoreChange = () => {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({ inst });\n      }\n    };\n    // Subscribe to the store and return a clean-up function.\n    return subscribe(handleStoreChange);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe]);\n\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst: any) {\n  const latestGetSnapshot = inst.getSnapshot;\n  const prevValue = inst.value;\n  try {\n    const nextValue = latestGetSnapshot();\n    return !is(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n", "/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nexport function useSyncExternalStore<T>(\n  subscribe: (fn: () => void) => () => void,\n  getSnapshot: () => T,\n  getServerSnapshot?: () => T\n): T {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n", "/**\n * Inlined into the react-router repo since use-sync-external-store does not\n * provide a UMD-compatible package, so we need this to be able to distribute\n * UMD react-router bundles\n */\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nimport * as React from \"react\";\n\nimport { useSyncExternalStore as client } from \"./useSyncExternalStoreShimClient\";\nimport { useSyncExternalStore as server } from \"./useSyncExternalStoreShimServer\";\n\nconst canUseDOM: boolean = !!(\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\"\n);\nconst isServerEnvironment = !canUseDOM;\nconst shim = isServerEnvironment ? server : client;\n\nexport const useSyncExternalStore =\n  \"useSyncExternalStore\" in React\n    ? ((module) => module.useSyncExternalStore)(React)\n    : shim;\n", "import * as React from \"react\";\nimport type {\n  AgnosticRouteMatch,\n  AgnosticIndexRouteObject,\n  AgnosticNonIndexRouteObject,\n  History,\n  Location,\n  Router,\n  StaticHandlerContext,\n  To,\n  TrackedPromise,\n} from \"@remix-run/router\";\nimport type { Action as NavigationType } from \"@remix-run/router\";\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nexport interface IndexRouteObject {\n  caseSensitive?: AgnosticIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticIndexRouteObject[\"path\"];\n  id?: AgnosticIndexRouteObject[\"id\"];\n  loader?: AgnosticIndexRouteObject[\"loader\"];\n  action?: AgnosticIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticIndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport interface NonIndexRouteObject {\n  caseSensitive?: AgnosticNonIndexRouteObject[\"caseSensitive\"];\n  path?: AgnosticNonIndexRouteObject[\"path\"];\n  id?: AgnosticNonIndexRouteObject[\"id\"];\n  loader?: AgnosticNonIndexRouteObject[\"loader\"];\n  action?: AgnosticNonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: AgnosticNonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: AgnosticNonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: AgnosticNonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: RouteObject[];\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport type RouteObject = IndexRouteObject | NonIndexRouteObject;\n\nexport type DataRouteObject = RouteObject & {\n  children?: DataRouteObject[];\n  id: string;\n};\n\nexport interface RouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends RouteObject = RouteObject\n> extends AgnosticRouteMatch<ParamKey, RouteObjectType> {}\n\nexport interface DataRouteMatch extends RouteMatch<string, DataRouteObject> {}\n\nexport interface DataRouterContextObject extends NavigationContextObject {\n  router: Router;\n  staticContext?: StaticHandlerContext;\n}\n\nexport const DataRouterContext =\n  React.createContext<DataRouterContextObject | null>(null);\nif (__DEV__) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nexport const DataRouterStateContext = React.createContext<\n  Router[\"state\"] | null\n>(null);\nif (__DEV__) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nexport const AwaitContext = React.createContext<TrackedPromise | null>(null);\nif (__DEV__) {\n  AwaitContext.displayName = \"Await\";\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\nexport interface NavigateOptions {\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level <Router> API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\nexport interface Navigator {\n  createHref: History[\"createHref\"];\n  // Optional for backwards-compat with Router/HistoryRouter usage (edge case)\n  encodeLocation?: History[\"encodeLocation\"];\n  go: History[\"go\"];\n  push(to: To, state?: any, opts?: NavigateOptions): void;\n  replace(to: To, state?: any, opts?: NavigateOptions): void;\n}\n\ninterface NavigationContextObject {\n  basename: string;\n  navigator: Navigator;\n  static: boolean;\n}\n\nexport const NavigationContext = React.createContext<NavigationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  NavigationContext.displayName = \"Navigation\";\n}\n\ninterface LocationContextObject {\n  location: Location;\n  navigationType: NavigationType;\n}\n\nexport const LocationContext = React.createContext<LocationContextObject>(\n  null!\n);\n\nif (__DEV__) {\n  LocationContext.displayName = \"Location\";\n}\n\nexport interface RouteContextObject {\n  outlet: React.ReactElement | null;\n  matches: RouteMatch[];\n}\n\nexport const RouteContext = React.createContext<RouteContextObject>({\n  outlet: null,\n  matches: [],\n});\n\nif (__DEV__) {\n  RouteContext.displayName = \"Route\";\n}\n\nexport const RouteErrorContext = React.createContext<any>(null);\n\nif (__DEV__) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n", "import * as React from \"react\";\nimport type {\n  <PERSON>er,\n  BlockerFunction,\n  Location,\n  ParamParseKey,\n  Params,\n  Path,\n  PathMatch,\n  PathPattern,\n  Router as RemixRouter,\n  To,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  invariant,\n  isRouteErrorResponse,\n  joinPaths,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  resolveTo,\n  warning,\n  UNSAFE_getPathContributingMatches as getPathContributingMatches,\n} from \"@remix-run/router\";\n\nimport type {\n  NavigateOptions,\n  RouteContextObject,\n  RouteMatch,\n  RouteObject,\n  DataRouteMatch,\n  RelativeRoutingType,\n} from \"./context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n  RouteErrorContext,\n  AwaitContext,\n} from \"./context\";\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\nexport function useHref(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useHref() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { hash, pathname, search } = useResolvedPath(to, { relative });\n\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname =\n      pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({ pathname: joinedPathname, search, hash });\n}\n\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\nexport function useInRouterContext(): boolean {\n  return React.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\nexport function useLocation(): Location {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useLocation() may be used only in the context of a <Router> component.`\n  );\n\n  return React.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\nexport function useNavigationType(): NavigationType {\n  return React.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\nexport function useMatch<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(pattern: PathPattern<Path> | Path): PathMatch<ParamKey> | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useMatch() may be used only in the context of a <Router> component.`\n  );\n\n  let { pathname } = useLocation();\n  return React.useMemo(\n    () => matchPath<ParamKey, Path>(pattern, pathname),\n    [pathname, pattern]\n  );\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\nexport interface NavigateFunction {\n  (to: To, options?: NavigateOptions): void;\n  (delta: number): void;\n}\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nexport function useNavigate(): NavigateFunction {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useNavigate() may be used only in the context of a <Router> component.`\n  );\n\n  let { basename, navigator } = React.useContext(NavigationContext);\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  let activeRef = React.useRef(false);\n  React.useEffect(() => {\n    activeRef.current = true;\n  });\n\n  let navigate: NavigateFunction = React.useCallback(\n    (to: To | number, options: NavigateOptions = {}) => {\n      warning(\n        activeRef.current,\n        `You should call navigate() in a React.useEffect(), not when ` +\n          `your component is first rendered.`\n      );\n\n      if (!activeRef.current) return;\n\n      if (typeof to === \"number\") {\n        navigator.go(to);\n        return;\n      }\n\n      let path = resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        options.relative === \"path\"\n      );\n\n      // If we're operating within a basename, prepend it to the pathname prior\n      // to handing off to history.  If this is a root navigation, then we\n      // navigate to the raw basename which allows the basename to have full\n      // control over the presence of a trailing slash on root links\n      if (basename !== \"/\") {\n        path.pathname =\n          path.pathname === \"/\"\n            ? basename\n            : joinPaths([basename, path.pathname]);\n      }\n\n      (!!options.replace ? navigator.replace : navigator.push)(\n        path,\n        options.state,\n        options\n      );\n    },\n    [basename, navigator, routePathnamesJson, locationPathname]\n  );\n\n  return navigate;\n}\n\nconst OutletContext = React.createContext<unknown>(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\nexport function useOutletContext<Context = unknown>(): Context {\n  return React.useContext(OutletContext) as Context;\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\nexport function useOutlet(context?: unknown): React.ReactElement | null {\n  let outlet = React.useContext(RouteContext).outlet;\n  if (outlet) {\n    return (\n      <OutletContext.Provider value={context}>{outlet}</OutletContext.Provider>\n    );\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\nexport function useParams<\n  ParamsOrKey extends string | Record<string, string | undefined> = string\n>(): Readonly<\n  [ParamsOrKey] extends [string] ? Params<ParamsOrKey> : Partial<ParamsOrKey>\n> {\n  let { matches } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? (routeMatch.params as any) : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\nexport function useResolvedPath(\n  to: To,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): Path {\n  let { matches } = React.useContext(RouteContext);\n  let { pathname: locationPathname } = useLocation();\n\n  let routePathnamesJson = JSON.stringify(\n    getPathContributingMatches(matches).map((match) => match.pathnameBase)\n  );\n\n  return React.useMemo(\n    () =>\n      resolveTo(\n        to,\n        JSON.parse(routePathnamesJson),\n        locationPathname,\n        relative === \"path\"\n      ),\n    [to, routePathnamesJson, locationPathname, relative]\n  );\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\nexport function useRoutes(\n  routes: RouteObject[],\n  locationArg?: Partial<Location> | string\n): React.ReactElement | null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of the\n    // router loaded. We can help them understand how to avoid that.\n    `useRoutes() may be used only in the context of a <Router> component.`\n  );\n\n  let { navigator } = React.useContext(NavigationContext);\n  let dataRouterStateContext = React.useContext(DataRouterStateContext);\n  let { matches: parentMatches } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (__DEV__) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = (parentRoute && parentRoute.path) || \"\";\n    warningOnce(\n      parentPathname,\n      !parentRoute || parentPath.endsWith(\"*\"),\n      `You rendered descendant <Routes> (or called \\`useRoutes()\\`) at ` +\n        `\"${parentPathname}\" (under <Route path=\"${parentPath}\">) but the ` +\n        `parent route path has no trailing \"*\". This means if you navigate ` +\n        `deeper, the parent won't match anymore and therefore the child ` +\n        `routes will never render.\\n\\n` +\n        `Please change the parent <Route path=\"${parentPath}\"> to <Route ` +\n        `path=\"${parentPath === \"/\" ? \"*\" : `${parentPath}/*`}\">.`\n    );\n  }\n\n  let locationFromContext = useLocation();\n\n  let location;\n  if (locationArg) {\n    let parsedLocationArg =\n      typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n    invariant(\n      parentPathnameBase === \"/\" ||\n        parsedLocationArg.pathname?.startsWith(parentPathnameBase),\n      `When overriding the location using \\`<Routes location>\\` or \\`useRoutes(routes, location)\\`, ` +\n        `the location pathname must begin with the portion of the URL pathname that was ` +\n        `matched by all parent routes. The current pathname base is \"${parentPathnameBase}\" ` +\n        `but pathname \"${parsedLocationArg.pathname}\" was given in the \\`location\\` prop.`\n    );\n\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n  let remainingPathname =\n    parentPathnameBase === \"/\"\n      ? pathname\n      : pathname.slice(parentPathnameBase.length) || \"/\";\n\n  let matches = matchRoutes(routes, { pathname: remainingPathname });\n\n  if (__DEV__) {\n    warning(\n      parentRoute || matches != null,\n      `No routes matched location \"${location.pathname}${location.search}${location.hash}\" `\n    );\n\n    warning(\n      matches == null ||\n        matches[matches.length - 1].route.element !== undefined,\n      `Matched leaf route at location \"${location.pathname}${location.search}${location.hash}\" does not have an element. ` +\n        `This means it will render an <Outlet /> with a null value by default resulting in an \"empty\" page.`\n    );\n  }\n\n  let renderedMatches = _renderMatches(\n    matches &&\n      matches.map((match) =>\n        Object.assign({}, match, {\n          params: Object.assign({}, parentParams, match.params),\n          pathname: joinPaths([\n            parentPathnameBase,\n            // Re-encode pathnames that were decoded inside matchRoutes\n            navigator.encodeLocation\n              ? navigator.encodeLocation(match.pathname).pathname\n              : match.pathname,\n          ]),\n          pathnameBase:\n            match.pathnameBase === \"/\"\n              ? parentPathnameBase\n              : joinPaths([\n                  parentPathnameBase,\n                  // Re-encode pathnames that were decoded inside matchRoutes\n                  navigator.encodeLocation\n                    ? navigator.encodeLocation(match.pathnameBase).pathname\n                    : match.pathnameBase,\n                ]),\n        })\n      ),\n    parentMatches,\n    dataRouterStateContext || undefined\n  );\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return (\n      <LocationContext.Provider\n        value={{\n          location: {\n            pathname: \"/\",\n            search: \"\",\n            hash: \"\",\n            state: null,\n            key: \"default\",\n            ...location,\n          },\n          navigationType: NavigationType.Pop,\n        }}\n      >\n        {renderedMatches}\n      </LocationContext.Provider>\n    );\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorElement() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error)\n    ? `${error.status} ${error.statusText}`\n    : error instanceof Error\n    ? error.message\n    : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = { padding: \"0.5rem\", backgroundColor: lightgrey };\n  let codeStyles = { padding: \"2px 4px\", backgroundColor: lightgrey };\n\n  let devInfo = null;\n  if (__DEV__) {\n    devInfo = (\n      <>\n        <p>💿 Hey developer 👋</p>\n        <p>\n          You can provide a way better UX than this when your app throws errors\n          by providing your own&nbsp;\n          <code style={codeStyles}>errorElement</code> props on&nbsp;\n          <code style={codeStyles}>&lt;Route&gt;</code>\n        </p>\n      </>\n    );\n  }\n\n  return (\n    <>\n      <h2>Unexpected Application Error!</h2>\n      <h3 style={{ fontStyle: \"italic\" }}>{message}</h3>\n      {stack ? <pre style={preStyles}>{stack}</pre> : null}\n      {devInfo}\n    </>\n  );\n}\n\ntype RenderErrorBoundaryProps = React.PropsWithChildren<{\n  location: Location;\n  error: any;\n  component: React.ReactNode;\n  routeContext: RouteContextObject;\n}>;\n\ntype RenderErrorBoundaryState = {\n  location: Location;\n  error: any;\n};\n\nexport class RenderErrorBoundary extends React.Component<\n  RenderErrorBoundaryProps,\n  RenderErrorBoundaryState\n> {\n  constructor(props: RenderErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      location: props.location,\n      error: props.error,\n    };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error: error };\n  }\n\n  static getDerivedStateFromProps(\n    props: RenderErrorBoundaryProps,\n    state: RenderErrorBoundaryState\n  ) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location) {\n      return {\n        error: props.error,\n        location: props.location,\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error || state.error,\n      location: state.location,\n    };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"React Router caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    return this.state.error ? (\n      <RouteContext.Provider value={this.props.routeContext}>\n        <RouteErrorContext.Provider\n          value={this.state.error}\n          children={this.props.component}\n        />\n      </RouteContext.Provider>\n    ) : (\n      this.props.children\n    );\n  }\n}\n\ninterface RenderedRouteProps {\n  routeContext: RouteContextObject;\n  match: RouteMatch<string, RouteObject>;\n  children: React.ReactNode | null;\n}\n\nfunction RenderedRoute({ routeContext, match, children }: RenderedRouteProps) {\n  let dataRouterContext = React.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (\n    dataRouterContext &&\n    dataRouterContext.static &&\n    dataRouterContext.staticContext &&\n    match.route.errorElement\n  ) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return (\n    <RouteContext.Provider value={routeContext}>\n      {children}\n    </RouteContext.Provider>\n  );\n}\n\nexport function _renderMatches(\n  matches: RouteMatch[] | null,\n  parentMatches: RouteMatch[] = [],\n  dataRouterState?: RemixRouter[\"state\"]\n): React.ReactElement | null {\n  if (matches == null) {\n    if (dataRouterState?.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches as DataRouteMatch[];\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = dataRouterState?.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(\n      (m) => m.route.id && errors?.[m.route.id]\n    );\n    invariant(\n      errorIndex >= 0,\n      `Could not find a matching route for the current errors: ${errors}`\n    );\n    renderedMatches = renderedMatches.slice(\n      0,\n      Math.min(renderedMatches.length, errorIndex + 1)\n    );\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    let error = match.route.id ? errors?.[match.route.id] : null;\n    // Only data routers handle errors\n    let errorElement = dataRouterState\n      ? match.route.errorElement || <DefaultErrorElement />\n      : null;\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => (\n      <RenderedRoute match={match} routeContext={{ outlet, matches }}>\n        {error\n          ? errorElement\n          : match.route.element !== undefined\n          ? match.route.element\n          : outlet}\n      </RenderedRoute>\n    );\n    // Only wrap in an error boundary within data router usages when we have an\n    // errorElement on this route.  Otherwise let it bubble up to an ancestor\n    // errorElement\n    return dataRouterState && (match.route.errorElement || index === 0) ? (\n      <RenderErrorBoundary\n        location={dataRouterState.location}\n        component={errorElement}\n        error={error}\n        children={getChildren()}\n        routeContext={{ outlet: null, matches }}\n      />\n    ) : (\n      getChildren()\n    );\n  }, null as React.ReactElement | null);\n}\n\nenum DataRouterHook {\n  UseBlocker = \"useBlocker\",\n  UseRevalidator = \"useRevalidator\",\n}\n\nenum DataRouterStateHook {\n  UseLoaderData = \"useLoaderData\",\n  UseActionData = \"useActionData\",\n  UseRouteError = \"useRouteError\",\n  UseNavigation = \"useNavigation\",\n  UseRouteLoaderData = \"useRouteLoaderData\",\n  UseMatches = \"useMatches\",\n  UseRevalidator = \"useRevalidator\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\nfunction useRouteContext(hookName: DataRouterStateHook) {\n  let route = React.useContext(RouteContext);\n  invariant(route, getDataRouterConsoleError(hookName));\n  return route;\n}\n\nfunction useCurrentRouteId(hookName: DataRouterStateHook) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  invariant(\n    thisRoute.route.id,\n    `${hookName} can only be used on routes that contain a unique \"id\"`\n  );\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nexport function useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nexport function useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return {\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation,\n  };\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nexport function useMatches() {\n  let { matches, loaderData } = useDataRouterState(\n    DataRouterStateHook.UseMatches\n  );\n  return React.useMemo(\n    () =>\n      matches.map((match) => {\n        let { pathname, params } = match;\n        // Note: This structure matches that created by createUseMatchesMatch\n        // in the @remix-run/router , so if you change this please also change\n        // that :)  Eventually we'll DRY this up\n        return {\n          id: match.route.id,\n          pathname,\n          params,\n          data: loaderData[match.route.id] as unknown,\n          handle: match.route.handle as unknown,\n        };\n      }),\n    [matches, loaderData]\n  );\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nexport function useLoaderData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\n      `You cannot \\`useLoaderData\\` in an errorElement (routeId: ${routeId})`\n    );\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nexport function useRouteLoaderData(routeId: string): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nexport function useActionData(): unknown {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useActionData must be used inside a RouteContext`);\n\n  return Object.values(state?.actionData || {})[0];\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * errorElement to display a proper error message.\n */\nexport function useRouteError(): unknown {\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return state.errors?.[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor <Await /> value\n */\nexport function useAsyncValue(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._data;\n}\n\n/**\n * Returns the error from the nearest ancestor <Await /> value\n */\nexport function useAsyncError(): unknown {\n  let value = React.useContext(AwaitContext);\n  return value?._error;\n}\n\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nexport function useBlocker(shouldBlock: boolean | BlockerFunction): Blocker {\n  let { router } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let [blockerKey] = React.useState(() => String(++blockerId));\n\n  let blockerFunction = React.useCallback<BlockerFunction>(\n    (args) => {\n      return typeof shouldBlock === \"function\"\n        ? !!shouldBlock(args)\n        : !!shouldBlock;\n    },\n    [shouldBlock]\n  );\n\n  let blocker = router.getBlocker(blockerKey, blockerFunction);\n\n  // Cleanup on unmount\n  React.useEffect(\n    () => () => router.deleteBlocker(blockerKey),\n    [router, blockerKey]\n  );\n\n  return blocker;\n}\n\nconst alreadyWarned: Record<string, boolean> = {};\n\nfunction warningOnce(key: string, cond: boolean, message: string) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    warning(false, message);\n  }\n}\n", "import * as React from \"react\";\nimport type {\n  TrackedPromise,\n  InitialEntry,\n  Location,\n  MemoryHistory,\n  Router as RemixRouter,\n  RouterState,\n  To,\n} from \"@remix-run/router\";\nimport {\n  Action as NavigationType,\n  AbortedDeferredError,\n  createMemoryHistory,\n  invariant,\n  parsePath,\n  stripBasename,\n  warning,\n} from \"@remix-run/router\";\nimport { useSyncExternalStore as useSyncExternalStoreShim } from \"./use-sync-external-store-shim\";\n\nimport type {\n  DataRouteObject,\n  IndexRouteObject,\n  RouteMatch,\n  RouteObject,\n  Navigator,\n  NonIndexRouteObject,\n  RelativeRoutingType,\n} from \"./context\";\nimport {\n  LocationContext,\n  NavigationContext,\n  DataRouterContext,\n  DataRouterStateContext,\n  AwaitContext,\n} from \"./context\";\nimport {\n  useAsyncValue,\n  useInRouterContext,\n  useNavigate,\n  useOutlet,\n  useRoutes,\n  _renderMatches,\n} from \"./hooks\";\n\nexport interface RouterProviderProps {\n  fallbackElement?: React.ReactNode;\n  router: RemixRouter;\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n}: RouterProviderProps): React.ReactElement {\n  // Sync router state to our component state to force re-renders\n  let state: RouterState = useSyncExternalStoreShim(\n    router.subscribe,\n    () => router.state,\n    // We have to provide this so React@18 doesn't complain during hydration,\n    // but we pass our serialized hydration data into the router so state here\n    // is already synced with what the server saw\n    () => router.state\n  );\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider\n        value={{\n          router,\n          navigator,\n          static: false,\n          // Do we need this?\n          basename,\n        }}\n      >\n        <DataRouterStateContext.Provider value={state}>\n          <Router\n            basename={router.basename}\n            location={router.state.location}\n            navigationType={router.state.historyAction}\n            navigator={navigator}\n          >\n            {router.state.initialized ? <Routes /> : fallbackElement}\n          </Router>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nexport interface MemoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n}\n\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nexport function MemoryRouter({\n  basename,\n  children,\n  initialEntries,\n  initialIndex,\n}: MemoryRouterProps): React.ReactElement {\n  let historyRef = React.useRef<MemoryHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true,\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface NavigateProps {\n  to: To;\n  replace?: boolean;\n  state?: any;\n  relative?: RelativeRoutingType;\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nexport function Navigate({\n  to,\n  replace,\n  state,\n  relative,\n}: NavigateProps): null {\n  invariant(\n    useInRouterContext(),\n    // TODO: This error is probably because they somehow have 2 versions of\n    // the router loaded. We can help them understand how to avoid that.\n    `<Navigate> may be used only in the context of a <Router> component.`\n  );\n\n  warning(\n    !React.useContext(NavigationContext).static,\n    `<Navigate> must not be used on the initial render in a <StaticRouter>. ` +\n      `This is a no-op, but you should modify your code so the <Navigate> is ` +\n      `only ever rendered in response to some user interaction or state change.`\n  );\n\n  let dataRouterState = React.useContext(DataRouterStateContext);\n  let navigate = useNavigate();\n\n  React.useEffect(() => {\n    // Avoid kicking off multiple navigations if we're in the middle of a\n    // data-router navigation, since components get re-rendered when we enter\n    // a submitting/loading state\n    if (dataRouterState && dataRouterState.navigation.state !== \"idle\") {\n      return;\n    }\n    navigate(to, { replace, state, relative });\n  });\n\n  return null;\n}\n\nexport interface OutletProps {\n  context?: unknown;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nexport function Outlet(props: OutletProps): React.ReactElement | null {\n  return useOutlet(props.context);\n}\n\nexport interface PathRouteProps {\n  caseSensitive?: NonIndexRouteObject[\"caseSensitive\"];\n  path?: NonIndexRouteObject[\"path\"];\n  id?: NonIndexRouteObject[\"id\"];\n  loader?: NonIndexRouteObject[\"loader\"];\n  action?: NonIndexRouteObject[\"action\"];\n  hasErrorBoundary?: NonIndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: NonIndexRouteObject[\"shouldRevalidate\"];\n  handle?: NonIndexRouteObject[\"handle\"];\n  index?: false;\n  children?: React.ReactNode;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport interface LayoutRouteProps extends PathRouteProps {}\n\nexport interface IndexRouteProps {\n  caseSensitive?: IndexRouteObject[\"caseSensitive\"];\n  path?: IndexRouteObject[\"path\"];\n  id?: IndexRouteObject[\"id\"];\n  loader?: IndexRouteObject[\"loader\"];\n  action?: IndexRouteObject[\"action\"];\n  hasErrorBoundary?: IndexRouteObject[\"hasErrorBoundary\"];\n  shouldRevalidate?: IndexRouteObject[\"shouldRevalidate\"];\n  handle?: IndexRouteObject[\"handle\"];\n  index: true;\n  children?: undefined;\n  element?: React.ReactNode | null;\n  errorElement?: React.ReactNode | null;\n}\n\nexport type RouteProps = PathRouteProps | LayoutRouteProps | IndexRouteProps;\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nexport function Route(_props: RouteProps): React.ReactElement | null {\n  invariant(\n    false,\n    `A <Route> is only ever to be used as the child of <Routes> element, ` +\n      `never rendered directly. Please wrap your <Route> in a <Routes>.`\n  );\n}\n\nexport interface RouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  location: Partial<Location> | string;\n  navigationType?: NavigationType;\n  navigator: Navigator;\n  static?: boolean;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nexport function Router({\n  basename: basenameProp = \"/\",\n  children = null,\n  location: locationProp,\n  navigationType = NavigationType.Pop,\n  navigator,\n  static: staticProp = false,\n}: RouterProps): React.ReactElement | null {\n  invariant(\n    !useInRouterContext(),\n    `You cannot render a <Router> inside another <Router>.` +\n      ` You should never have more than one in your app.`\n  );\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(\n    () => ({ basename, navigator, static: staticProp }),\n    [basename, navigator, staticProp]\n  );\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\",\n  } = locationProp;\n\n  let location = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      pathname: trailingPathname,\n      search,\n      hash,\n      state,\n      key,\n    };\n  }, [basename, pathname, search, hash, state, key]);\n\n  warning(\n    location != null,\n    `<Router basename=\"${basename}\"> is not able to match the URL ` +\n      `\"${pathname}${search}${hash}\" because it does not start with the ` +\n      `basename, so the <Router> won't render anything.`\n  );\n\n  if (location == null) {\n    return null;\n  }\n\n  return (\n    <NavigationContext.Provider value={navigationContext}>\n      <LocationContext.Provider\n        children={children}\n        value={{ location, navigationType }}\n      />\n    </NavigationContext.Provider>\n  );\n}\n\nexport interface RoutesProps {\n  children?: React.ReactNode;\n  location?: Partial<Location> | string;\n}\n\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nexport function Routes({\n  children,\n  location,\n}: RoutesProps): React.ReactElement | null {\n  let dataRouterContext = React.useContext(DataRouterContext);\n  // When in a DataRouterContext _without_ children, we use the router routes\n  // directly.  If we have children, then we're in a descendant tree and we\n  // need to use child routes.\n  let routes =\n    dataRouterContext && !children\n      ? (dataRouterContext.router.routes as DataRouteObject[])\n      : createRoutesFromChildren(children);\n  return useRoutes(routes, location);\n}\n\nexport interface AwaitResolveRenderFunction {\n  (data: Awaited<any>): React.ReactNode;\n}\n\nexport interface AwaitProps {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nexport function Await({ children, errorElement, resolve }: AwaitProps) {\n  return (\n    <AwaitErrorBoundary resolve={resolve} errorElement={errorElement}>\n      <ResolveAwait>{children}</ResolveAwait>\n    </AwaitErrorBoundary>\n  );\n}\n\ntype AwaitErrorBoundaryProps = React.PropsWithChildren<{\n  errorElement?: React.ReactNode;\n  resolve: TrackedPromise | any;\n}>;\n\ntype AwaitErrorBoundaryState = {\n  error: any;\n};\n\nenum AwaitRenderStatus {\n  pending,\n  success,\n  error,\n}\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component<\n  AwaitErrorBoundaryProps,\n  AwaitErrorBoundaryState\n> {\n  constructor(props: AwaitErrorBoundaryProps) {\n    super(props);\n    this.state = { error: null };\n  }\n\n  static getDerivedStateFromError(error: any) {\n    return { error };\n  }\n\n  componentDidCatch(error: any, errorInfo: any) {\n    console.error(\n      \"<Await> caught the following error during render\",\n      error,\n      errorInfo\n    );\n  }\n\n  render() {\n    let { children, errorElement, resolve } = this.props;\n\n    let promise: TrackedPromise | null = null;\n    let status: AwaitRenderStatus = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_data\", { get: () => resolve });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", { get: () => true });\n      Object.defineProperty(promise, \"_error\", { get: () => renderError });\n    } else if ((resolve as TrackedPromise)._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status =\n        promise._error !== undefined\n          ? AwaitRenderStatus.error\n          : promise._data !== undefined\n          ? AwaitRenderStatus.success\n          : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", { get: () => true });\n      promise = resolve.then(\n        (data: any) =>\n          Object.defineProperty(resolve, \"_data\", { get: () => data }),\n        (error: any) =>\n          Object.defineProperty(resolve, \"_error\", { get: () => error })\n      );\n    }\n\n    if (\n      status === AwaitRenderStatus.error &&\n      promise._error instanceof AbortedDeferredError\n    ) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return <AwaitContext.Provider value={promise} children={errorElement} />;\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return <AwaitContext.Provider value={promise} children={children} />;\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on <Await>\n */\nfunction ResolveAwait({\n  children,\n}: {\n  children: React.ReactNode | AwaitResolveRenderFunction;\n}) {\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return <>{toRender}</>;\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\nexport function createRoutesFromChildren(\n  children: React.ReactNode,\n  parentPath: number[] = []\n): RouteObject[] {\n  let routes: RouteObject[] = [];\n\n  React.Children.forEach(children, (element, index) => {\n    if (!React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(\n        routes,\n        createRoutesFromChildren(element.props.children, parentPath)\n      );\n      return;\n    }\n\n    invariant(\n      element.type === Route,\n      `[${\n        typeof element.type === \"string\" ? element.type : element.type.name\n      }] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`\n    );\n\n    invariant(\n      !element.props.index || !element.props.children,\n      \"An index route cannot have child routes.\"\n    );\n\n    let treePath = [...parentPath, index];\n    let route: RouteObject = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      hasErrorBoundary: element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(\n        element.props.children,\n        treePath\n      );\n    }\n\n    routes.push(route);\n  });\n\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nexport function renderMatches(\n  matches: RouteMatch[] | null\n): React.ReactElement | null {\n  return _renderMatches(matches);\n}\n\n/**\n * @private\n * Walk the route tree and add hasErrorBoundary if it's not provided, so that\n * users providing manual route arrays can just specify errorElement\n */\nexport function enhanceManualRouteObjects(\n  routes: RouteObject[]\n): RouteObject[] {\n  return routes.map((route) => {\n    let routeClone = { ...route };\n    if (routeClone.hasErrorBoundary == null) {\n      routeClone.hasErrorBoundary = routeClone.errorElement != null;\n    }\n    if (routeClone.children) {\n      routeClone.children = enhanceManualRouteObjects(routeClone.children);\n    }\n    return routeClone;\n  });\n}\n", "import type {\n  ActionFunction,\n  ActionFunctionArgs,\n  Blocker,\n  BlockerFunction,\n  Fetcher,\n  HydrationState,\n  JsonFunction,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  Navigation,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  PathPattern,\n  RedirectFunction,\n  Router as RemixRouter,\n  ShouldRevalidateFunction,\n  To,\n  InitialEntry,\n} from \"@remix-run/router\";\nimport {\n  AbortedDeferredError,\n  Action as NavigationType,\n  createMemoryHistory,\n  createPath,\n  createRouter,\n  defer,\n  generatePath,\n  isRouteErrorResponse,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  resolvePath,\n} from \"@remix-run/router\";\n\nimport type {\n  AwaitProps,\n  MemoryRouterProps,\n  NavigateProps,\n  OutletProps,\n  RouteProps,\n  PathRouteProps,\n  LayoutRouteProps,\n  IndexRouteProps,\n  RouterProps,\n  RoutesProps,\n  RouterProviderProps,\n} from \"./lib/components\";\nimport {\n  enhanceManualRouteObjects,\n  createRoutesFromChildren,\n  renderMatches,\n  Await,\n  MemoryRouter,\n  Navigate,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n} from \"./lib/components\";\nimport type {\n  DataRouteMatch,\n  DataRouteObject,\n  IndexRouteObject,\n  Navigator,\n  NavigateOptions,\n  NonIndexRouteObject,\n  RouteMatch,\n  RouteObject,\n  RelativeRoutingType,\n} from \"./lib/context\";\nimport {\n  DataRouterContext,\n  DataRouterStateContext,\n  LocationContext,\n  NavigationContext,\n  RouteContext,\n} from \"./lib/context\";\nimport type { NavigateFunction } from \"./lib/hooks\";\nimport {\n  useBlocker,\n  useHref,\n  useInRouterContext,\n  useLocation,\n  useMatch,\n  useNavigationType,\n  useNavigate,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRoutes,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useLoaderData,\n  useMatches,\n  useNavigation,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n} from \"./lib/hooks\";\n\n// Exported for backwards compatibility, but not being used internally anymore\ntype Hash = string;\ntype Pathname = string;\ntype Search = string;\n\n// Expose react-router public API\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  Blocker as unstable_Blocker,\n  BlockerFunction as unstable_BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  To,\n};\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  RouterProvider,\n  Routes,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromChildren as createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  useBlocker as unstable_useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n};\n\nexport function createMemoryRouter(\n  routes: RouteObject[],\n  opts?: {\n    basename?: string;\n    hydrationData?: HydrationState;\n    initialEntries?: InitialEntry[];\n    initialIndex?: number;\n  }\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    history: createMemoryHistory({\n      initialEntries: opts?.initialEntries,\n      initialIndex: opts?.initialIndex,\n    }),\n    hydrationData: opts?.hydrationData,\n    routes: enhanceManualRouteObjects(routes),\n  }).initialize();\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  NavigationContext as UNSAFE_NavigationContext,\n  LocationContext as UNSAFE_LocationContext,\n  RouteContext as UNSAFE_RouteContext,\n  DataRouterContext as UNSAFE_DataRouterContext,\n  DataRouterStateContext as UNSAFE_DataRouterStateContext,\n  enhanceManualRouteObjects as UNSAFE_enhanceManualRouteObjects,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;;AACA,SAASA,UAATA,CAAoBC,CAApB,EAA4BC,CAA5B,EAAoC;EAClC,OACGD,CAAC,KAAKC,CAAN,KAAYD,CAAC,KAAK,CAAN,IAAW,IAAIA,CAAJ,KAAU,IAAIC,CAArC,CAAD,IAA8CD,CAAC,KAAKA,CAAN,IAAWC,CAAC,KAAKA,CADjE;EAAA;AAGD;AAED,MAAMC,EAA+B,GACnC,OAAOC,MAAM,CAACD,EAAd,KAAqB,UAArB,GAAkCC,MAAM,CAACD,EAAzC,GAA8CH,UADhD;AAIA;;AACA,MAAM;EAAEK,QAAF;EAAYC,SAAZ;EAAuBC,eAAvB;EAAwCC;AAAxC,IAA0DC,KAAhE;AAEA,IAAIC,iBAAiB,GAAG,KAAxB;AACA,IAAIC,0BAA0B,GAAG,KAAjC;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,sBAATC,CACLC,SADK,EAELC,WAFK;AAAA;AAIL;AACA;AACA;AACAC,iBAPK,EAQF;EACH,IAAaC,OAAA,CAAAC,GAAA,CAAAC,QAAA;IACX,IAAI,CAACT,iBAAL,EAAwB;MACtB,IAAI,qBAAqBD,KAAzB,EAAgC;QAC9BC,iBAAiB,GAAG,IAApB;QACAU,OAAO,CAACC,KAAR,CACE,mEACE,6CADF,GAEE,gEAFF,GAGE,yBAJJ;MAMD;IACF;EACF,CAbE;EAgBH;EACA;EACA;;EACA,MAAMC,KAAK,GAAGP,WAAW,EAAzB;EACA,IAAaE,OAAA,CAAAC,GAAA,CAAAC,QAAA;IACX,IAAI,CAACR,0BAAL,EAAiC;MAC/B,MAAMY,WAAW,GAAGR,WAAW,EAA/B;MACA,IAAI,CAACZ,EAAE,CAACmB,KAAD,EAAQC,WAAR,CAAP,EAA6B;QAC3BH,OAAO,CAACC,KAAR,CACE,sEADF;QAGAV,0BAA0B,GAAG,IAA7B;MACD;IACF;EACF,CA9BE;EAiCH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,MAAM,CAAC;IAAEa;EAAF,CAAD,EAAWC,WAAX,CAA0B,GAAApB,QAAQ,CAAC;IAAEmB,IAAI,EAAE;MAAEF,KAAF;MAASP;IAAT;GAAT,CAAxC,CA9CG;EAiDH;EACA;;EACAR,eAAe,CAAC,MAAM;IACpBiB,IAAI,CAACF,KAAL,GAAaA,KAAb;IACAE,IAAI,CAACT,WAAL,GAAmBA,WAAnB,CAFoB;IAKpB;IACA;IACA;;IACA,IAAIW,sBAAsB,CAACF,IAAD,CAA1B,EAAkC;MAChC;MACAC,WAAW,CAAC;QAAED;MAAF,CAAD,CAAX;IACD,CAXmB;GAAP,EAaZ,CAACV,SAAD,EAAYQ,KAAZ,EAAmBP,WAAnB,CAbY,CAAf;EAeAT,SAAS,CAAC,MAAM;IACd;IACA;IACA,IAAIoB,sBAAsB,CAACF,IAAD,CAA1B,EAAkC;MAChC;MACAC,WAAW,CAAC;QAAED;MAAF,CAAD,CAAX;IACD;IACD,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;MAC9B;MACA;MACA;MACA;MAEA;MACA;MACA,IAAID,sBAAsB,CAACF,IAAD,CAA1B,EAAkC;QAChC;QACAC,WAAW,CAAC;UAAED;QAAF,CAAD,CAAX;MACD;IACF,CAZD,CAPc;;IAqBd,OAAOV,SAAS,CAACa,iBAAD,CAAhB,CArBc;EAuBf,CAvBQ,EAuBN,CAACb,SAAD,CAvBM,CAAT;EAyBAN,aAAa,CAACc,KAAD,CAAb;EACA,OAAOA,KAAP;AACD;AAED,SAASI,sBAATA,CAAgCF,IAAhC,EAA2C;EACzC,MAAMI,iBAAiB,GAAGJ,IAAI,CAACT,WAA/B;EACA,MAAMc,SAAS,GAAGL,IAAI,CAACF,KAAvB;EACA,IAAI;IACF,MAAMQ,SAAS,GAAGF,iBAAiB,EAAnC;IACA,OAAO,CAACzB,EAAE,CAAC0B,SAAD,EAAYC,SAAZ,CAAV;GAFF,CAGE,OAAOT,KAAP,EAAc;IACd,OAAO,IAAP;EACD;AACF;;ACvJD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEO,SAASU,sBAATlB,CACLC,SADK,EAELC,WAFK,EAGLC,iBAHK,EAIF;EACH;EACA;EACA;EACA;EACA,OAAOD,WAAW,EAAlB;AACD;;ACnBD;AACA;AACA;AACA;AACA;AAgBA,MAAMiB,SAAkB,GAAG,CAAC,EAC1B,OAAOC,MAAP,KAAkB,WAAlB,IACA,OAAOA,MAAM,CAACC,QAAd,KAA2B,WAD3B,IAEA,OAAOD,MAAM,CAACC,QAAP,CAAgBC,aAAvB,KAAyC,WAHf,CAA5B;AAKA,MAAMC,mBAAmB,GAAG,CAACJ,SAA7B;AACA,MAAMK,IAAI,GAAGD,mBAAmB,GAAGL,sBAAH,GAAYnB,sBAA5C;AAEO,MAAMC,oBAAoB,GAC/B,sBAA0B,IAAAJ,KAA1B,GACI,CAAE6B,MAAD,IAAYA,MAAM,CAACzB,oBAApB,EAA0CJ,KAA1C,CADJ,GAEI4B,IAHC;ACqCA,MAAME,iBAAiB,gBAC5B9B,KAAK,CAAC+B,aAAN,CAAoD,IAApD;AACF,IAAavB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXoB,iBAAiB,CAACE,WAAlB,GAAgC,YAAhC;AACD;AAEM,MAAMC,sBAAsB,gBAAGjC,KAAK,CAAC+B,aAAN,CAEpC,IAFoC;AAGtC,IAAavB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXuB,sBAAsB,CAACD,WAAvB,GAAqC,iBAArC;AACD;AAEM,MAAME,YAAY,gBAAGlC,KAAK,CAAC+B,aAAN,CAA2C,IAA3C,CAArB;AACP,IAAavB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXwB,YAAY,CAACF,WAAb,GAA2B,OAA3B;AACD;AAmCM,MAAMG,iBAAiB,gBAAGnC,KAAK,CAAC+B,aAAN,CAC/B,IAD+B;AAIjC,IAAavB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACXyB,iBAAiB,CAACH,WAAlB,GAAgC,YAAhC;AACD;AAOM,MAAMI,eAAe,gBAAGpC,KAAK,CAAC+B,aAAN,CAC7B,IAD6B;AAI/B,IAAavB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACX0B,eAAe,CAACJ,WAAhB,GAA8B,UAA9B;AACD;MAOYK,YAAY,gBAAGrC,KAAK,CAAC+B,aAAN,CAAwC;EAClEO,MAAM,EAAE,IAD0D;EAElEC,OAAO,EAAE;AAFyD,CAAxC;AAK5B,IAAa/B,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACX2B,YAAY,CAACL,WAAb,GAA2B,OAA3B;AACD;AAEM,MAAMQ,iBAAiB,gBAAGxC,KAAK,CAAC+B,aAAN,CAAyB,IAAzB,CAA1B;AAEP,IAAavB,OAAA,CAAAC,GAAA,CAAAC,QAAA;EACX8B,iBAAiB,CAACR,WAAlB,GAAgC,YAAhC;AACD;;AC/GD;AACA;AACA;AACA;AACA;AACA;;AACO,SAASS,OAATA,CACLC,EADK,EAGGC,KAAA;EAAA,IADR;IAAEC;EAAF,CACQ,GAAAD,KAAA,cAD2C,EAC3C,GAAAA,KAAA;EACR,CACEE,kBAAkB,EADpB,GAAArC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAEP;EAAA;EACA;EAHO,oEAAT,IAAAA,SAAS,CAAT;EAOA,IAAI;IAAEC,QAAF;IAAYC;EAAZ,IAA0BhD,KAAK,CAACiD,UAAN,CAAiBd,iBAAjB,CAA9B;EACA,IAAI;IAAEe,IAAF;IAAQC,QAAR;IAAkBC;GAAW,GAAAC,eAAe,CAACX,EAAD,EAAK;IAAEE;EAAF,CAAL,CAAhD;EAEA,IAAIU,cAAc,GAAGH,QAArB,CAXQ;EAcR;EACA;EACA;;EACA,IAAIJ,QAAQ,KAAK,GAAjB,EAAsB;IACpBO,cAAc,GACZH,QAAQ,KAAK,GAAb,GAAmBJ,QAAnB,GAA8BQ,SAAS,CAAC,CAACR,QAAD,EAAWI,QAAX,CAAD,CADzC;EAED;EAED,OAAOH,SAAS,CAACQ,UAAV,CAAqB;IAAEL,QAAQ,EAAEG,cAAZ;IAA4BF,MAA5B;IAAoCF;EAApC,CAArB,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;AACO,SAASL,kBAATA,CAAA,EAAuC;EAC5C,OAAO7C,KAAK,CAACiD,UAAN,CAAiBb,eAAjB,KAAqC,IAA5C;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASqB,WAATA,CAAA,EAAiC;EACtC,CACEZ,kBAAkB,EADpB,GAAArC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAEP;EAAA;EACA;EAHO,wEAAT,IAAAA,SAAS,CAAT;EAOA,OAAO9C,KAAK,CAACiD,UAAN,CAAiBb,eAAjB,EAAkCsB,QAAzC;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,iBAATA,CAAA,EAA6C;EAClD,OAAO3D,KAAK,CAACiD,UAAN,CAAiBb,eAAjB,EAAkCwB,cAAzC;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,QAATA,CAGLC,OAHK,EAG0D;EAC/D,CACEjB,kBAAkB,EADpB,GAAArC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAEP;EAAA;EACA;EAHO,qEAAT,IAAAA,SAAS,CAAT;EAOA,IAAI;IAAEK;EAAF,IAAeM,WAAW,EAA9B;EACA,OAAOzD,KAAK,CAAC+D,OAAN,CACL,MAAMC,SAAS,CAAiBF,OAAjB,EAA0BX,QAA1B,CADV,EAEL,CAACA,QAAD,EAAWW,OAAX,CAFK,CAAP;AAID;AAED;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,WAATA,CAAA,EAAyC;EAC9C,CACEpB,kBAAkB,EADpB,GAAArC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAEP;EAAA;EACA;EAHO,wEAAT,IAAAA,SAAS,CAAT;EAOA,IAAI;IAAEC,QAAF;IAAYC;EAAZ,IAA0BhD,KAAK,CAACiD,UAAN,CAAiBd,iBAAjB,CAA9B;EACA,IAAI;IAAEI;EAAF,IAAcvC,KAAK,CAACiD,UAAN,CAAiBZ,YAAjB,CAAlB;EACA,IAAI;IAAEc,QAAQ,EAAEe;EAAZ,IAAiCT,WAAW,EAAhD;EAEA,IAAIU,kBAAkB,GAAGC,IAAI,CAACC,SAAL,CACvBC,iCAA0B,CAAC/B,OAAD,CAA1B,CAAoCgC,GAApC,CAAyCC,KAAD,IAAWA,KAAK,CAACC,YAAzD,CADuB,CAAzB;EAIA,IAAIC,SAAS,GAAG1E,KAAK,CAAC2E,MAAN,CAAa,KAAb,CAAhB;EACA3E,KAAK,CAACH,SAAN,CAAgB,MAAM;IACpB6E,SAAS,CAACE,OAAV,GAAoB,IAApB;GADF;EAIA,IAAIC,QAA0B,GAAG7E,KAAK,CAAC8E,WAAN,CAC/B,UAACpC,EAAD,EAAkBqC,OAAlB,EAAoD;IAAA,IAAlCA,OAAkC;MAAlCA,OAAkC,GAAP,EAAO;IAAA;IAClDvE,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAsE,OAAO,CACLN,SAAS,CAACE,OADL,EAEL,oGAFK,CAAP;IAMA,IAAI,CAACF,SAAS,CAACE,OAAf,EAAwB;IAExB,IAAI,OAAOlC,EAAP,KAAc,QAAlB,EAA4B;MAC1BM,SAAS,CAACiC,EAAV,CAAavC,EAAb;MACA;IACD;IAED,IAAIwC,IAAI,GAAGC,SAAS,CAClBzC,EADkB,EAElB0B,IAAI,CAACgB,KAAL,CAAWjB,kBAAX,CAFkB,EAGlBD,gBAHkB,EAIlBa,OAAO,CAACnC,QAAR,KAAqB,MAJH,CAApB,CAdkD;IAsBlD;IACA;IACA;;IACA,IAAIG,QAAQ,KAAK,GAAjB,EAAsB;MACpBmC,IAAI,CAAC/B,QAAL,GACE+B,IAAI,CAAC/B,QAAL,KAAkB,GAAlB,GACIJ,QADJ,GAEIQ,SAAS,CAAC,CAACR,QAAD,EAAWmC,IAAI,CAAC/B,QAAhB,CAAD,CAHf;IAID;IAED,CAAC,CAAC,CAAC4B,OAAO,CAACM,OAAV,GAAoBrC,SAAS,CAACqC,OAA9B,GAAwCrC,SAAS,CAACsC,IAAnD,EACEJ,IADF,EAEEH,OAAO,CAACQ,KAFV,EAGER,OAHF;GAjC6B,EAuC/B,CAAChC,QAAD,EAAWC,SAAX,EAAsBmB,kBAAtB,EAA0CD,gBAA1C,CAvC+B,CAAjC;EA0CA,OAAOW,QAAP;AACD;AAED,MAAMW,aAAa,gBAAGxF,KAAK,CAAC+B,aAAN,CAA6B,IAA7B,CAAtB;AAEA;AACA;AACA;AACA;AACA;;AACO,SAAS0D,gBAATA,CAAA,EAAwD;EAC7D,OAAOzF,KAAK,CAACiD,UAAN,CAAiBuC,aAAjB,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;AACO,SAASE,SAATA,CAAmBC,OAAnB,EAAiE;EACtE,IAAIrD,MAAM,GAAGtC,KAAK,CAACiD,UAAN,CAAiBZ,YAAjB,EAA+BC,MAA5C;EACA,IAAIA,MAAJ,EAAY;IACV,oBACEtC,KAAA,CAAA0B,aAAA,CAAC8D,aAAD,CAAeI,QAAf;MAAwB/E,KAAK,EAAE8E;IAA/B,GAAyCrD,MAAzC,CADF;EAGD;EACD,OAAOA,MAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;AACO,SAASuD,SAATA,CAAA,EAIL;EACA,IAAI;IAAEtD;EAAF,IAAcvC,KAAK,CAACiD,UAAN,CAAiBZ,YAAjB,CAAlB;EACA,IAAIyD,UAAU,GAAGvD,OAAO,CAACA,OAAO,CAACwD,MAAR,GAAiB,CAAlB,CAAxB;EACA,OAAOD,UAAU,GAAIA,UAAU,CAACE,MAAf,GAAgC,EAAjD;AACD;AAED;AACA;AACA;AACA;AACA;;AACO,SAAS3C,eAATA,CACLX,EADK,EAGCuD,MAAA;EAAA,IADN;IAAErD;EAAF,CACM,GAAAqD,MAAA,cAD6C,EAC7C,GAAAA,MAAA;EACN,IAAI;IAAE1D;EAAF,IAAcvC,KAAK,CAACiD,UAAN,CAAiBZ,YAAjB,CAAlB;EACA,IAAI;IAAEc,QAAQ,EAAEe;EAAZ,IAAiCT,WAAW,EAAhD;EAEA,IAAIU,kBAAkB,GAAGC,IAAI,CAACC,SAAL,CACvBC,iCAA0B,CAAC/B,OAAD,CAA1B,CAAoCgC,GAApC,CAAyCC,KAAD,IAAWA,KAAK,CAACC,YAAzD,CADuB,CAAzB;EAIA,OAAOzE,KAAK,CAAC+D,OAAN,CACL,MACEoB,SAAS,CACPzC,EADO,EAEP0B,IAAI,CAACgB,KAAL,CAAWjB,kBAAX,CAFO,EAGPD,gBAHO,EAIPtB,QAAQ,KAAK,MAJN,CAFN,EAQL,CAACF,EAAD,EAAKyB,kBAAL,EAAyBD,gBAAzB,EAA2CtB,QAA3C,CARK,CAAP;AAUD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASsD,SAATA,CACLC,MADK,EAELC,WAFK,EAGsB;EAC3B,CACEvD,kBAAkB,EADpB,GAAArC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAEP;EAAA;EACA;EAHO,sEAAT,IAAAA,SAAS,CAAT;EAOA,IAAI;IAAEE;EAAF,IAAgBhD,KAAK,CAACiD,UAAN,CAAiBd,iBAAjB,CAApB;EACA,IAAIkE,sBAAsB,GAAGrG,KAAK,CAACiD,UAAN,CAAiBhB,sBAAjB,CAA7B;EACA,IAAI;IAAEM,OAAO,EAAE+D;EAAX,IAA6BtG,KAAK,CAACiD,UAAN,CAAiBZ,YAAjB,CAAjC;EACA,IAAIyD,UAAU,GAAGQ,aAAa,CAACA,aAAa,CAACP,MAAd,GAAuB,CAAxB,CAA9B;EACA,IAAIQ,YAAY,GAAGT,UAAU,GAAGA,UAAU,CAACE,MAAd,GAAuB,EAApD;EACA,IAAIQ,cAAc,GAAGV,UAAU,GAAGA,UAAU,CAAC3C,QAAd,GAAyB,GAAxD;EACA,IAAIsD,kBAAkB,GAAGX,UAAU,GAAGA,UAAU,CAACrB,YAAd,GAA6B,GAAhE;EACA,IAAIiC,WAAW,GAAGZ,UAAU,IAAIA,UAAU,CAACa,KAA3C;EAEA,IAAanG,OAAA,CAAAC,GAAA,CAAAC,QAAA;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIkG,UAAU,GAAIF,WAAW,IAAIA,WAAW,CAACxB,IAA5B,IAAqC,EAAtD;IACA2B,WAAW,CACTL,cADS,EAET,CAACE,WAAD,IAAgBE,UAAU,CAACE,QAAX,CAAoB,GAApB,CAFP,EAGT,2EACMN,cADN,gCAC6CI,UAD7C,kPAK2CA,UAL3C,qCAMWA,UAAU,KAAK,GAAf,GAAqB,GAArB,GAA8BA,UAA9B,OANX,WAHS,CAAX;EAWD;EAED,IAAIG,mBAAmB,GAAGtD,WAAW,EAArC;EAEA,IAAIC,QAAJ;EACA,IAAI0C,WAAJ,EAAiB;IAAA,IAAAY,qBAAA;IACf,IAAIC,iBAAiB,GACnB,OAAOb,WAAP,KAAuB,QAAvB,GAAkCc,SAAS,CAACd,WAAD,CAA3C,GAA2DA,WAD7D;IAGA,EACEK,kBAAkB,KAAK,GAAvB,KACE,CAAAO,qBAAA,GAAAC,iBAAiB,CAAC9D,QADpB,KACE,gBAAA6D,qBAAA,CAA4BG,UAA5B,CAAuCV,kBAAvC,CADF,CADF,IAAAjG,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,QAGP,2FAEiE,0JAAA2D,kBAFjE,GAGmB,8BAAAQ,iBAAiB,CAAC9D,QAHrC,GAHO,wCAAT,GAAAL,SAAS,CAAT;IASAY,QAAQ,GAAGuD,iBAAX;EACD,CAdD,MAcO;IACLvD,QAAQ,GAAGqD,mBAAX;EACD;EAED,IAAI5D,QAAQ,GAAGO,QAAQ,CAACP,QAAT,IAAqB,GAApC;EACA,IAAIiE,iBAAiB,GACnBX,kBAAkB,KAAK,GAAvB,GACItD,QADJ,GAEIA,QAAQ,CAACkE,KAAT,CAAeZ,kBAAkB,CAACV,MAAlC,KAA6C,GAHnD;EAKA,IAAIxD,OAAO,GAAG+E,WAAW,CAACnB,MAAD,EAAS;IAAEhD,QAAQ,EAAEiE;EAAZ,CAAT,CAAzB;EAEA,IAAa5G,OAAA,CAAAC,GAAA,CAAAC,QAAA;IACXF,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAsE,OAAO,CACL0B,WAAW,IAAInE,OAAO,IAAI,IADrB,EAE0B,kCAAAmB,QAAQ,CAACP,QAFnC,GAE8CO,QAAQ,CAACN,MAFvD,GAEgEM,QAAQ,CAACR,IAFzE,GAAP;IAKA1C,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAsE,OAAO,CACLzC,OAAO,IAAI,IAAX,IACEA,OAAO,CAACA,OAAO,CAACwD,MAAR,GAAiB,CAAlB,CAAP,CAA4BY,KAA5B,CAAkCY,OAAlC,KAA8CC,SAF3C,EAGL,mCAAmC,GAAA9D,QAAQ,CAACP,QAA5C,GAAuDO,QAAQ,CAACN,MAAhE,GAAyEM,QAAQ,CAACR,IAAlF,2IAHK,CAAP;EAMD;EAED,IAAIuE,eAAe,GAAGC,cAAc,CAClCnF,OAAO,IACLA,OAAO,CAACgC,GAAR,CAAaC,KAAD,IACV7E,MAAM,CAACgI,MAAP,CAAc,EAAd,EAAkBnD,KAAlB,EAAyB;IACvBwB,MAAM,EAAErG,MAAM,CAACgI,MAAP,CAAc,EAAd,EAAkBpB,YAAlB,EAAgC/B,KAAK,CAACwB,MAAtC,CADe;IAEvB7C,QAAQ,EAAEI,SAAS,CAAC,CAClBkD,kBADkB;IAAA;IAGlBzD,SAAS,CAAC4E,cAAV,GACI5E,SAAS,CAAC4E,cAAV,CAAyBpD,KAAK,CAACrB,QAA/B,EAAyCA,QAD7C,GAEIqB,KAAK,CAACrB,QALQ,CAAD,CAFI;IASvBsB,YAAY,EACVD,KAAK,CAACC,YAAN,KAAuB,GAAvB,GACIgC,kBADJ,GAEIlD,SAAS,CAAC,CACRkD,kBADQ;IAAA;IAGRzD,SAAS,CAAC4E,cAAV,GACI5E,SAAS,CAAC4E,cAAV,CAAyBpD,KAAK,CAACC,YAA/B,EAA6CtB,QADjD,GAEIqB,KAAK,CAACC,YALF,CAAD;GAZjB,CADF,CAFgC,EAwBlC6B,aAxBkC,EAyBlCD,sBAAsB,IAAImB,SAzBQ,CAApC,CA/F2B;EA4H3B;EACA;;EACA,IAAIpB,WAAW,IAAIqB,eAAnB,EAAoC;IAClC,oBACEzH,KAAA,CAAA0B,aAAA,CAACU,eAAD,CAAiBwD,QAAjB;MACE/E,KAAK,EAAE;QACL6C,QAAQ,EAAAmE,QAAA;UACN1E,QAAQ,EAAE,GADJ;UAENC,MAAM,EAAE,EAFF;UAGNF,IAAI,EAAE,EAHA;UAINqC,KAAK,EAAE,IAJD;UAKNuC,GAAG,EAAE;QALC,GAMHpE,QANG,CADH;QASLE,cAAc,EAAEmE,MAAc,CAACC;MAT1B;IADT,GAaGP,eAbH,CADF;EAiBD;EAED,OAAOA,eAAP;AACD;AAED,SAASQ,mBAATA,CAAA,EAA+B;EAC7B,IAAIrH,KAAK,GAAGsH,aAAa,EAAzB;EACA,IAAIC,OAAO,GAAGC,oBAAoB,CAACxH,KAAD,CAApB,GACPA,KAAK,CAACyH,MADC,GACS,MAAAzH,KAAK,CAAC0H,UADf,GAEV1H,KAAK,YAAY2H,KAAjB,GACA3H,KAAK,CAACuH,OADN,GAEA/D,IAAI,CAACC,SAAL,CAAezD,KAAf,CAJJ;EAKA,IAAI4H,KAAK,GAAG5H,KAAK,YAAY2H,KAAjB,GAAyB3H,KAAK,CAAC4H,KAA/B,GAAuC,IAAnD;EACA,IAAIC,SAAS,GAAG,wBAAhB;EACA,IAAIC,SAAS,GAAG;IAAEC,OAAO,EAAE,QAAX;IAAqBC,eAAe,EAAEH;GAAtD;EACA,IAAII,UAAU,GAAG;IAAEF,OAAO,EAAE,SAAX;IAAsBC,eAAe,EAAEH;GAAxD;EAEA,IAAIK,OAAO,GAAG,IAAd;EACA,IAAatI,OAAA,CAAAC,GAAA,CAAAC,QAAA;IACXoI,OAAO,gBACL9I,KAAA,CAAA0B,aAAA,CAAA1B,KAAA,CAAA+I,QAAA,qBACE/I,KADF,CAAA0B,aAAA,qEAEE1B,KAGE,CAAA0B,aAAA,4HAAA1B,KAAA,CAAA0B,aAAA;MAAMsH,KAAK,EAAEH;IAAb,kBAHF,EAIE,8BAAA7I,KAAA,CAAA0B,aAAA;MAAMsH,KAAK,EAAEH;IAAb,aAJF,CAFF,CADF;EAWD;EAED,oBACE7I,KAAA,CAAA0B,aAAA,CAAA1B,KAAA,CAAA+I,QAAA,qBACE/I,KAAA,CAAA0B,aAAA,6CADF,eAEE1B,KAAA,CAAA0B,aAAA;IAAIsH,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAb;EAAX,GAAqCd,OAArC,CAFF,EAGGK,KAAK,gBAAGxI,KAAA,CAAA0B,aAAA;IAAKsH,KAAK,EAAEN;EAAZ,GAAwBF,KAAxB,CAAH,GAA0C,IAHlD,EAIGM,OAJH,CADF;AAQD;AAcM,MAAMI,mBAAN,SAAkClJ,KAAK,CAACmJ,SAAxC,CAGL;EACAC,WAAWA,CAACC,KAAD,EAAkC;IAC3C,MAAMA,KAAN;IACA,KAAK9D,KAAL,GAAa;MACX7B,QAAQ,EAAE2F,KAAK,CAAC3F,QADL;MAEX9C,KAAK,EAAEyI,KAAK,CAACzI;KAFf;EAID;EAE8B,OAAxB0I,wBAAwBA,CAAC1I,KAAD,EAAa;IAC1C,OAAO;MAAEA,KAAK,EAAEA;KAAhB;EACD;EAE8B,OAAxB2I,wBAAwBA,CAC7BF,KAD6B,EAE7B9D,KAF6B,EAG7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIA,KAAK,CAAC7B,QAAN,KAAmB2F,KAAK,CAAC3F,QAA7B,EAAuC;MACrC,OAAO;QACL9C,KAAK,EAAEyI,KAAK,CAACzI,KADR;QAEL8C,QAAQ,EAAE2F,KAAK,CAAC3F;OAFlB;IAID,CAdD;IAiBA;IACA;IACA;;IACA,OAAO;MACL9C,KAAK,EAAEyI,KAAK,CAACzI,KAAN,IAAe2E,KAAK,CAAC3E,KADvB;MAEL8C,QAAQ,EAAE6B,KAAK,CAAC7B;KAFlB;EAID;EAED8F,iBAAiBA,CAAC5I,KAAD,EAAa6I,SAAb,EAA6B;IAC5C9I,OAAO,CAACC,KAAR,CACE,uDADF,EAEEA,KAFF,EAGE6I,SAHF;EAKD;EAEDC,MAAMA,CAAA,EAAG;IACP,OAAO,KAAKnE,KAAL,CAAW3E,KAAX,gBACLZ,KAAC,CAAA0B,aAAA,CAAAW,YAAD,CAAcuD,QAAd;MAAuB/E,KAAK,EAAE,IAAK,CAAAwI,KAAL,CAAWM;KACvC,eAAA3J,KAAA,CAAA0B,aAAA,CAACc,iBAAD,CAAmBoD,QAAnB;MACE/E,KAAK,EAAE,KAAK0E,KAAL,CAAW3E,KADpB;MAEEgJ,QAAQ,EAAE,IAAK,CAAAP,KAAL,CAAWQ;IAFvB,EADF,CADK,GAQL,IAAK,CAAAR,KAAL,CAAWO,QARb;EAUD;AA7DD;AAsEF,SAASE,aAATA,CAA8EC,IAAA;EAAA,IAAvD;IAAEJ,YAAF;IAAgBnF,KAAhB;IAAuBoF;GAAgC,GAAAG,IAAA;EAC5E,IAAIC,iBAAiB,GAAGhK,KAAK,CAACiD,UAAN,CAAiBnB,iBAAjB,CAAxB,CAD4E;EAI5E;;EACA,IACEkI,iBAAiB,IACjBA,iBAAiB,CAACC,MADlB,IAEAD,iBAAiB,CAACE,aAFlB,IAGA1F,KAAK,CAACmC,KAAN,CAAYwD,YAJd,EAKE;IACAH,iBAAiB,CAACE,aAAlB,CAAgCE,0BAAhC,GAA6D5F,KAAK,CAACmC,KAAN,CAAY0D,EAAzE;EACD;EAED,oBACErK,KAAA,CAAA0B,aAAA,CAACW,YAAD,CAAcuD,QAAd;IAAuB/E,KAAK,EAAE8I;EAA9B,GACGC,QADH,CADF;AAKD;AAEM,SAASlC,cAATA,CACLnF,OADK,EAEL+D,aAFK,EAGLgE,eAHK,EAIsB;EAAA,IAF3BhE,aAE2B;IAF3BA,aAE2B,GAFG,EAEH;EAAA;EAC3B,IAAI/D,OAAO,IAAI,IAAf,EAAqB;IACnB,IAAI+H,eAAJ,YAAIA,eAAe,CAAEC,MAArB,EAA6B;MAC3B;MACA;MACAhI,OAAO,GAAG+H,eAAe,CAAC/H,OAA1B;IACD,CAJD,MAIO;MACL,OAAO,IAAP;IACD;EACF;EAED,IAAIkF,eAAe,GAAGlF,OAAtB,CAX2B;;EAc3B,IAAIgI,MAAM,GAAGD,eAAH,IAAG,gBAAAA,eAAe,CAAEC,MAA9B;EACA,IAAIA,MAAM,IAAI,IAAd,EAAoB;IAClB,IAAIC,UAAU,GAAG/C,eAAe,CAACgD,SAAhB,CACdC,CAAD,IAAOA,CAAC,CAAC/D,KAAF,CAAQ0D,EAAR,KAAcE,MAAd,IAAc,gBAAAA,MAAM,CAAGG,CAAC,CAAC/D,KAAF,CAAQ0D,EAAX,CAApB,CADQ,CAAjB;IAGA,EACEG,UAAU,IAAI,CADhB,IAAAhK,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,qEAEoDyH,MAFpD,CAAT,GAAAzH,SAAS,CAAT;IAIA2E,eAAe,GAAGA,eAAe,CAACJ,KAAhB,CAChB,CADgB,EAEhBsD,IAAI,CAACC,GAAL,CAASnD,eAAe,CAAC1B,MAAzB,EAAiCyE,UAAU,GAAG,CAA9C,CAFgB,CAAlB;EAID;EAED,OAAO/C,eAAe,CAACoD,WAAhB,CAA4B,CAACvI,MAAD,EAASkC,KAAT,EAAgBsG,KAAhB,KAA0B;IAC3D,IAAIlK,KAAK,GAAG4D,KAAK,CAACmC,KAAN,CAAY0D,EAAZ,GAAiBE,MAAjB,oBAAiBA,MAAM,CAAG/F,KAAK,CAACmC,KAAN,CAAY0D,EAAf,CAAvB,GAA4C,IAAxD,CAD2D;;IAG3D,IAAIF,YAAY,GAAGG,eAAe,GAC9B9F,KAAK,CAACmC,KAAN,CAAYwD,YAAZ,iBAA4BnK,KAAA,CAAA0B,aAAA,CAACuG,mBAAD,OADE,GAE9B,IAFJ;IAGA,IAAI1F,OAAO,GAAG+D,aAAa,CAACyE,MAAd,CAAqBtD,eAAe,CAACJ,KAAhB,CAAsB,CAAtB,EAAyByD,KAAK,GAAG,CAAjC,CAArB,CAAd;IACA,IAAIE,WAAW,GAAGA,CAAA,kBAChBhL,KAAA,CAAA0B,aAAA,CAACoI,aAAD;MAAetF,KAAK,EAAEA,KAAtB;MAA6BmF,YAAY,EAAE;QAAErH,MAAF;QAAUC;MAAV;KACxC,EAAA3B,KAAK,GACFuJ,YADE,GAEF3F,KAAK,CAACmC,KAAN,CAAYY,OAAZ,KAAwBC,SAAxB,GACAhD,KAAK,CAACmC,KAAN,CAAYY,OADZ,GAEAjF,MALN,CADF,CAP2D;IAiB3D;IACA;;IACA,OAAOgI,eAAe,KAAK9F,KAAK,CAACmC,KAAN,CAAYwD,YAAZ,IAA4BW,KAAK,KAAK,CAA3C,CAAf,gBACL9K,KAAA,CAAA0B,aAAA,CAACwH,mBAAD;MACExF,QAAQ,EAAE4G,eAAe,CAAC5G,QAD5B;MAEEmG,SAAS,EAAEM,YAFb;MAGEvJ,KAAK,EAAEA,KAHT;MAIEgJ,QAAQ,EAAEoB,WAAW,EAJvB;MAKErB,YAAY,EAAE;QAAErH,MAAM,EAAE,IAAV;QAAgBC;MAAhB;KANX,IASLyI,WAAW,EATb;GAnBK,EA8BJ,IA9BI,CAAP;AA+BD;IAEIC,cAAA;WAAAA,cAAA;EAAAA,cAAA;EAAAA,cAAA;AAAA,GAAAA,cAAA,KAAAA,cAAA;IAKAC,mBAAA;WAAAA,mBAAA;EAAAA,mBAAA;EAAAA,mBAAA;EAAAA,mBAAA;EAAAA,mBAAA;EAAAA,mBAAA;EAAAA,mBAAA;EAAAA,mBAAA;AAAA,GAAAA,mBAAA,KAAAA,mBAAA;AAUL,SAASC,yBAATA,CACEC,QADF,EAEE;EACA,OAAUA,QAAV;AACD;AAED,SAASC,oBAATA,CAA8BD,QAA9B,EAAwD;EACtD,IAAIE,GAAG,GAAGtL,KAAK,CAACiD,UAAN,CAAiBnB,iBAAjB,CAAV;EACA,CAAUwJ,GAAV,GAAA9K,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAAM,OAAAqI,yBAAyB,CAACC,QAAD,CAA/B,CAAT,GAAAtI,SAAS,CAAT;EACA,OAAOwI,GAAP;AACD;AAED,SAASC,kBAATA,CAA4BH,QAA5B,EAA2D;EACzD,IAAI7F,KAAK,GAAGvF,KAAK,CAACiD,UAAN,CAAiBhB,sBAAjB,CAAZ;EACA,CAAUsD,KAAV,GAAA/E,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAAQ,OAAAqI,yBAAyB,CAACC,QAAD,CAAjC,CAAT,GAAAtI,SAAS,CAAT;EACA,OAAOyC,KAAP;AACD;AAED,SAASiG,eAATA,CAAyBJ,QAAzB,EAAwD;EACtD,IAAIzE,KAAK,GAAG3G,KAAK,CAACiD,UAAN,CAAiBZ,YAAjB,CAAZ;EACA,CAAUsE,KAAV,GAAAnG,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAAQ,OAAAqI,yBAAyB,CAACC,QAAD,CAAjC,CAAT,GAAAtI,SAAS,CAAT;EACA,OAAO6D,KAAP;AACD;AAED,SAAS8E,iBAATA,CAA2BL,QAA3B,EAA0D;EACxD,IAAIzE,KAAK,GAAG6E,eAAe,CAACJ,QAAD,CAA3B;EACA,IAAIM,SAAS,GAAG/E,KAAK,CAACpE,OAAN,CAAcoE,KAAK,CAACpE,OAAN,CAAcwD,MAAd,GAAuB,CAArC,CAAhB;EACA,CACE2F,SAAS,CAAC/E,KAAV,CAAgB0D,EADlB,GAAA7J,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,QAEJsI,QAFI,8DAAT,GAAAtI,SAAS,CAAT;EAIA,OAAO4I,SAAS,CAAC/E,KAAV,CAAgB0D,EAAvB;AACD;AAED;AACA;AACA;AACA;;AACO,SAASsB,aAATA,CAAA,EAAyB;EAC9B,IAAIpG,KAAK,GAAGgG,kBAAkB,CAACL,mBAAmB,CAACU,aAArB,CAA9B;EACA,OAAOrG,KAAK,CAACsG,UAAb;AACD;AAED;AACA;AACA;AACA;;AACO,SAASC,cAATA,CAAA,EAA0B;EAC/B,IAAI9B,iBAAiB,GAAGqB,oBAAoB,CAACJ,cAAc,CAACc,cAAhB,CAA5C;EACA,IAAIxG,KAAK,GAAGgG,kBAAkB,CAACL,mBAAmB,CAACa,cAArB,CAA9B;EACA,OAAO;IACLC,UAAU,EAAEhC,iBAAiB,CAACiC,MAAlB,CAAyBD,UADhC;IAELzG,KAAK,EAAEA,KAAK,CAAC2G;GAFf;AAID;AAED;AACA;AACA;AACA;;AACO,SAASC,UAATA,CAAA,EAAsB;EAC3B,IAAI;IAAE5J,OAAF;IAAW6J;EAAX,IAA0Bb,kBAAkB,CAC9CL,mBAAmB,CAACmB,UAD0B,CAAhD;EAGA,OAAOrM,KAAK,CAAC+D,OAAN,CACL,MACExB,OAAO,CAACgC,GAAR,CAAaC,KAAD,IAAW;IACrB,IAAI;MAAErB,QAAF;MAAY6C;KAAW,GAAAxB,KAA3B,CADqB;IAGrB;IACA;;IACA,OAAO;MACL6F,EAAE,EAAE7F,KAAK,CAACmC,KAAN,CAAY0D,EADX;MAELlH,QAFK;MAGL6C,MAHK;MAILsG,IAAI,EAAEF,UAAU,CAAC5H,KAAK,CAACmC,KAAN,CAAY0D,EAAb,CAJX;MAKLkC,MAAM,EAAE/H,KAAK,CAACmC,KAAN,CAAY4F;KALtB;EAOD,CAZD,CAFG,EAeL,CAAChK,OAAD,EAAU6J,UAAV,CAfK,CAAP;AAiBD;AAED;AACA;AACA;;AACO,SAASI,aAATA,CAAA,EAAkC;EACvC,IAAIjH,KAAK,GAAGgG,kBAAkB,CAACL,mBAAmB,CAACuB,aAArB,CAA9B;EACA,IAAIC,OAAO,GAAGjB,iBAAiB,CAACP,mBAAmB,CAACuB,aAArB,CAA/B;EAEA,IAAIlH,KAAK,CAACgF,MAAN,IAAgBhF,KAAK,CAACgF,MAAN,CAAamC,OAAb,CAAyB,QAA7C,EAAmD;IACjD/L,OAAO,CAACC,KAAR,8DAC+D8L,OAD/D;IAGA,OAAOlF,SAAP;EACD;EACD,OAAOjC,KAAK,CAAC6G,UAAN,CAAiBM,OAAjB,CAAP;AACD;AAED;AACA;AACA;;AACO,SAASC,kBAATA,CAA4BD,OAA5B,EAAsD;EAC3D,IAAInH,KAAK,GAAGgG,kBAAkB,CAACL,mBAAmB,CAAC0B,kBAArB,CAA9B;EACA,OAAOrH,KAAK,CAAC6G,UAAN,CAAiBM,OAAjB,CAAP;AACD;AAED;AACA;AACA;;AACO,SAASG,aAATA,CAAA,EAAkC;EACvC,IAAItH,KAAK,GAAGgG,kBAAkB,CAACL,mBAAmB,CAAC4B,aAArB,CAA9B;EAEA,IAAInG,KAAK,GAAG3G,KAAK,CAACiD,UAAN,CAAiBZ,YAAjB,CAAZ;EACA,CAAUsE,KAAV,GAAAnG,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAAT,6DAAAA,SAAS,CAAT;EAEA,OAAOnD,MAAM,CAACoN,MAAP,CAAc,CAAAxH,KAAK,QAAL,YAAAA,KAAK,CAAEyH,UAAP,KAAqB,EAAnC,EAAuC,CAAvC,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;;AACO,SAAS9E,aAATA,CAAA,EAAkC;EAAA,IAAA+E,aAAA;EACvC,IAAIrM,KAAK,GAAGZ,KAAK,CAACiD,UAAN,CAAiBT,iBAAjB,CAAZ;EACA,IAAI+C,KAAK,GAAGgG,kBAAkB,CAACL,mBAAmB,CAACgC,aAArB,CAA9B;EACA,IAAIR,OAAO,GAAGjB,iBAAiB,CAACP,mBAAmB,CAACgC,aAArB,CAA/B,CAHuC;EAMvC;;EACA,IAAItM,KAAJ,EAAW;IACT,OAAOA,KAAP;EACD,CATsC;;EAYvC,QAAAqM,aAAA,GAAO1H,KAAK,CAACgF,MAAb,KAAO,gBAAA0C,aAAA,CAAeP,OAAf,CAAP;AACD;AAED;AACA;AACA;;AACO,SAASS,aAATA,CAAA,EAAkC;EACvC,IAAItM,KAAK,GAAGb,KAAK,CAACiD,UAAN,CAAiBf,YAAjB,CAAZ;EACA,OAAOrB,KAAP,oBAAOA,KAAK,CAAEuM,KAAd;AACD;AAED;AACA;AACA;;AACO,SAASC,aAATA,CAAA,EAAkC;EACvC,IAAIxM,KAAK,GAAGb,KAAK,CAACiD,UAAN,CAAiBf,YAAjB,CAAZ;EACA,OAAOrB,KAAP,oBAAOA,KAAK,CAAEyM,MAAd;AACD;AAED,IAAIC,SAAS,GAAG,CAAhB;AAEA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASC,UAATA,CAAoBC,WAApB,EAAqE;EAC1E,IAAI;IAAExB;EAAF,IAAaZ,oBAAoB,CAACJ,cAAc,CAACyC,UAAhB,CAArC;EACA,IAAI,CAACC,UAAD,CAAe,GAAA3N,KAAK,CAACJ,QAAN,CAAe,MAAMgO,MAAM,CAAC,EAAEL,SAAH,CAA3B,CAAnB;EAEA,IAAIM,eAAe,GAAG7N,KAAK,CAAC8E,WAAN,CACnBgJ,IAAD,IAAU;IACR,OAAO,OAAOL,WAAP,KAAuB,UAAvB,GACH,CAAC,CAACA,WAAW,CAACK,IAAD,CADV,GAEH,CAAC,CAACL,WAFN;EAGD,CALmB,EAMpB,CAACA,WAAD,CANoB,CAAtB;EASA,IAAIM,OAAO,GAAG9B,MAAM,CAAC+B,UAAP,CAAkBL,UAAlB,EAA8BE,eAA9B,CAAd,CAb0E;;EAgB1E7N,KAAK,CAACH,SAAN,CACE,MAAM,MAAMoM,MAAM,CAACgC,aAAP,CAAqBN,UAArB,CADd,EAEE,CAAC1B,MAAD,EAAS0B,UAAT,CAFF;EAKA,OAAOI,OAAP;AACD;AAED,MAAMG,aAAsC,GAAG,EAA/C;AAEA,SAASrH,WAATA,CAAqBiB,GAArB,EAAkCqG,IAAlC,EAAiDhG,OAAjD,EAAkE;EAChE,IAAI,CAACgG,IAAD,IAAS,CAACD,aAAa,CAACpG,GAAD,CAA3B,EAAkC;IAChCoG,aAAa,CAACpG,GAAD,CAAb,GAAqB,IAArB;IACAtH,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAsE,OAAO,CAAC,KAAD,EAAQmD,OAAR,CAAP;EACD;AACF;;ACrzBD;AACA;AACA;AACO,SAASiG,cAATA,CAGqCrE,IAAA;EAAA,IAHb;IAC7BsE,eAD6B;IAE7BpC;GAC0C,GAAAlC,IAAA;EAC1C;EACA,IAAIxE,KAAkB,GAAGnF,oBAAwB,CAC/C6L,MAAM,CAAC5L,SADwC,EAE/C,MAAM4L,MAAM,CAAC1G,KAFkC;EAAA;EAI/C;EACA;EACA,MAAM0G,MAAM,CAAC1G,KANkC,CAAjD;EASA,IAAIvC,SAAS,GAAGhD,KAAK,CAAC+D,OAAN,CAAc,MAAiB;IAC7C,OAAO;MACLP,UAAU,EAAEyI,MAAM,CAACzI,UADd;MAELoE,cAAc,EAAEqE,MAAM,CAACrE,cAFlB;MAGL3C,EAAE,EAAGqJ,CAAD,IAAOrC,MAAM,CAACpH,QAAP,CAAgByJ,CAAhB,CAHN;MAILhJ,IAAI,EAAEA,CAAC5C,EAAD,EAAK6C,KAAL,EAAYgJ,IAAZ,KACJtC,MAAM,CAACpH,QAAP,CAAgBnC,EAAhB,EAAoB;QAClB6C,KADkB;QAElBiJ,kBAAkB,EAAED,IAAF,IAAE,gBAAAA,IAAI,CAAEC;MAFR,CAApB,CALG;MASLnJ,OAAO,EAAEA,CAAC3C,EAAD,EAAK6C,KAAL,EAAYgJ,IAAZ,KACPtC,MAAM,CAACpH,QAAP,CAAgBnC,EAAhB,EAAoB;QAClB2C,OAAO,EAAE,IADS;QAElBE,KAFkB;QAGlBiJ,kBAAkB,EAAED,IAAF,IAAE,gBAAAA,IAAI,CAAEC;OAH5B;KAVJ;EAgBD,CAjBe,EAiBb,CAACvC,MAAD,CAjBa,CAAhB;EAmBA,IAAIlJ,QAAQ,GAAGkJ,MAAM,CAAClJ,QAAP,IAAmB,GAAlC,CA9B0C;EAiC1C;EACA;EACA;EACA;EACA;;EACA,oBACE/C,KACE,CAAA0B,aAAA,CAAA1B,KAAA,CAAA+I,QAAA,qBAAA/I,KAAA,CAAA0B,aAAA,CAACI,iBAAD,CAAmB8D,QAAnB;IACE/E,KAAK,EAAE;MACLoL,MADK;MAELjJ,SAFK;MAGLiH,MAAM,EAAE,KAHH;MAIL;MACAlH;IALK;GAQP,eAAA/C,KAAA,CAAA0B,aAAA,CAACO,sBAAD,CAAwB2D,QAAxB;IAAiC/E,KAAK,EAAE0E;EAAxC,gBACEvF,KAAA,CAAA0B,aAAA,CAAC+M,MAAD;IACE1L,QAAQ,EAAEkJ,MAAM,CAAClJ,QADnB;IAEEW,QAAQ,EAAEuI,MAAM,CAAC1G,KAAP,CAAa7B,QAFzB;IAGEE,cAAc,EAAEqI,MAAM,CAAC1G,KAAP,CAAamJ,aAH/B;IAIE1L,SAAS,EAAEA;EAJb,GAMGiJ,MAAM,CAAC1G,KAAP,CAAaoJ,WAAb,gBAA2B3O,KAAC,CAAA0B,aAAA,CAAAkN,MAAD,EAA3B,QAAwCP,eAN3C,CADF,CATF,CADF,EAqBG,IArBH,CADF;AAyBD;;AASD;AACA;AACA;AACA;AACA;AACO,SAASQ,YAATA,CAKmCC,KAAA;EAAA,IALb;IAC3B/L,QAD2B;IAE3B6G,QAF2B;IAG3BmF,cAH2B;IAI3BC;GACwC,GAAAF,KAAA;EACxC,IAAIG,UAAU,GAAGjP,KAAK,CAAC2E,MAAN,EAAjB;EACA,IAAIsK,UAAU,CAACrK,OAAX,IAAsB,IAA1B,EAAgC;IAC9BqK,UAAU,CAACrK,OAAX,GAAqBsK,mBAAmB,CAAC;MACvCH,cADuC;MAEvCC,YAFuC;MAGvCG,QAAQ,EAAE;IAH6B,CAAD,CAAxC;EAKD;EAED,IAAIC,OAAO,GAAGH,UAAU,CAACrK,OAAzB;EACA,IAAI,CAACW,KAAD,EAAQ8J,QAAR,IAAoBrP,KAAK,CAACJ,QAAN,CAAe;IACrC0P,MAAM,EAAEF,OAAO,CAACE,MADqB;IAErC5L,QAAQ,EAAE0L,OAAO,CAAC1L;EAFmB,CAAf,CAAxB;EAKA1D,KAAK,CAACF,eAAN,CAAsB,MAAMsP,OAAO,CAACG,MAAR,CAAeF,QAAf,CAA5B,EAAsD,CAACD,OAAD,CAAtD;EAEA,oBACEpP,KAAA,CAAA0B,aAAA,CAAC+M,MAAD;IACE1L,QAAQ,EAAEA,QADZ;IAEE6G,QAAQ,EAAEA,QAFZ;IAGElG,QAAQ,EAAE6B,KAAK,CAAC7B,QAHlB;IAIEE,cAAc,EAAE2B,KAAK,CAAC+J,MAJxB;IAKEtM,SAAS,EAAEoM;GANf;AASD;;AASD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,QAATA,CAKiBC,KAAA;EAAA,IALC;IACvB/M,EADuB;IAEvB2C,OAFuB;IAGvBE,KAHuB;IAIvB3C;GACsB,GAAA6M,KAAA;EACtB,CACE5M,kBAAkB,EADpB,GAAArC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAEP;EAAA;EACA;EAHO,qEAAT,IAAAA,SAAS,CAAT;EAOAtC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAsE,OAAO,CACL,CAAChF,KAAK,CAACiD,UAAN,CAAiBd,iBAAjB,CAAoC,CAAA8H,MADhC,EAEL,iOAFK,CAAP;EAOA,IAAIK,eAAe,GAAGtK,KAAK,CAACiD,UAAN,CAAiBhB,sBAAjB,CAAtB;EACA,IAAI4C,QAAQ,GAAGZ,WAAW,EAA1B;EAEAjE,KAAK,CAACH,SAAN,CAAgB,MAAM;IACpB;IACA;IACA;IACA,IAAIyK,eAAe,IAAIA,eAAe,CAACuB,UAAhB,CAA2BtG,KAA3B,KAAqC,MAA5D,EAAoE;MAClE;IACD;IACDV,QAAQ,CAACnC,EAAD,EAAK;MAAE2C,OAAF;MAAWE,KAAX;MAAkB3C;IAAlB,CAAL,CAAR;GAPF;EAUA,OAAO,IAAP;AACD;;AAMD;AACA;AACA;AACA;AACA;AACO,SAAS8M,MAATA,CAAgBrG,KAAhB,EAA+D;EACpE,OAAO3D,SAAS,CAAC2D,KAAK,CAAC1D,OAAP,CAAhB;AACD;;AAoCD;AACA;AACA;AACA;AACA;AACO,SAASgK,KAATA,CAAeC,MAAf,EAA8D;0CACnE9M,SAAS,QAEP,2IAFO,CAAT,GAAAA,SAAS,CAAT;AAKD;;AAWD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS2L,MAATA,CAOoCoB,KAAA;EAAA,IAPpB;IACrB9M,QAAQ,EAAE+M,YAAY,GAAG,GADJ;IAErBlG,QAAQ,GAAG,IAFU;IAGrBlG,QAAQ,EAAEqM,YAHW;IAIrBnM,cAAc,GAAGmE,MAAc,CAACC,GAJX;IAKrBhF,SALqB;IAMrBiH,MAAM,EAAE+F,UAAU,GAAG;GACoB,GAAAH,KAAA;EACzC,CACE,CAAChN,kBAAkB,EADrB,GAAArC,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,QAEP,uDAFO,uDAAT,GAAAA,SAAS,CAAT,gBADyC;EAQzC;;EACA,IAAIC,QAAQ,GAAG+M,YAAY,CAACzK,OAAb,CAAqB,MAArB,EAA6B,GAA7B,CAAf;EACA,IAAI4K,iBAAiB,GAAGjQ,KAAK,CAAC+D,OAAN,CACtB,OAAO;IAAEhB,QAAF;IAAYC,SAAZ;IAAuBiH,MAAM,EAAE+F;GAAtC,CADsB,EAEtB,CAACjN,QAAD,EAAWC,SAAX,EAAsBgN,UAAtB,CAFsB,CAAxB;EAKA,IAAI,OAAOD,YAAP,KAAwB,QAA5B,EAAsC;IACpCA,YAAY,GAAG7I,SAAS,CAAC6I,YAAD,CAAxB;EACD;EAED,IAAI;IACF5M,QAAQ,GAAG,GADT;IAEFC,MAAM,GAAG,EAFP;IAGFF,IAAI,GAAG,EAHL;IAIFqC,KAAK,GAAG,IAJN;IAKFuC,GAAG,GAAG;EALJ,IAMAiI,YANJ;EAQA,IAAIrM,QAAQ,GAAG1D,KAAK,CAAC+D,OAAN,CAAc,MAAM;IACjC,IAAImM,gBAAgB,GAAGC,aAAa,CAAChN,QAAD,EAAWJ,QAAX,CAApC;IAEA,IAAImN,gBAAgB,IAAI,IAAxB,EAA8B;MAC5B,OAAO,IAAP;IACD;IAED,OAAO;MACL/M,QAAQ,EAAE+M,gBADL;MAEL9M,MAFK;MAGLF,IAHK;MAILqC,KAJK;MAKLuC;KALF;EAOD,CAdc,EAcZ,CAAC/E,QAAD,EAAWI,QAAX,EAAqBC,MAArB,EAA6BF,IAA7B,EAAmCqC,KAAnC,EAA0CuC,GAA1C,CAdY,CAAf;EAgBAtH,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAsE,OAAO,CACLtB,QAAQ,IAAI,IADP,EAEL,wBAAqBX,QAArB,iDACMI,QADN,GACiBC,MADjB,GAC0BF,IAD1B,iGAFK,CAAP;EAOA,IAAIQ,QAAQ,IAAI,IAAhB,EAAsB;IACpB,OAAO,IAAP;EACD;EAED,oBACE1D,KAAA,CAAA0B,aAAA,CAACS,iBAAD,CAAmByD,QAAnB;IAA4B/E,KAAK,EAAEoP;GACjC,eAAAjQ,KAAA,CAAA0B,aAAA,CAACU,eAAD,CAAiBwD,QAAjB;IACEgE,QAAQ,EAAEA,QADZ;IAEE/I,KAAK,EAAE;MAAE6C,QAAF;MAAYE;IAAZ;EAFT,EADF,CADF;AAQD;;AAOD;AACA;AACA;AACA;AACA;AACA;AACO,SAASgL,MAATA,CAGoCwB,KAAA;EAAA,IAHpB;IACrBxG,QADqB;IAErBlG;GACyC,GAAA0M,KAAA;EACzC,IAAIpG,iBAAiB,GAAGhK,KAAK,CAACiD,UAAN,CAAiBnB,iBAAjB,CAAxB,CADyC;EAGzC;EACA;;EACA,IAAIqE,MAAM,GACR6D,iBAAiB,IAAI,CAACJ,QAAtB,GACKI,iBAAiB,CAACiC,MAAlB,CAAyB9F,MAD9B,GAEIkK,wBAAwB,CAACzG,QAAD,CAH9B;EAIA,OAAO1D,SAAS,CAACC,MAAD,EAASzC,QAAT,CAAhB;AACD;;AAYD;AACA;AACA;AACA;AACO,SAAS4M,KAATA,CAAgEC,KAAA;EAAA,IAAjD;IAAE3G,QAAF;IAAYO,YAAZ;IAA0BqG;GAAuB,GAAAD,KAAA;EACrE,oBACEvQ,KAAA,CAAA0B,aAAA,CAAC+O,kBAAD;IAAoBD,OAAO,EAAEA,OAA7B;IAAsCrG,YAAY,EAAEA;EAApD,gBACEnK,KAAC,CAAA0B,aAAA,CAAAgP,YAAD,EAAe,MAAA9G,QAAf,CADF,CADF;AAKD;IAWI+G,iBAAA;WAAAA,iBAAA;EAAAA,iBAAA,CAAAA,iBAAA;EAAAA,iBAAA,CAAAA,iBAAA;EAAAA,iBAAA,CAAAA,iBAAA;AAAA,GAAAA,iBAAA,KAAAA,iBAAA;AAML,MAAMC,mBAAmB,GAAG,IAAIC,OAAJ,CAAY,MAAM,EAAlB,CAA5B;AAEA,MAAMJ,kBAAN,SAAiCzQ,KAAK,CAACmJ,SAAvC,CAGE;EACAC,WAAWA,CAACC,KAAD,EAAiC;IAC1C,MAAMA,KAAN;IACA,KAAK9D,KAAL,GAAa;MAAE3E,KAAK,EAAE;KAAtB;EACD;EAE8B,OAAxB0I,wBAAwBA,CAAC1I,KAAD,EAAa;IAC1C,OAAO;MAAEA;KAAT;EACD;EAED4I,iBAAiBA,CAAC5I,KAAD,EAAa6I,SAAb,EAA6B;IAC5C9I,OAAO,CAACC,KAAR,CACE,kDADF,EAEEA,KAFF,EAGE6I,SAHF;EAKD;EAEDC,MAAMA,CAAA,EAAG;IACP,IAAI;MAAEE,QAAF;MAAYO,YAAZ;MAA0BqG;IAA1B,IAAsC,KAAKnH,KAA/C;IAEA,IAAIyH,OAA8B,GAAG,IAArC;IACA,IAAIzI,MAAyB,GAAGsI,iBAAiB,CAACI,OAAlD;IAEA,IAAI,EAAEP,OAAO,YAAYK,OAArB,CAAJ,EAAmC;MACjC;MACAxI,MAAM,GAAGsI,iBAAiB,CAACK,OAA3B;MACAF,OAAO,GAAGD,OAAO,CAACL,OAAR,EAAV;MACA7Q,MAAM,CAACsR,cAAP,CAAsBH,OAAtB,EAA+B,UAA/B,EAA2C;QAAEI,GAAG,EAAEA,CAAA,KAAM;OAAxD;MACAvR,MAAM,CAACsR,cAAP,CAAsBH,OAAtB,EAA+B,OAA/B,EAAwC;QAAEI,GAAG,EAAEA,CAAA,KAAMV;OAArD;IACD,CAND,MAMO,IAAI,KAAKjL,KAAL,CAAW3E,KAAf,EAAsB;MAC3B;MACAyH,MAAM,GAAGsI,iBAAiB,CAAC/P,KAA3B;MACA,IAAIuQ,WAAW,GAAG,IAAK,CAAA5L,KAAL,CAAW3E,KAA7B;MACAkQ,OAAO,GAAGD,OAAO,CAACO,MAAR,EAAiB,CAAAC,KAAjB,CAAuB,MAAM,EAA7B,CAAV,CAJ2B;;MAK3B1R,MAAM,CAACsR,cAAP,CAAsBH,OAAtB,EAA+B,UAA/B,EAA2C;QAAEI,GAAG,EAAEA,CAAA,KAAM;OAAxD;MACAvR,MAAM,CAACsR,cAAP,CAAsBH,OAAtB,EAA+B,QAA/B,EAAyC;QAAEI,GAAG,EAAEA,CAAA,KAAMC;OAAtD;IACD,CAPM,MAOA,IAAKX,OAAD,CAA4Bc,QAAhC,EAA0C;MAC/C;MACAR,OAAO,GAAGN,OAAV;MACAnI,MAAM,GACJyI,OAAO,CAACxD,MAAR,KAAmB9F,SAAnB,GACImJ,iBAAiB,CAAC/P,KADtB,GAEIkQ,OAAO,CAAC1D,KAAR,KAAkB5F,SAAlB,GACAmJ,iBAAiB,CAACK,OADlB,GAEAL,iBAAiB,CAACI,OALxB;IAMD,CATM,MASA;MACL;MACA1I,MAAM,GAAGsI,iBAAiB,CAACI,OAA3B;MACApR,MAAM,CAACsR,cAAP,CAAsBT,OAAtB,EAA+B,UAA/B,EAA2C;QAAEU,GAAG,EAAEA,CAAA,KAAM;OAAxD;MACAJ,OAAO,GAAGN,OAAO,CAACe,IAAR,CACPjF,IAAD,IACE3M,MAAM,CAACsR,cAAP,CAAsBT,OAAtB,EAA+B,OAA/B,EAAwC;QAAEU,GAAG,EAAEA,CAAA,KAAM5E;OAArD,CAFM,EAGP1L,KAAD,IACEjB,MAAM,CAACsR,cAAP,CAAsBT,OAAtB,EAA+B,QAA/B,EAAyC;QAAEU,GAAG,EAAEA,CAAA,KAAMtQ;MAAb,CAAzC,CAJM,CAAV;IAMD;IAED,IACEyH,MAAM,KAAKsI,iBAAiB,CAAC/P,KAA7B,IACAkQ,OAAO,CAACxD,MAAR,YAA0BkE,oBAF5B,EAGE;MACA;MACA,MAAMZ,mBAAN;IACD;IAED,IAAIvI,MAAM,KAAKsI,iBAAiB,CAAC/P,KAA7B,IAAsC,CAACuJ,YAA3C,EAAyD;MACvD;MACA,MAAM2G,OAAO,CAACxD,MAAd;IACD;IAED,IAAIjF,MAAM,KAAKsI,iBAAiB,CAAC/P,KAAjC,EAAwC;MACtC;MACA,oBAAOZ,KAAA,CAAA0B,aAAA,CAACQ,YAAD,CAAc0D,QAAd;QAAuB/E,KAAK,EAAEiQ,OAA9B;QAAuClH,QAAQ,EAAEO;OAAxD;IACD;IAED,IAAI9B,MAAM,KAAKsI,iBAAiB,CAACK,OAAjC,EAA0C;MACxC;MACA,oBAAOhR,KAAA,CAAA0B,aAAA,CAACQ,YAAD,CAAc0D,QAAd;QAAuB/E,KAAK,EAAEiQ,OAA9B;QAAuClH,QAAQ,EAAEA;OAAxD;IACD,CA7DM;;IAgEP,MAAMkH,OAAN;EACD;AAnFD;AAsFF;AACA;AACA;AACA;;AACA,SAASJ,YAATA,CAIGe,KAAA;EAAA,IAJmB;IACpB7H;GAGC,GAAA6H,KAAA;EACD,IAAInF,IAAI,GAAGa,aAAa,EAAxB;EACA,IAAIuE,QAAQ,GAAG,OAAO9H,QAAP,KAAoB,UAApB,GAAiCA,QAAQ,CAAC0C,IAAD,CAAzC,GAAkD1C,QAAjE;EACA,oBAAO5J,KAAA,CAAA0B,aAAA,CAAA1B,KAAA,CAAA+I,QAAA,QAAG2I,QAAH,CAAP;AACD;AAGD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACO,SAASrB,wBAATA,CACLzG,QADK,EAELhD,UAFK,EAGU;EAAA,IADfA,UACe;IADfA,UACe,GADQ,EACR;EAAA;EACf,IAAIT,MAAqB,GAAG,EAA5B;EAEAnG,KAAK,CAAC2R,QAAN,CAAeC,OAAf,CAAuBhI,QAAvB,EAAiC,CAACrC,OAAD,EAAUuD,KAAV,KAAoB;IACnD,IAAI,eAAC9K,KAAK,CAAC6R,cAAN,CAAqBtK,OAArB,CAAL,EAAoC;MAClC;MACA;MACA;IACD;IAED,IAAIA,OAAO,CAACuK,IAAR,KAAiB9R,KAAK,CAAC+I,QAA3B,EAAqC;MACnC;MACA5C,MAAM,CAACb,IAAP,CAAYyM,KAAZ,CACE5L,MADF,EAEEkK,wBAAwB,CAAC9I,OAAO,CAAC8B,KAAR,CAAcO,QAAf,EAAyBhD,UAAzB,CAF1B;MAIA;IACD;IAED,EACEW,OAAO,CAACuK,IAAR,KAAiBnC,KADnB,IAAAnP,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,CAGL,qBAAOyE,OAAO,CAACuK,IAAf,KAAwB,QAAxB,GAAmCvK,OAAO,CAACuK,IAA3C,GAAkDvK,OAAO,CAACuK,IAAR,CAAaE,IAH1D,6GAAT,GAAAlP,SAAS,CAAT;IAOA,EACE,CAACyE,OAAO,CAAC8B,KAAR,CAAcyB,KAAf,IAAwB,CAACvD,OAAO,CAAC8B,KAAR,CAAcO,QADzC,IAAApJ,OAAA,CAAAC,GAAA,CAAAC,QAAA,oBAAAoC,SAAS,QAEP,0CAFO,CAAT,GAAAA,SAAS,CAAT;IAKA,IAAImP,QAAQ,GAAG,CAAC,GAAGrL,UAAJ,EAAgBkE,KAAhB,CAAf;IACA,IAAInE,KAAkB,GAAG;MACvB0D,EAAE,EAAE9C,OAAO,CAAC8B,KAAR,CAAcgB,EAAd,IAAoB4H,QAAQ,CAACC,IAAT,CAAc,GAAd,CADD;MAEvBC,aAAa,EAAE5K,OAAO,CAAC8B,KAAR,CAAc8I,aAFN;MAGvB5K,OAAO,EAAEA,OAAO,CAAC8B,KAAR,CAAc9B,OAHA;MAIvBuD,KAAK,EAAEvD,OAAO,CAAC8B,KAAR,CAAcyB,KAJE;MAKvB5F,IAAI,EAAEqC,OAAO,CAAC8B,KAAR,CAAcnE,IALG;MAMvBkN,MAAM,EAAE7K,OAAO,CAAC8B,KAAR,CAAc+I,MANC;MAOvB9C,MAAM,EAAE/H,OAAO,CAAC8B,KAAR,CAAciG,MAPC;MAQvBnF,YAAY,EAAE5C,OAAO,CAAC8B,KAAR,CAAcc,YARL;MASvBkI,gBAAgB,EAAE9K,OAAO,CAAC8B,KAAR,CAAcc,YAAd,IAA8B,IATzB;MAUvBmI,gBAAgB,EAAE/K,OAAO,CAAC8B,KAAR,CAAciJ,gBAVT;MAWvB/F,MAAM,EAAEhF,OAAO,CAAC8B,KAAR,CAAckD;KAXxB;IAcA,IAAIhF,OAAO,CAAC8B,KAAR,CAAcO,QAAlB,EAA4B;MAC1BjD,KAAK,CAACiD,QAAN,GAAiByG,wBAAwB,CACvC9I,OAAO,CAAC8B,KAAR,CAAcO,QADyB,EAEvCqI,QAFuC,CAAzC;IAID;IAED9L,MAAM,CAACb,IAAP,CAAYqB,KAAZ;GAlDF;EAqDA,OAAOR,MAAP;AACD;AAED;AACA;AACA;;AACO,SAASoM,aAATA,CACLhQ,OADK,EAEsB;EAC3B,OAAOmF,cAAc,CAACnF,OAAD,CAArB;AACD;AAED;AACA;AACA;AACA;AACA;;AACO,SAASiQ,yBAATA,CACLrM,MADK,EAEU;EACf,OAAOA,MAAM,CAAC5B,GAAP,CAAYoC,KAAD,IAAW;IAC3B,IAAI8L,UAAU,GAAQ5K,QAAA,KAAAlB,KAAR,CAAd;IACA,IAAI8L,UAAU,CAACJ,gBAAX,IAA+B,IAAnC,EAAyC;MACvCI,UAAU,CAACJ,gBAAX,GAA8BI,UAAU,CAACtI,YAAX,IAA2B,IAAzD;IACD;IACD,IAAIsI,UAAU,CAAC7I,QAAf,EAAyB;MACvB6I,UAAU,CAAC7I,QAAX,GAAsB4I,yBAAyB,CAACC,UAAU,CAAC7I,QAAZ,CAA/C;IACD;IACD,OAAO6I,UAAP;EACD,CATM,CAAP;AAUD;AC/aM,SAASC,kBAATA,CACLvM,MADK,EAELoI,IAFK,EAQQ;EACb,OAAOoE,YAAY,CAAC;IAClB5P,QAAQ,EAAEwL,IAAF,IAAE,gBAAAA,IAAI,CAAExL,QADE;IAElBqM,OAAO,EAAEF,mBAAmB,CAAC;MAC3BH,cAAc,EAAER,IAAF,IAAE,gBAAAA,IAAI,CAAEQ,cADK;MAE3BC,YAAY,EAAET,IAAF,IAAE,gBAAAA,IAAI,CAAES;IAFO,CAAD,CAFV;IAMlB4D,aAAa,EAAErE,IAAF,IAAE,gBAAAA,IAAI,CAAEqE,aANH;IAOlBzM,MAAM,EAAEqM,yBAAyB,CAACrM,MAAD;GAPhB,CAAZ,CAQJ0M,UARI,EAAP;AASD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}