{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\aiplanet\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';\nimport { cn } from '../../lib/utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ToastContext = /*#__PURE__*/createContext();\nexport const useToast = () => {\n  _s();\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n_s(useToast, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst ToastProvider = ({\n  children\n}) => {\n  _s2();\n  const [toasts, setToasts] = useState([]);\n  const addToast = useCallback(toast => {\n    const id = Date.now().toString();\n    const newToast = {\n      id,\n      ...toast\n    };\n    setToasts(prev => [...prev, newToast]);\n\n    // Auto remove after duration\n    if (toast.duration !== 0) {\n      setTimeout(() => {\n        removeToast(id);\n      }, toast.duration || 5000);\n    }\n    return id;\n  }, [removeToast]);\n  const removeToast = useCallback(id => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  }, []);\n  const toast = useCallback(options => {\n    return addToast(options);\n  }, [addToast]);\n  const success = useCallback((message, options = {}) => {\n    return addToast({\n      ...options,\n      type: 'success',\n      title: message\n    });\n  }, [addToast]);\n  const error = useCallback((message, options = {}) => {\n    return addToast({\n      ...options,\n      type: 'error',\n      title: message\n    });\n  }, [addToast]);\n  const warning = useCallback((message, options = {}) => {\n    return addToast({\n      ...options,\n      type: 'warning',\n      title: message\n    });\n  }, [addToast]);\n  const info = useCallback((message, options = {}) => {\n    return addToast({\n      ...options,\n      type: 'info',\n      title: message\n    });\n  }, [addToast]);\n  return /*#__PURE__*/_jsxDEV(ToastContext.Provider, {\n    value: {\n      toast,\n      success,\n      error,\n      warning,\n      info\n    },\n    children: [children, /*#__PURE__*/_jsxDEV(ToastContainer, {\n      toasts: toasts,\n      removeToast: removeToast\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s2(ToastProvider, \"7wZg3xcHEwhavtqQGY31b7Isb0c=\");\n_c = ToastProvider;\nconst ToastContainer = ({\n  toasts,\n  removeToast\n}) => {\n  if (toasts.length === 0) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-4 right-4 z-50 space-y-2\",\n    children: toasts.map(toast => /*#__PURE__*/_jsxDEV(Toast, {\n      toast: toast,\n      onClose: () => removeToast(toast.id)\n    }, toast.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n};\n_c2 = ToastContainer;\nconst Toast = ({\n  toast,\n  onClose\n}) => {\n  const {\n    type = 'info',\n    title,\n    description\n  } = toast;\n  const icons = {\n    success: CheckCircle,\n    error: AlertCircle,\n    warning: AlertTriangle,\n    info: Info\n  };\n  const styles = {\n    success: 'bg-green-50 border-green-200 text-green-800',\n    error: 'bg-red-50 border-red-200 text-red-800',\n    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n    info: 'bg-blue-50 border-blue-200 text-blue-800'\n  };\n  const iconStyles = {\n    success: 'text-green-500',\n    error: 'text-red-500',\n    warning: 'text-yellow-500',\n    info: 'text-blue-500'\n  };\n  const Icon = icons[type];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: cn('min-w-80 max-w-md p-4 rounded-lg border shadow-lg', styles[type]),\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start space-x-3\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        className: cn('w-5 h-5 mt-0.5 flex-shrink-0', iconStyles[type])\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 min-w-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm font-medium\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), description && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm mt-1 opacity-90\",\n          children: description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"flex-shrink-0 p-1 rounded-md hover:bg-black/10 transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(X, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_c3 = Toast;\nexport { ToastProvider, Toast };\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ToastProvider\");\n$RefreshReg$(_c2, \"ToastContainer\");\n$RefreshReg$(_c3, \"Toast\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useCallback", "X", "CheckCircle", "AlertCircle", "Info", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cn", "jsxDEV", "_jsxDEV", "ToastContext", "useToast", "_s", "context", "Error", "ToastProvider", "children", "_s2", "toasts", "setToasts", "addToast", "toast", "id", "Date", "now", "toString", "newToast", "prev", "duration", "setTimeout", "removeToast", "filter", "options", "success", "message", "type", "title", "error", "warning", "info", "Provider", "value", "ToastContainer", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "length", "className", "map", "Toast", "onClose", "_c2", "description", "icons", "styles", "iconStyles", "Icon", "onClick", "_c3", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/aiplanet/frontend/src/components/ui/toast.jsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useCallback } from 'react';\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';\nimport { cn } from '../../lib/utils';\n\nconst ToastContext = createContext();\n\nexport const useToast = () => {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n\nconst ToastProvider = ({ children }) => {\n  const [toasts, setToasts] = useState([]);\n\n  const addToast = useCallback((toast) => {\n    const id = Date.now().toString();\n    const newToast = { id, ...toast };\n\n    setToasts(prev => [...prev, newToast]);\n\n    // Auto remove after duration\n    if (toast.duration !== 0) {\n      setTimeout(() => {\n        removeToast(id);\n      }, toast.duration || 5000);\n    }\n\n    return id;\n  }, [removeToast]);\n\n  const removeToast = useCallback((id) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  }, []);\n\n  const toast = useCallback((options) => {\n    return addToast(options);\n  }, [addToast]);\n\n  const success = useCallback((message, options = {}) => {\n    return addToast({ ...options, type: 'success', title: message });\n  }, [addToast]);\n\n  const error = useCallback((message, options = {}) => {\n    return addToast({ ...options, type: 'error', title: message });\n  }, [addToast]);\n\n  const warning = useCallback((message, options = {}) => {\n    return addToast({ ...options, type: 'warning', title: message });\n  }, [addToast]);\n\n  const info = useCallback((message, options = {}) => {\n    return addToast({ ...options, type: 'info', title: message });\n  }, [addToast]);\n\n  return (\n    <ToastContext.Provider value={{ toast, success, error, warning, info }}>\n      {children}\n      <ToastContainer toasts={toasts} removeToast={removeToast} />\n    </ToastContext.Provider>\n  );\n};\n\nconst ToastContainer = ({ toasts, removeToast }) => {\n  if (toasts.length === 0) return null;\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2\">\n      {toasts.map(toast => (\n        <Toast key={toast.id} toast={toast} onClose={() => removeToast(toast.id)} />\n      ))}\n    </div>\n  );\n};\n\nconst Toast = ({ toast, onClose }) => {\n  const { type = 'info', title, description } = toast;\n\n  const icons = {\n    success: CheckCircle,\n    error: AlertCircle,\n    warning: AlertTriangle,\n    info: Info,\n  };\n\n  const styles = {\n    success: 'bg-green-50 border-green-200 text-green-800',\n    error: 'bg-red-50 border-red-200 text-red-800',\n    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',\n    info: 'bg-blue-50 border-blue-200 text-blue-800',\n  };\n\n  const iconStyles = {\n    success: 'text-green-500',\n    error: 'text-red-500',\n    warning: 'text-yellow-500',\n    info: 'text-blue-500',\n  };\n\n  const Icon = icons[type];\n\n  return (\n    <div className={cn(\n      'min-w-80 max-w-md p-4 rounded-lg border shadow-lg',\n      styles[type]\n    )}>\n      <div className=\"flex items-start space-x-3\">\n        <Icon className={cn('w-5 h-5 mt-0.5 flex-shrink-0', iconStyles[type])} />\n        <div className=\"flex-1 min-w-0\">\n          <p className=\"text-sm font-medium\">{title}</p>\n          {description && (\n            <p className=\"text-sm mt-1 opacity-90\">{description}</p>\n          )}\n        </div>\n        <button\n          onClick={onClose}\n          className=\"flex-shrink-0 p-1 rounded-md hover:bg-black/10 transition-colors\"\n        >\n          <X className=\"w-4 h-4\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport { ToastProvider, Toast };\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/E,SAASC,CAAC,EAAEC,WAAW,EAAEC,WAAW,EAAEC,IAAI,EAAEC,aAAa,QAAQ,cAAc;AAC/E,SAASC,EAAE,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,YAAY,gBAAGZ,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMa,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGd,UAAU,CAACW,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAQrB,MAAMI,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACtC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAExC,MAAMoB,QAAQ,GAAGnB,WAAW,CAAEoB,KAAK,IAAK;IACtC,MAAMC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;IAChC,MAAMC,QAAQ,GAAG;MAAEJ,EAAE;MAAE,GAAGD;IAAM,CAAC;IAEjCF,SAAS,CAACQ,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,QAAQ,CAAC,CAAC;;IAEtC;IACA,IAAIL,KAAK,CAACO,QAAQ,KAAK,CAAC,EAAE;MACxBC,UAAU,CAAC,MAAM;QACfC,WAAW,CAACR,EAAE,CAAC;MACjB,CAAC,EAAED,KAAK,CAACO,QAAQ,IAAI,IAAI,CAAC;IAC5B;IAEA,OAAON,EAAE;EACX,CAAC,EAAE,CAACQ,WAAW,CAAC,CAAC;EAEjB,MAAMA,WAAW,GAAG7B,WAAW,CAAEqB,EAAE,IAAK;IACtCH,SAAS,CAACQ,IAAI,IAAIA,IAAI,CAACI,MAAM,CAACV,KAAK,IAAIA,KAAK,CAACC,EAAE,KAAKA,EAAE,CAAC,CAAC;EAC1D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,KAAK,GAAGpB,WAAW,CAAE+B,OAAO,IAAK;IACrC,OAAOZ,QAAQ,CAACY,OAAO,CAAC;EAC1B,CAAC,EAAE,CAACZ,QAAQ,CAAC,CAAC;EAEd,MAAMa,OAAO,GAAGhC,WAAW,CAAC,CAACiC,OAAO,EAAEF,OAAO,GAAG,CAAC,CAAC,KAAK;IACrD,OAAOZ,QAAQ,CAAC;MAAE,GAAGY,OAAO;MAAEG,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAEF;IAAQ,CAAC,CAAC;EAClE,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;EAEd,MAAMiB,KAAK,GAAGpC,WAAW,CAAC,CAACiC,OAAO,EAAEF,OAAO,GAAG,CAAC,CAAC,KAAK;IACnD,OAAOZ,QAAQ,CAAC;MAAE,GAAGY,OAAO;MAAEG,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAEF;IAAQ,CAAC,CAAC;EAChE,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;EAEd,MAAMkB,OAAO,GAAGrC,WAAW,CAAC,CAACiC,OAAO,EAAEF,OAAO,GAAG,CAAC,CAAC,KAAK;IACrD,OAAOZ,QAAQ,CAAC;MAAE,GAAGY,OAAO;MAAEG,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAEF;IAAQ,CAAC,CAAC;EAClE,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;EAEd,MAAMmB,IAAI,GAAGtC,WAAW,CAAC,CAACiC,OAAO,EAAEF,OAAO,GAAG,CAAC,CAAC,KAAK;IAClD,OAAOZ,QAAQ,CAAC;MAAE,GAAGY,OAAO;MAAEG,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAEF;IAAQ,CAAC,CAAC;EAC/D,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;EAEd,oBACEX,OAAA,CAACC,YAAY,CAAC8B,QAAQ;IAACC,KAAK,EAAE;MAAEpB,KAAK;MAAEY,OAAO;MAAEI,KAAK;MAAEC,OAAO;MAAEC;IAAK,CAAE;IAAAvB,QAAA,GACpEA,QAAQ,eACTP,OAAA,CAACiC,cAAc;MAACxB,MAAM,EAAEA,MAAO;MAACY,WAAW,EAAEA;IAAY;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CAAC;AAE5B,CAAC;AAAC7B,GAAA,CAjDIF,aAAa;AAAAgC,EAAA,GAAbhC,aAAa;AAmDnB,MAAM2B,cAAc,GAAGA,CAAC;EAAExB,MAAM;EAAEY;AAAY,CAAC,KAAK;EAClD,IAAIZ,MAAM,CAAC8B,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAEpC,oBACEvC,OAAA;IAAKwC,SAAS,EAAC,oCAAoC;IAAAjC,QAAA,EAChDE,MAAM,CAACgC,GAAG,CAAC7B,KAAK,iBACfZ,OAAA,CAAC0C,KAAK;MAAgB9B,KAAK,EAAEA,KAAM;MAAC+B,OAAO,EAAEA,CAAA,KAAMtB,WAAW,CAACT,KAAK,CAACC,EAAE;IAAE,GAA7DD,KAAK,CAACC,EAAE;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAuD,CAC5E;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACO,GAAA,GAVIX,cAAc;AAYpB,MAAMS,KAAK,GAAGA,CAAC;EAAE9B,KAAK;EAAE+B;AAAQ,CAAC,KAAK;EACpC,MAAM;IAAEjB,IAAI,GAAG,MAAM;IAAEC,KAAK;IAAEkB;EAAY,CAAC,GAAGjC,KAAK;EAEnD,MAAMkC,KAAK,GAAG;IACZtB,OAAO,EAAE9B,WAAW;IACpBkC,KAAK,EAAEjC,WAAW;IAClBkC,OAAO,EAAEhC,aAAa;IACtBiC,IAAI,EAAElC;EACR,CAAC;EAED,MAAMmD,MAAM,GAAG;IACbvB,OAAO,EAAE,6CAA6C;IACtDI,KAAK,EAAE,uCAAuC;IAC9CC,OAAO,EAAE,gDAAgD;IACzDC,IAAI,EAAE;EACR,CAAC;EAED,MAAMkB,UAAU,GAAG;IACjBxB,OAAO,EAAE,gBAAgB;IACzBI,KAAK,EAAE,cAAc;IACrBC,OAAO,EAAE,iBAAiB;IAC1BC,IAAI,EAAE;EACR,CAAC;EAED,MAAMmB,IAAI,GAAGH,KAAK,CAACpB,IAAI,CAAC;EAExB,oBACE1B,OAAA;IAAKwC,SAAS,EAAE1C,EAAE,CAChB,mDAAmD,EACnDiD,MAAM,CAACrB,IAAI,CACb,CAAE;IAAAnB,QAAA,eACAP,OAAA;MAAKwC,SAAS,EAAC,4BAA4B;MAAAjC,QAAA,gBACzCP,OAAA,CAACiD,IAAI;QAACT,SAAS,EAAE1C,EAAE,CAAC,8BAA8B,EAAEkD,UAAU,CAACtB,IAAI,CAAC;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACzErC,OAAA;QAAKwC,SAAS,EAAC,gBAAgB;QAAAjC,QAAA,gBAC7BP,OAAA;UAAGwC,SAAS,EAAC,qBAAqB;UAAAjC,QAAA,EAAEoB;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAC7CQ,WAAW,iBACV7C,OAAA;UAAGwC,SAAS,EAAC,yBAAyB;UAAAjC,QAAA,EAAEsC;QAAW;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CACxD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACNrC,OAAA;QACEkD,OAAO,EAAEP,OAAQ;QACjBH,SAAS,EAAC,kEAAkE;QAAAjC,QAAA,eAE5EP,OAAA,CAACP,CAAC;UAAC+C,SAAS,EAAC;QAAS;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACc,GAAA,GAhDIT,KAAK;AAkDX,SAASpC,aAAa,EAAEoC,KAAK;AAAG,IAAAJ,EAAA,EAAAM,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}