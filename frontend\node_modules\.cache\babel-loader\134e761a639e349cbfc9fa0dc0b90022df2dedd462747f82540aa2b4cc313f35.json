{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Folders = createLucideIcon(\"Folders\", [[\"path\", {\n  d: \"M20 17a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3.9a2 2 0 0 1-1.69-.9l-.81-1.2a2 2 0 0 0-1.67-.9H8a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2Z\",\n  key: \"4u7rpt\"\n}], [\"path\", {\n  d: \"M2 8v11a2 2 0 0 0 2 2h14\",\n  key: \"1eicx1\"\n}]]);\nexport { Folders as default };", "map": {"version": 3, "names": ["Folders", "createLucideIcon", "d", "key"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\folders.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Folders\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTdhMiAyIDAgMCAwIDItMlY5YTIgMiAwIDAgMC0yLTJoLTMuOWEyIDIgMCAwIDEtMS42OS0uOWwtLjgxLTEuMmEyIDIgMCAwIDAtMS42Ny0uOUg4YTIgMiAwIDAgMC0yIDJ2OWEyIDIgMCAwIDAgMiAyWiIgLz4KICA8cGF0aCBkPSJNMiA4djExYTIgMiAwIDAgMCAyIDJoMTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/folders\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Folders = createLucideIcon('Folders', [\n  [\n    'path',\n    {\n      d: 'M20 17a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3.9a2 2 0 0 1-1.69-.9l-.81-1.2a2 2 0 0 0-1.67-.9H8a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2Z',\n      key: '4u7rpt',\n    },\n  ],\n  ['path', { d: 'M2 8v11a2 2 0 0 0 2 2h14', key: '1eicx1' }],\n]);\n\nexport default Folders;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}