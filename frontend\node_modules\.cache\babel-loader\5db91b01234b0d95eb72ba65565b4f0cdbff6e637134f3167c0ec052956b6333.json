{"ast": null, "code": "import ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nimport { createStore } from 'zustand/vanilla';\nconst {\n  useDebugValue\n} = ReactExports;\nconst {\n  useSyncExternalStoreWithSelector\n} = useSyncExternalStoreExports;\nconst identity = arg => arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n  const slice = useSyncExternalStoreWithSelector(api.subscribe, api.getState, api.getServerState || api.getInitialState, selector, equalityFn);\n  useDebugValue(slice);\n  return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn) => {\n  const api = createStore(createState);\n  const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn) => useStoreWithEqualityFn(api, selector, equalityFn);\n  Object.assign(useBoundStoreWithEqualityFn, api);\n  return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn) => createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\nexport { createWithEqualityFn, useStoreWithEqualityFn };", "map": {"version": 3, "names": ["ReactExports", "useSyncExternalStoreExports", "createStore", "useDebugValue", "useSyncExternalStoreWithSelector", "identity", "arg", "useStoreWithEqualityFn", "api", "selector", "equalityFn", "slice", "subscribe", "getState", "getServerState", "getInitialState", "createWithEqualityFnImpl", "createState", "defaultEqualityFn", "useBoundStoreWithEqualityFn", "Object", "assign", "createWithEqualityFn"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/zustand/esm/traditional.mjs"], "sourcesContent": ["import ReactExports from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nimport { createStore } from 'zustand/vanilla';\n\nconst { useDebugValue } = ReactExports;\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nconst identity = (arg) => arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getServerState || api.getInitialState,\n    selector,\n    equalityFn\n  );\n  useDebugValue(slice);\n  return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn) => {\n  const api = createStore(createState);\n  const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn) => useStoreWithEqualityFn(api, selector, equalityFn);\n  Object.assign(useBoundStoreWithEqualityFn, api);\n  return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn) => createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\n\nexport { createWithEqualityFn, useStoreWithEqualityFn };\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,OAAO;AAChC,OAAOC,2BAA2B,MAAM,+CAA+C;AACvF,SAASC,WAAW,QAAQ,iBAAiB;AAE7C,MAAM;EAAEC;AAAc,CAAC,GAAGH,YAAY;AACtC,MAAM;EAAEI;AAAiC,CAAC,GAAGH,2BAA2B;AACxE,MAAMI,QAAQ,GAAIC,GAAG,IAAKA,GAAG;AAC7B,SAASC,sBAAsBA,CAACC,GAAG,EAAEC,QAAQ,GAAGJ,QAAQ,EAAEK,UAAU,EAAE;EACpE,MAAMC,KAAK,GAAGP,gCAAgC,CAC5CI,GAAG,CAACI,SAAS,EACbJ,GAAG,CAACK,QAAQ,EACZL,GAAG,CAACM,cAAc,IAAIN,GAAG,CAACO,eAAe,EACzCN,QAAQ,EACRC,UACF,CAAC;EACDP,aAAa,CAACQ,KAAK,CAAC;EACpB,OAAOA,KAAK;AACd;AACA,MAAMK,wBAAwB,GAAGA,CAACC,WAAW,EAAEC,iBAAiB,KAAK;EACnE,MAAMV,GAAG,GAAGN,WAAW,CAACe,WAAW,CAAC;EACpC,MAAME,2BAA2B,GAAGA,CAACV,QAAQ,EAAEC,UAAU,GAAGQ,iBAAiB,KAAKX,sBAAsB,CAACC,GAAG,EAAEC,QAAQ,EAAEC,UAAU,CAAC;EACnIU,MAAM,CAACC,MAAM,CAACF,2BAA2B,EAAEX,GAAG,CAAC;EAC/C,OAAOW,2BAA2B;AACpC,CAAC;AACD,MAAMG,oBAAoB,GAAGA,CAACL,WAAW,EAAEC,iBAAiB,KAAKD,WAAW,GAAGD,wBAAwB,CAACC,WAAW,EAAEC,iBAAiB,CAAC,GAAGF,wBAAwB;AAElK,SAASM,oBAAoB,EAAEf,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}