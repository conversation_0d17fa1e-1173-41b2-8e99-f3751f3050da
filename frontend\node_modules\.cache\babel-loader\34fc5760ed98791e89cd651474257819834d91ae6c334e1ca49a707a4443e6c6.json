{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst MonitorStop = createLucideIcon(\"MonitorStop\", [[\"rect\", {\n  x: \"9\",\n  y: \"7\",\n  width: \"6\",\n  height: \"6\",\n  key: \"4xvc6r\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"14\",\n  x: \"2\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"48i651\"\n}], [\"path\", {\n  d: \"M12 17v4\",\n  key: \"1riwvh\"\n}], [\"path\", {\n  d: \"M8 21h8\",\n  key: \"1ev6f3\"\n}]]);\nexport { MonitorStop as default };", "map": {"version": 3, "names": ["MonitorStop", "createLucideIcon", "x", "y", "width", "height", "key", "rx", "d"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\monitor-stop.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MonitorStop\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSI5IiB5PSI3IiB3aWR0aD0iNiIgaGVpZ2h0PSI2IiAvPgogIDxyZWN0IHdpZHRoPSIyMCIgaGVpZ2h0PSIxNCIgeD0iMiIgeT0iMyIgcng9IjIiIC8+CiAgPHBhdGggZD0iTTEyIDE3djQiIC8+CiAgPHBhdGggZD0iTTggMjFoOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/monitor-stop\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MonitorStop = createLucideIcon('MonitorStop', [\n  ['rect', { x: '9', y: '7', width: '6', height: '6', key: '4xvc6r' }],\n  ['rect', { width: '20', height: '14', x: '2', y: '3', rx: '2', key: '48i651' }],\n  ['path', { d: 'M12 17v4', key: '1riwvh' }],\n  ['path', { d: 'M8 21h8', key: '1ev6f3' }],\n]);\n\nexport default MonitorStop;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,QAAQ;EAAEC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,QAAQ;EAAEF,KAAA,EAAO;EAAMC,MAAQ;EAAMH,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAI,EAAA,EAAI,GAAK;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEE,CAAA,EAAG,UAAY;EAAAF,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEE,CAAA,EAAG,SAAW;EAAAF,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}