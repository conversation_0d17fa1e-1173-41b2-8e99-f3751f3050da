# PostgreSQL Setup Guide for GenAI Workflow Builder

## Prerequisites

1. **Install PostgreSQL**
   - **Windows**: Download from https://www.postgresql.org/download/windows/
   - **macOS**: `brew install postgresql` or download from official site
   - **Linux**: `sudo apt-get install postgresql postgresql-contrib`

2. **Start PostgreSQL Service**
   - **Windows**: PostgreSQL should start automatically after installation
   - **macOS**: `brew services start postgresql`
   - **Linux**: `sudo systemctl start postgresql`

## Database Setup

### Method 1: Using psql Command Line

1. **Connect to PostgreSQL as superuser:**
   ```bash
   psql -U postgres
   ```

2. **Create the database:**
   ```sql
   CREATE DATABASE genai_workflow;
   ```

3. **Exit psql:**
   ```sql
   \q
   ```

### Method 2: Using pgAdmin (GUI)

1. Open pgAdmin (installed with PostgreSQL)
2. Connect to your PostgreSQL server
3. Right-click on "Databases" → "Create" → "Database"
4. Name: `genai_workflow`
5. Click "Save"

## Configuration

### Update Backend Configuration

The `.env` file is already configured for PostgreSQL:

```env
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/genai_workflow
```

**Important**: Change the password from `postgres` to your actual PostgreSQL password.

### Common PostgreSQL Default Credentials

- **Username**: `postgres`
- **Password**: Set during PostgreSQL installation
- **Host**: `localhost`
- **Port**: `5432`

## Verification

1. **Test connection:**
   ```bash
   psql -U postgres -d genai_workflow -c "SELECT 'Connection successful!' as status;"
   ```

2. **Start the backend:**
   ```bash
   cd backend
   python -m uvicorn app.main:app --reload
   ```

3. **Check logs for successful database connection**

## Troubleshooting

### Common Issues

1. **Connection refused**
   - Ensure PostgreSQL service is running
   - Check if port 5432 is available

2. **Authentication failed**
   - Verify username and password in `.env` file
   - Check PostgreSQL authentication settings

3. **Database does not exist**
   - Create the database using the steps above

### Reset Database (if needed)

```sql
-- Connect as postgres user
psql -U postgres

-- Drop and recreate database
DROP DATABASE IF EXISTS genai_workflow;
CREATE DATABASE genai_workflow;
```

## Production Notes

For production deployment:
1. Create a dedicated database user (not postgres)
2. Use strong passwords
3. Configure proper access controls
4. Enable SSL connections
5. Set up regular backups

## Tables Created Automatically

The FastAPI application will automatically create these tables:
- `documents` - Uploaded PDF documents
- `chat_messages` - Chat conversation history
- `workflow_executions` - Workflow execution logs

No manual table creation is required!
