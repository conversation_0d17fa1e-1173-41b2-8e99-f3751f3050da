# Production Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# OpenAI API Configuration (REQUIRED)
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration
POSTGRES_DB=genai_workflow
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_PORT=5432

# Security
SECRET_KEY=your-very-secure-secret-key-change-this-in-production

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Web Search API Configuration (OPTIONAL)
# Choose one: SerpAPI or Brave Search
SERPAPI_API_KEY=your_serpapi_key_here
BRAVE_API_KEY=your_brave_api_key_here
WEB_SEARCH_PROVIDER=serpapi  # Options: serpapi, brave, none

# Vector Database Configuration
QDRANT_PORT=6333
QDRANT_GRPC_PORT=6334

# Application Ports
BACKEND_PORT=8000
FRONTEND_PORT=3000
REDIS_PORT=6379
NGINX_PORT=80
NGINX_SSL_PORT=443

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000

# CORS Configuration (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# =============================================================================
# PRODUCTION DEPLOYMENT CONFIGURATION
# =============================================================================

# Domain Configuration (for production deployment)
DOMAIN=yourdomain.com
SSL_EMAIL=<EMAIL>

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Monitoring
LOG_LEVEL=INFO
SENTRY_DSN=your_sentry_dsn_here

# =============================================================================
# DOCKER COMPOSE PROFILES
# =============================================================================
# Available profiles:
# - default: postgres, qdrant, backend, frontend
# - with-redis: includes Redis caching
# - with-nginx: includes Nginx load balancer
# - monitoring: includes monitoring stack

COMPOSE_PROFILES=default
