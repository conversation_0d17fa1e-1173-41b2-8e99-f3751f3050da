{"ast": null, "code": "import { get, set } from \"./schedule.js\";\nfunction easeConstant(id, value) {\n  if (typeof value !== \"function\") throw new Error();\n  return function () {\n    set(this, id).ease = value;\n  };\n}\nexport default function (value) {\n  var id = this._id;\n  return arguments.length ? this.each(easeConstant(id, value)) : get(this.node(), id).ease;\n}", "map": {"version": 3, "names": ["get", "set", "easeConstant", "id", "value", "Error", "ease", "_id", "arguments", "length", "each", "node"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-transition/src/transition/ease.js"], "sourcesContent": ["import {get, set} from \"./schedule.js\";\n\nfunction easeConstant(id, value) {\n  if (typeof value !== \"function\") throw new Error;\n  return function() {\n    set(this, id).ease = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each(easeConstant(id, value))\n      : get(this.node(), id).ease;\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,GAAG,QAAO,eAAe;AAEtC,SAASC,YAAYA,CAACC,EAAE,EAAEC,KAAK,EAAE;EAC/B,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE,MAAM,IAAIC,KAAK,CAAD,CAAC;EAChD,OAAO,YAAW;IAChBJ,GAAG,CAAC,IAAI,EAAEE,EAAE,CAAC,CAACG,IAAI,GAAGF,KAAK;EAC5B,CAAC;AACH;AAEA,eAAe,UAASA,KAAK,EAAE;EAC7B,IAAID,EAAE,GAAG,IAAI,CAACI,GAAG;EAEjB,OAAOC,SAAS,CAACC,MAAM,GACjB,IAAI,CAACC,IAAI,CAACR,YAAY,CAACC,EAAE,EAAEC,KAAK,CAAC,CAAC,GAClCJ,GAAG,CAAC,IAAI,CAACW,IAAI,CAAC,CAAC,EAAER,EAAE,CAAC,CAACG,IAAI;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}