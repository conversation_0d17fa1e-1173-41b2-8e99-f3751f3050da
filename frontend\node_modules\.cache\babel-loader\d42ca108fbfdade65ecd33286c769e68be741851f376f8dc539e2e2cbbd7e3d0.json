{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FlaskRound = createLucideIcon(\"FlaskRound\", [[\"path\", {\n  d: \"M10 2v7.31\",\n  key: \"5d1hyh\"\n}], [\"path\", {\n  d: \"M14 9.3V1.99\",\n  key: \"14k4l0\"\n}], [\"path\", {\n  d: \"M8.5 2h7\",\n  key: \"csnxdl\"\n}], [\"path\", {\n  d: \"M14 9.3a6.5 6.5 0 1 1-4 0\",\n  key: \"1r8fvy\"\n}], [\"path\", {\n  d: \"M5.52 16h12.96\",\n  key: \"46hh1i\"\n}]]);\nexport { FlaskRound as default };", "map": {"version": 3, "names": ["FlaskRound", "createLucideIcon", "d", "key"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\flask-round.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FlaskRound\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnY3LjMxIiAvPgogIDxwYXRoIGQ9Ik0xNCA5LjNWMS45OSIgLz4KICA8cGF0aCBkPSJNOC41IDJoNyIgLz4KICA8cGF0aCBkPSJNMTQgOS4zYTYuNSA2LjUgMCAxIDEtNCAwIiAvPgogIDxwYXRoIGQ9Ik01LjUyIDE2aDEyLjk2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/flask-round\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FlaskRound = createLucideIcon('FlaskRound', [\n  ['path', { d: 'M10 2v7.31', key: '5d1hyh' }],\n  ['path', { d: 'M14 9.3V1.99', key: '14k4l0' }],\n  ['path', { d: 'M8.5 2h7', key: 'csnxdl' }],\n  ['path', { d: 'M14 9.3a6.5 6.5 0 1 1-4 0', key: '1r8fvy' }],\n  ['path', { d: 'M5.52 16h12.96', key: '46hh1i' }],\n]);\n\nexport default FlaskRound;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}