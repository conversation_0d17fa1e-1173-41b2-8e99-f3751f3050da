{"ast": null, "code": "export function Transform(k, x, y) {\n  this.k = k;\n  this.x = x;\n  this.y = y;\n}\nTransform.prototype = {\n  constructor: Transform,\n  scale: function (k) {\n    return k === 1 ? this : new Transform(this.k * k, this.x, this.y);\n  },\n  translate: function (x, y) {\n    return x === 0 & y === 0 ? this : new Transform(this.k, this.x + this.k * x, this.y + this.k * y);\n  },\n  apply: function (point) {\n    return [point[0] * this.k + this.x, point[1] * this.k + this.y];\n  },\n  applyX: function (x) {\n    return x * this.k + this.x;\n  },\n  applyY: function (y) {\n    return y * this.k + this.y;\n  },\n  invert: function (location) {\n    return [(location[0] - this.x) / this.k, (location[1] - this.y) / this.k];\n  },\n  invertX: function (x) {\n    return (x - this.x) / this.k;\n  },\n  invertY: function (y) {\n    return (y - this.y) / this.k;\n  },\n  rescaleX: function (x) {\n    return x.copy().domain(x.range().map(this.invertX, this).map(x.invert, x));\n  },\n  rescaleY: function (y) {\n    return y.copy().domain(y.range().map(this.invertY, this).map(y.invert, y));\n  },\n  toString: function () {\n    return \"translate(\" + this.x + \",\" + this.y + \") scale(\" + this.k + \")\";\n  }\n};\nexport var identity = new Transform(1, 0, 0);\ntransform.prototype = Transform.prototype;\nexport default function transform(node) {\n  while (!node.__zoom) if (!(node = node.parentNode)) return identity;\n  return node.__zoom;\n}", "map": {"version": 3, "names": ["Transform", "k", "x", "y", "prototype", "constructor", "scale", "translate", "apply", "point", "applyX", "applyY", "invert", "location", "invertX", "invertY", "rescaleX", "copy", "domain", "range", "map", "rescaleY", "toString", "identity", "transform", "node", "__zoom", "parentNode"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-zoom/src/transform.js"], "sourcesContent": ["export function Transform(k, x, y) {\n  this.k = k;\n  this.x = x;\n  this.y = y;\n}\n\nTransform.prototype = {\n  constructor: Transform,\n  scale: function(k) {\n    return k === 1 ? this : new Transform(this.k * k, this.x, this.y);\n  },\n  translate: function(x, y) {\n    return x === 0 & y === 0 ? this : new Transform(this.k, this.x + this.k * x, this.y + this.k * y);\n  },\n  apply: function(point) {\n    return [point[0] * this.k + this.x, point[1] * this.k + this.y];\n  },\n  applyX: function(x) {\n    return x * this.k + this.x;\n  },\n  applyY: function(y) {\n    return y * this.k + this.y;\n  },\n  invert: function(location) {\n    return [(location[0] - this.x) / this.k, (location[1] - this.y) / this.k];\n  },\n  invertX: function(x) {\n    return (x - this.x) / this.k;\n  },\n  invertY: function(y) {\n    return (y - this.y) / this.k;\n  },\n  rescaleX: function(x) {\n    return x.copy().domain(x.range().map(this.invertX, this).map(x.invert, x));\n  },\n  rescaleY: function(y) {\n    return y.copy().domain(y.range().map(this.invertY, this).map(y.invert, y));\n  },\n  toString: function() {\n    return \"translate(\" + this.x + \",\" + this.y + \") scale(\" + this.k + \")\";\n  }\n};\n\nexport var identity = new Transform(1, 0, 0);\n\ntransform.prototype = Transform.prototype;\n\nexport default function transform(node) {\n  while (!node.__zoom) if (!(node = node.parentNode)) return identity;\n  return node.__zoom;\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACjC,IAAI,CAACF,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACV,IAAI,CAACC,CAAC,GAAGA,CAAC;AACZ;AAEAH,SAAS,CAACI,SAAS,GAAG;EACpBC,WAAW,EAAEL,SAAS;EACtBM,KAAK,EAAE,SAAAA,CAASL,CAAC,EAAE;IACjB,OAAOA,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,IAAID,SAAS,CAAC,IAAI,CAACC,CAAC,GAAGA,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;EACnE,CAAC;EACDI,SAAS,EAAE,SAAAA,CAASL,CAAC,EAAEC,CAAC,EAAE;IACxB,OAAOD,CAAC,KAAK,CAAC,GAAGC,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,IAAIH,SAAS,CAAC,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,GAAG,IAAI,CAACD,CAAC,GAAGC,CAAC,EAAE,IAAI,CAACC,CAAC,GAAG,IAAI,CAACF,CAAC,GAAGE,CAAC,CAAC;EACnG,CAAC;EACDK,KAAK,EAAE,SAAAA,CAASC,KAAK,EAAE;IACrB,OAAO,CAACA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACR,CAAC,GAAG,IAAI,CAACC,CAAC,EAAEO,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAACR,CAAC,GAAG,IAAI,CAACE,CAAC,CAAC;EACjE,CAAC;EACDO,MAAM,EAAE,SAAAA,CAASR,CAAC,EAAE;IAClB,OAAOA,CAAC,GAAG,IAAI,CAACD,CAAC,GAAG,IAAI,CAACC,CAAC;EAC5B,CAAC;EACDS,MAAM,EAAE,SAAAA,CAASR,CAAC,EAAE;IAClB,OAAOA,CAAC,GAAG,IAAI,CAACF,CAAC,GAAG,IAAI,CAACE,CAAC;EAC5B,CAAC;EACDS,MAAM,EAAE,SAAAA,CAASC,QAAQ,EAAE;IACzB,OAAO,CAAC,CAACA,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAACX,CAAC,IAAI,IAAI,CAACD,CAAC,EAAE,CAACY,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAACV,CAAC,IAAI,IAAI,CAACF,CAAC,CAAC;EAC3E,CAAC;EACDa,OAAO,EAAE,SAAAA,CAASZ,CAAC,EAAE;IACnB,OAAO,CAACA,CAAC,GAAG,IAAI,CAACA,CAAC,IAAI,IAAI,CAACD,CAAC;EAC9B,CAAC;EACDc,OAAO,EAAE,SAAAA,CAASZ,CAAC,EAAE;IACnB,OAAO,CAACA,CAAC,GAAG,IAAI,CAACA,CAAC,IAAI,IAAI,CAACF,CAAC;EAC9B,CAAC;EACDe,QAAQ,EAAE,SAAAA,CAASd,CAAC,EAAE;IACpB,OAAOA,CAAC,CAACe,IAAI,CAAC,CAAC,CAACC,MAAM,CAAChB,CAAC,CAACiB,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,IAAI,CAACN,OAAO,EAAE,IAAI,CAAC,CAACM,GAAG,CAAClB,CAAC,CAACU,MAAM,EAAEV,CAAC,CAAC,CAAC;EAC5E,CAAC;EACDmB,QAAQ,EAAE,SAAAA,CAASlB,CAAC,EAAE;IACpB,OAAOA,CAAC,CAACc,IAAI,CAAC,CAAC,CAACC,MAAM,CAACf,CAAC,CAACgB,KAAK,CAAC,CAAC,CAACC,GAAG,CAAC,IAAI,CAACL,OAAO,EAAE,IAAI,CAAC,CAACK,GAAG,CAACjB,CAAC,CAACS,MAAM,EAAET,CAAC,CAAC,CAAC;EAC5E,CAAC;EACDmB,QAAQ,EAAE,SAAAA,CAAA,EAAW;IACnB,OAAO,YAAY,GAAG,IAAI,CAACpB,CAAC,GAAG,GAAG,GAAG,IAAI,CAACC,CAAC,GAAG,UAAU,GAAG,IAAI,CAACF,CAAC,GAAG,GAAG;EACzE;AACF,CAAC;AAED,OAAO,IAAIsB,QAAQ,GAAG,IAAIvB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAE5CwB,SAAS,CAACpB,SAAS,GAAGJ,SAAS,CAACI,SAAS;AAEzC,eAAe,SAASoB,SAASA,CAACC,IAAI,EAAE;EACtC,OAAO,CAACA,IAAI,CAACC,MAAM,EAAE,IAAI,EAAED,IAAI,GAAGA,IAAI,CAACE,UAAU,CAAC,EAAE,OAAOJ,QAAQ;EACnE,OAAOE,IAAI,CAACC,MAAM;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}