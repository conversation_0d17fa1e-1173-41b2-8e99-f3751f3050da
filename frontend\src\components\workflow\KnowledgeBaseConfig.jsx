import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Upload, 
  FileText, 
  Trash2, 
  CheckCircle,
  AlertCircle,
  Loader2,
  File,
  Link,
  X
} from 'lucide-react';
import { documentService } from '../../services/documentService';

const KnowledgeBaseConfig = ({ nodeData, onConfigChange }) => {
  const [documents, setDocuments] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [selectedDocuments, setSelectedDocuments] = useState(nodeData?.config?.selectedDocuments || []);
  const [maxResults, setMaxResults] = useState(nodeData?.config?.maxResults || 5);
  const [similarityThreshold, setSimilarityThreshold] = useState(nodeData?.config?.similarityThreshold || 0.7);

  // Load documents on component mount
  useEffect(() => {
    loadDocuments();
  }, []);

  // Update parent component when config changes
  useEffect(() => {
    onConfigChange({
      selectedDocuments,
      maxResults,
      similarityThreshold
    });
  }, [selectedDocuments, maxResults, similarityThreshold, onConfigChange]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const docs = await documentService.getDocuments();
      setDocuments(docs);
    } catch (err) {
      setError('Failed to load documents: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploading(true);
    setError(null);
    setSuccess(null);
    setUploadProgress(0);

    try {
      // Validate file
      documentService.validateFile(file);

      // Upload with progress tracking
      const result = await documentService.uploadDocument(file, (progress) => {
        setUploadProgress(progress);
      });

      setSuccess(`Document "${file.name}" uploaded successfully! Extracted ${result.page_count} pages.`);
      
      // Reload documents list
      await loadDocuments();
      
      // Clear the input
      event.target.value = '';
      
      // Auto-select the newly uploaded document
      if (result.id) {
        setSelectedDocuments(prev => [...prev, result.id]);
      }

    } catch (err) {
      setError('Upload failed: ' + err.message);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDocumentToggle = (documentId) => {
    setSelectedDocuments(prev => {
      if (prev.includes(documentId)) {
        return prev.filter(id => id !== documentId);
      } else {
        return [...prev, documentId];
      }
    });
  };

  const handleDeleteDocument = async (documentId) => {
    if (!window.confirm('Are you sure you want to delete this document?')) {
      return;
    }

    try {
      await documentService.deleteDocument(documentId);
      setSuccess('Document deleted successfully');
      
      // Remove from selected documents
      setSelectedDocuments(prev => prev.filter(id => id !== documentId));
      
      // Reload documents list
      await loadDocuments();
    } catch (err) {
      setError('Delete failed: ' + err.message);
    }
  };

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Knowledge Base Configuration</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Upload PDF documents and configure how they're used for context retrieval.
        </p>
      </div>

      {/* Upload Section */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>Upload PDF Document</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="border-2 border-dashed border-border rounded-lg p-4 text-center">
              <File className="w-8 h-8 mx-auto text-muted-foreground mb-2" />
              <p className="text-xs text-muted-foreground mb-2">
                Select a PDF file (max 10MB)
              </p>
              <Input
                type="file"
                accept=".pdf"
                onChange={handleFileUpload}
                disabled={uploading}
                className="max-w-xs mx-auto text-xs"
              />
            </div>

            {uploading && (
              <div className="space-y-2">
                <div className="flex items-center justify-center space-x-2 text-xs text-muted-foreground">
                  <Loader2 className="w-3 h-3 animate-spin" />
                  <span>Uploading and processing... {Math.round(uploadProgress)}%</span>
                </div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Messages */}
      {error && (
        <Alert className="border-destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-destructive flex items-center justify-between">
            <span>{error}</span>
            <Button variant="ghost" size="sm" onClick={clearMessages}>
              <X className="w-3 h-3" />
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-500">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <AlertDescription className="text-green-700 flex items-center justify-between">
            <span>{success}</span>
            <Button variant="ghost" size="sm" onClick={clearMessages}>
              <X className="w-3 h-3" />
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Configuration Settings */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Retrieval Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <Label htmlFor="maxResults" className="text-xs">Max Results</Label>
            <Input
              id="maxResults"
              type="number"
              min="1"
              max="20"
              value={maxResults}
              onChange={(e) => setMaxResults(parseInt(e.target.value) || 5)}
              className="text-xs"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Maximum number of relevant chunks to retrieve
            </p>
          </div>

          <div>
            <Label htmlFor="similarityThreshold" className="text-xs">Similarity Threshold</Label>
            <Input
              id="similarityThreshold"
              type="number"
              min="0"
              max="1"
              step="0.1"
              value={similarityThreshold}
              onChange={(e) => setSimilarityThreshold(parseFloat(e.target.value) || 0.7)}
              className="text-xs"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Minimum similarity score (0.0 - 1.0)
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Documents List */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FileText className="w-4 h-4" />
              <span>Available Documents ({documents.length})</span>
            </div>
            <Button onClick={loadDocuments} variant="outline" size="sm" disabled={loading}>
              {loading ? <Loader2 className="w-3 h-3 animate-spin" /> : 'Refresh'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
              <span className="text-xs">Loading documents...</span>
            </div>
          ) : documents.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-xs">No documents uploaded yet</p>
            </div>
          ) : (
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  className={`flex items-center justify-between p-2 border rounded text-xs transition-colors ${
                    selectedDocuments.includes(doc.id) 
                      ? 'bg-primary/10 border-primary' 
                      : 'hover:bg-muted/50'
                  }`}
                >
                  <div className="flex items-center space-x-2 flex-1">
                    <input
                      type="checkbox"
                      checked={selectedDocuments.includes(doc.id)}
                      onChange={() => handleDocumentToggle(doc.id)}
                      className="rounded"
                    />
                    <FileText className="w-4 h-4 text-blue-500 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{doc.original_filename}</p>
                      <p className="text-muted-foreground">
                        {documentService.formatFileSize(doc.file_size)} • {doc.page_count} pages
                        {doc.embeddings_generated && (
                          <span className="text-green-600 ml-2">✓ Processed</span>
                        )}
                      </p>
                    </div>
                  </div>
                  <Button
                    onClick={() => handleDeleteDocument(doc.id)}
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:text-destructive p-1"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {selectedDocuments.length > 0 && (
            <div className="mt-3 pt-3 border-t">
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <Link className="w-3 h-3" />
                <span>{selectedDocuments.length} document(s) selected for this knowledge base</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default KnowledgeBaseConfig;
