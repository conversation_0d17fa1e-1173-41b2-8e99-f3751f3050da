@echo off
echo 🚀 Starting GenAI Workflow Builder Backend...
echo ================================================

cd /d "%~dp0backend"

echo 📋 Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

echo 📦 Checking dependencies...
pip show fastapi >nul 2>&1
if %errorlevel% neq 0 (
    echo 📥 Installing dependencies...
    pip install -r requirements.txt
)

echo 🐘 Checking PostgreSQL connection...
python -c "import psycopg2; conn = psycopg2.connect('postgresql://postgres:postgres123@localhost:5432/genai_workflow'); print('✅ PostgreSQL connected'); conn.close()" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  PostgreSQL connection failed. Please ensure:
    echo    1. PostgreSQL is installed and running
    echo    2. Database 'genai_workflow' exists
    echo    3. Password is set to 'postgres123'
    echo.
    echo 🔧 You can create the database with:
    echo    psql -U postgres -c "CREATE DATABASE genai_workflow;"
    echo.
)

echo 🌟 Starting FastAPI server...
echo 📡 Backend will be available at: http://localhost:8000
echo 📚 API Documentation at: http://localhost:8000/docs
echo.
echo Press Ctrl+C to stop the server
echo ================================================

python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

pause
