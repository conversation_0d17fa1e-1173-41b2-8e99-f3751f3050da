import React from 'react';
import { Handle, Position } from 'reactflow';
import { Card, CardContent } from '../ui/card';
import { MessageCircle } from 'lucide-react';

const UserQueryNode = ({ data, selected }) => {
  return (
    <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-500 text-white rounded-lg">
            <MessageCircle className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-foreground text-sm">
              {data.label}
            </h3>
            <p className="text-xs text-muted-foreground mt-1">
              Entry point for user questions
            </p>
          </div>
        </div>
        
        <div className="mt-3">
          <div className="text-xs text-muted-foreground mb-1">Preview:</div>
          <div className="p-2 bg-muted rounded text-xs">
            {data.config?.placeholder || 'Enter your question...'}
          </div>
        </div>
      </CardContent>
      
      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />
    </Card>
  );
};

export default UserQueryNode;
