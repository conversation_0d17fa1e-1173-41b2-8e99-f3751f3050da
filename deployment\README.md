# Deployment Guide

This directory contains deployment configurations for the GenAI Workflow Builder application.

## Docker Compose Deployment

### Prerequisites

- Docker and Docker Compose installed
- OpenAI API key

### Quick Start

1. **Set up environment variables:**
   ```bash
   # Create .env file in the project root
   cp .env.example .env
   # Edit .env with your actual values
   ```

2. **Start all services:**
   ```bash
   cd deployment
   docker-compose up -d
   ```

3. **Check service status:**
   ```bash
   docker-compose ps
   ```

4. **View logs:**
   ```bash
   # All services
   docker-compose logs -f
   
   # Specific service
   docker-compose logs -f backend
   ```

### Services

| Service | Port | Description |
|---------|------|-------------|
| Frontend | 3000 | React application |
| Backend | 8000 | FastAPI server |
| PostgreSQL | 5432 | Database |
| Redis | 6379 | Caching (optional) |

### Environment Variables

Create a `.env` file in the project root with:

```env
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional
SECRET_KEY=your-secret-key-change-in-production
DEBUG=false
```

### Health Checks

- Backend: http://localhost:8000/health
- Frontend: http://localhost:3000
- API Documentation: http://localhost:8000/docs

### Data Persistence

The following volumes are created for data persistence:

- `postgres_data`: PostgreSQL database files
- `backend_uploads`: Uploaded PDF files
- `backend_chroma`: ChromaDB vector database
- `redis_data`: Redis cache data

### Stopping Services

```bash
# Stop all services
docker-compose down

# Stop and remove volumes (WARNING: This will delete all data)
docker-compose down -v
```

### Scaling

To scale the backend service:

```bash
docker-compose up -d --scale backend=3
```

Note: You'll need a load balancer for multiple backend instances.

### Troubleshooting

1. **Database connection issues:**
   ```bash
   # Check if PostgreSQL is running
   docker-compose logs postgres
   
   # Connect to database manually
   docker-compose exec postgres psql -U postgres -d genai_workflow
   ```

2. **Backend startup issues:**
   ```bash
   # Check backend logs
   docker-compose logs backend
   
   # Restart backend service
   docker-compose restart backend
   ```

3. **Frontend build issues:**
   ```bash
   # Rebuild frontend
   docker-compose build frontend
   docker-compose up -d frontend
   ```

### Production Considerations

1. **Security:**
   - Change default passwords
   - Use strong SECRET_KEY
   - Enable HTTPS
   - Restrict database access

2. **Performance:**
   - Use production-grade database
   - Implement caching strategy
   - Configure resource limits

3. **Monitoring:**
   - Add health check endpoints
   - Implement logging aggregation
   - Set up monitoring and alerting

4. **Backup:**
   - Regular database backups
   - Backup uploaded files
   - Backup ChromaDB data

### Development vs Production

For development, you can run services individually:

```bash
# Start only database
docker-compose up -d postgres redis

# Run backend locally
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload

# Run frontend locally
cd frontend
npm install
npm start
```
