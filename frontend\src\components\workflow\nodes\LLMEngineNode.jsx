import React, { memo, useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, useReactFlow } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Input } from '../../ui/input';
import { Textarea } from '../../ui/textarea';
import { Brain, Eye, EyeOff } from 'lucide-react';

const LLMEngineNode = ({ id, data, selected }) => {
  const { setNodes } = useReactFlow();
  const [apiKey, setApiKey] = useState(data?.config?.apiKey || '');
  const [model, setModel] = useState(data?.config?.model || 'gpt-3.5-turbo');
  const [prompt, setPrompt] = useState(data?.config?.prompt || '');
  const [temperature, setTemperature] = useState(data?.config?.temperature || 0.7);
  const [showApiKey, setShowApi<PERSON>ey] = useState(false);

  // Update node data when any config changes
  useEffect(() => {
    const updateNodeData = () => {
      setNodes((nodes) =>
        nodes.map((node) =>
          node.id === id
            ? {
                ...node,
                data: {
                  ...node.data,
                  config: {
                    ...node.data.config,
                    apiKey,
                    model,
                    prompt,
                    temperature,
                  },
                },
              }
            : node
        )
      );
    };

    const debounceTimer = setTimeout(updateNodeData, 300);
    return () => clearTimeout(debounceTimer);
  }, [apiKey, model, prompt, temperature, id, setNodes]);

  return (
    <Card className={`min-w-[320px] max-w-[380px] ${selected ? 'ring-2 ring-purple-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center space-x-2">
          <div className="p-1.5 rounded-lg bg-purple-500 text-white">
            <Brain className="w-4 h-4" />
          </div>
          <CardTitle className="text-sm">LLM Engine</CardTitle>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* API Key Input */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-700">
            API Key
          </label>
          <div className="relative">
            <Input
              type={showApiKey ? "text" : "password"}
              placeholder="sk-..."
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="text-sm pr-10"
            />
            <button
              type="button"
              onClick={() => setShowApiKey(!showApiKey)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
        </div>

        {/* Model Selector */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-700">
            Model
          </label>
          <select
            value={model}
            onChange={(e) => setModel(e.target.value)}
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          >
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
            <option value="gpt-4">GPT-4</option>
            <option value="gpt-4-turbo">GPT-4 Turbo</option>
          </select>
        </div>

        {/* System Prompt */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-700">
            System Prompt
          </label>
          <Textarea
            placeholder="You are a helpful AI assistant..."
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            className="text-sm resize-none"
            rows={3}
          />
        </div>

        {/* Temperature Slider */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-700">
            Temperature: {temperature}
          </label>
          <input
            type="range"
            min="0"
            max="2"
            step="0.1"
            value={temperature}
            onChange={(e) => setTemperature(parseFloat(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>Focused</span>
            <span>Creative</span>
          </div>
        </div>

        {/* Status */}
        <div className="bg-purple-50 p-2 rounded text-xs">
          <div className="font-medium text-purple-800 mb-1">Configuration:</div>
          <div className="text-purple-700 space-y-1">
            <div>Model: {model}</div>
            <div>Temperature: {temperature}</div>
            <div>API Key: {apiKey ? '••••••••' : 'Not set'}</div>
          </div>
        </div>
      </CardContent>

      {/* Input Handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="query"
        style={{ top: '30%' }}
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />
      
      <Handle
        type="target"
        position={Position.Left}
        id="context"
        style={{ top: '70%' }}
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="response"
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(LLMEngineNode);
