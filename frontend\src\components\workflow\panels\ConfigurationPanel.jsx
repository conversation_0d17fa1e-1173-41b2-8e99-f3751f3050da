import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Input } from '../../ui/input';
import { Button } from '../../ui/button';
import { 
  Settings, 
  Save, 
  RotateCcw,
  Upload,
  Globe,
  Sliders,
  FileText,
  MessageSquare
} from 'lucide-react';

const ConfigurationPanel = ({ selectedNode, onUpdateNode, onClose }) => {
  const [config, setConfig] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (selectedNode) {
      setConfig(selectedNode.data?.config || {});
      setHasChanges(false);
    }
  }, [selectedNode]);

  const handleConfigChange = (key, value) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    if (selectedNode && onUpdateNode) {
      onUpdateNode(selectedNode.id, {
        ...selectedNode.data,
        config
      });
      setHasChanges(false);
    }
  };

  const handleReset = () => {
    if (selectedNode) {
      setConfig(selectedNode.data?.config || {});
      setHasChanges(false);
    }
  };

  if (!selectedNode) {
    return (
      <div className="w-80 xl:w-96 bg-background border-l border-border flex flex-col shadow-lg">
        <div className="p-4 border-b border-border">
          <h3 className="text-lg font-semibold text-foreground">Configuration</h3>
          <p className="text-sm text-muted-foreground">Select a component to configure</p>
        </div>
        
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="text-center">
            <Settings className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              No Component Selected
            </h3>
            <p className="text-sm text-muted-foreground">
              Click on a component in the workflow to configure its settings
            </p>
          </div>
        </div>
      </div>
    );
  }

  const renderConfigFields = () => {
    switch (selectedNode.type) {
      case 'userQuery':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">
                Placeholder Text
              </label>
              <Input
                value={config.placeholder || ''}
                onChange={(e) => handleConfigChange('placeholder', e.target.value)}
                placeholder="Enter your question..."
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Text shown in the query input field
              </p>
            </div>
          </div>
        );

      case 'knowledgeBase':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">
                Maximum Results
              </label>
              <Input
                type="number"
                value={config.maxResults || 5}
                onChange={(e) => handleConfigChange('maxResults', parseInt(e.target.value))}
                min="1"
                max="20"
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Number of relevant documents to retrieve
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-foreground">
                Similarity Threshold
              </label>
              <Input
                type="number"
                value={config.similarityThreshold || 0.7}
                onChange={(e) => handleConfigChange('similarityThreshold', parseFloat(e.target.value))}
                min="0"
                max="1"
                step="0.1"
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Minimum similarity score for document retrieval
              </p>
            </div>

            <div className="p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <Upload className="w-4 h-4" />
                <span className="text-sm font-medium">Document Upload</span>
              </div>
              <p className="text-xs text-muted-foreground">
                Upload PDF documents to build your knowledge base. Documents will be processed and embedded automatically.
              </p>
            </div>
          </div>
        );

      case 'llmEngine':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">
                Model
              </label>
              <select
                value={config.model || 'gpt-3.5-turbo'}
                onChange={(e) => handleConfigChange('model', e.target.value)}
                className="mt-1 w-full h-10 px-3 py-2 text-sm border border-input bg-background rounded-md"
              >
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="gpt-4">GPT-4</option>
                <option value="gpt-4-turbo">GPT-4 Turbo</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium text-foreground">
                Temperature
              </label>
              <Input
                type="number"
                value={config.temperature || 0.7}
                onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
                min="0"
                max="2"
                step="0.1"
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Controls randomness in responses (0 = deterministic, 2 = very random)
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-foreground">
                Max Tokens
              </label>
              <Input
                type="number"
                value={config.maxTokens || 1000}
                onChange={(e) => handleConfigChange('maxTokens', parseInt(e.target.value))}
                min="100"
                max="4000"
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Maximum length of the generated response
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-foreground">
                Custom System Prompt
              </label>
              <textarea
                value={config.systemPrompt || ''}
                onChange={(e) => handleConfigChange('systemPrompt', e.target.value)}
                placeholder="You are a helpful AI assistant..."
                className="mt-1 w-full min-h-[80px] px-3 py-2 text-sm border border-input bg-background rounded-md resize-none"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Custom instructions for the AI model
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="enableWebSearch"
                checked={config.enableWebSearch || false}
                onChange={(e) => handleConfigChange('enableWebSearch', e.target.checked)}
                className="rounded border-input"
              />
              <label htmlFor="enableWebSearch" className="text-sm font-medium text-foreground flex items-center space-x-1">
                <Globe className="w-4 h-4" />
                <span>Enable Web Search</span>
              </label>
            </div>
          </div>
        );

      case 'output':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">
                Output Format
              </label>
              <select
                value={config.format || 'text'}
                onChange={(e) => handleConfigChange('format', e.target.value)}
                className="mt-1 w-full h-10 px-3 py-2 text-sm border border-input bg-background rounded-md"
              >
                <option value="text">Plain Text</option>
                <option value="markdown">Markdown</option>
                <option value="html">HTML</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showSources"
                checked={config.showSources || false}
                onChange={(e) => handleConfigChange('showSources', e.target.checked)}
                className="rounded border-input"
              />
              <label htmlFor="showSources" className="text-sm font-medium text-foreground flex items-center space-x-1">
                <FileText className="w-4 h-4" />
                <span>Show Source Citations</span>
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="enableFollowUp"
                checked={config.enableFollowUp !== false}
                onChange={(e) => handleConfigChange('enableFollowUp', e.target.checked)}
                className="rounded border-input"
              />
              <label htmlFor="enableFollowUp" className="text-sm font-medium text-foreground flex items-center space-x-1">
                <MessageSquare className="w-4 h-4" />
                <span>Enable Follow-up Questions</span>
              </label>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-8">
            <Sliders className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              No configuration options available for this component
            </p>
          </div>
        );
    }
  };

  return (
    <div className="w-80 xl:w-96 bg-background border-l border-border flex flex-col shadow-lg">
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">Configuration</h3>
            <p className="text-sm text-muted-foreground">{selectedNode.data?.label || selectedNode.type}</p>
          </div>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              ×
            </Button>
          )}
        </div>
      </div>
      
      <div className="flex-1 p-4 overflow-y-auto">
        {renderConfigFields()}
      </div>

      <div className="p-4 border-t border-border space-y-2">
        <Button 
          onClick={handleSave} 
          disabled={!hasChanges}
          className="w-full" 
          size="sm"
        >
          <Save className="w-4 h-4 mr-2" />
          Save Changes
        </Button>
        
        <Button 
          onClick={handleReset} 
          variant="outline" 
          disabled={!hasChanges}
          className="w-full" 
          size="sm"
        >
          <RotateCcw className="w-4 h-4 mr-2" />
          Reset
        </Button>
      </div>
    </div>
  );
};

export default ConfigurationPanel;
