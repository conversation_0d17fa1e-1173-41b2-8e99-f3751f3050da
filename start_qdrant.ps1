# PowerShell script to download and run Qdrant locally
Write-Host "Starting Qdrant Vector Database..." -ForegroundColor Green

# Create qdrant directory if it doesn't exist
$qdrantDir = "qdrant"
if (-not (Test-Path $qdrantDir)) {
    New-Item -ItemType Directory -Path $qdrantDir
    Write-Host "Created qdrant directory" -ForegroundColor Yellow
}

# Check if Qdrant executable exists
$qdrantExe = "$qdrantDir\qdrant.exe"
if (-not (Test-Path $qdrantExe)) {
    Write-Host "Qdrant executable not found. Please download Qdrant from:" -ForegroundColor Red
    Write-Host "https://github.com/qdrant/qdrant/releases/download/v1.7.0/qdrant-x86_64-pc-windows-msvc.zip" -ForegroundColor Yellow
    Write-Host "Extract it to the 'qdrant' folder and run this script again." -ForegroundColor Yellow
    exit 1
}

# Start Qdrant
Write-Host "Starting Qdrant on port 6333..." -ForegroundColor Green
Set-Location $qdrantDir
Start-Process -FilePath ".\qdrant.exe" -ArgumentList "--config-path", "config.yaml" -NoNewWindow
Set-Location ..

Write-Host "Qdrant should be starting on http://localhost:6333" -ForegroundColor Green
Write-Host "Check http://localhost:6333/health to verify it's running" -ForegroundColor Yellow
