"""
ChromaDB service for vector embeddings storage and similarity search.
Integrates with OpenAI embeddings API for text vectorization.
"""

import os
import openai
import chromadb
from chromadb.config import Settings
from typing import List, Dict, Any, Optional, Tuple
import logging
import hashlib
import json
from datetime import datetime

from app.core.config import settings

logger = logging.getLogger(__name__)


class ChromaService:
    """Service for managing vector embeddings with ChromaDB."""
    
    def __init__(self):
        """Initialize ChromaDB client and OpenAI API."""
        # Set OpenAI API key
        openai.api_key = settings.openai_api_key
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=settings.chroma_persist_directory,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # Collection name for document embeddings
        self.collection_name = "document_embeddings"
        
        # Get or create collection
        try:
            self.collection = self.client.get_collection(name=self.collection_name)
            logger.info(f"Connected to existing ChromaDB collection: {self.collection_name}")
        except Exception:
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "Document text embeddings for similarity search"}
            )
            logger.info(f"Created new ChromaDB collection: {self.collection_name}")
    
    def _chunk_text(self, text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """
        Split text into overlapping chunks for embedding.
        
        Args:
            text: Text to chunk
            chunk_size: Maximum size of each chunk
            chunk_overlap: Number of characters to overlap between chunks
            
        Returns:
            List of text chunks
        """
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # If this is not the last chunk, try to break at a sentence or word boundary
            if end < len(text):
                # Look for sentence boundary (period followed by space)
                sentence_end = text.rfind('. ', start, end)
                if sentence_end > start:
                    end = sentence_end + 1
                else:
                    # Look for word boundary (space)
                    word_end = text.rfind(' ', start, end)
                    if word_end > start:
                        end = word_end
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position with overlap
            start = end - chunk_overlap if end < len(text) else end
        
        return chunks
    
    async def _get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Get embeddings for a list of texts using OpenAI API.

        Args:
            texts: List of texts to embed

        Returns:
            List of embedding vectors

        Raises:
            Exception: If OpenAI API call fails
        """
        try:
            from openai import OpenAI
            client = OpenAI(api_key=settings.openai_api_key)

            response = client.embeddings.create(
                model="text-embedding-ada-002",
                input=texts
            )

            embeddings = [item.embedding for item in response.data]
            logger.info(f"Generated {len(embeddings)} embeddings")
            return embeddings

        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise Exception(f"Failed to generate embeddings: {str(e)}")
    
    def _generate_chunk_id(self, document_id: int, chunk_index: int, chunk_text: str) -> str:
        """
        Generate unique ID for a text chunk.
        
        Args:
            document_id: ID of the source document
            chunk_index: Index of the chunk within the document
            chunk_text: Text content of the chunk
            
        Returns:
            Unique chunk ID
        """
        # Create hash of chunk content for uniqueness
        content_hash = hashlib.md5(chunk_text.encode()).hexdigest()[:8]
        return f"doc_{document_id}_chunk_{chunk_index}_{content_hash}"
    
    async def generate_embeddings(
        self, 
        document_id: int, 
        text: str, 
        chunk_size: int = 1000, 
        chunk_overlap: int = 200
    ) -> Dict[str, Any]:
        """
        Generate and store embeddings for document text.
        
        Args:
            document_id: ID of the document
            text: Text content to embed
            chunk_size: Size of text chunks
            chunk_overlap: Overlap between chunks
            
        Returns:
            Dictionary with processing results
            
        Raises:
            Exception: If embedding generation fails
        """
        try:
            # Split text into chunks
            chunks = self._chunk_text(text, chunk_size, chunk_overlap)
            logger.info(f"Split document {document_id} into {len(chunks)} chunks")
            
            if not chunks:
                return {
                    "document_id": document_id,
                    "chunks_processed": 0,
                    "embeddings_generated": False,
                    "message": "No text content to process"
                }
            
            # Generate embeddings for chunks
            embeddings = await self._get_embeddings(chunks)
            
            # Prepare data for ChromaDB
            ids = []
            metadatas = []
            documents = []
            
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                chunk_id = self._generate_chunk_id(document_id, i, chunk)
                
                ids.append(chunk_id)
                documents.append(chunk)
                metadatas.append({
                    "document_id": document_id,
                    "chunk_index": i,
                    "chunk_size": len(chunk),
                    "timestamp": datetime.utcnow().isoformat()
                })
            
            # Store in ChromaDB
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(f"Stored {len(embeddings)} embeddings for document {document_id}")
            
            return {
                "document_id": document_id,
                "chunks_processed": len(chunks),
                "embeddings_generated": True,
                "message": f"Successfully generated embeddings for {len(chunks)} chunks"
            }
            
        except Exception as e:
            logger.error(f"Error generating embeddings for document {document_id}: {e}")
            raise Exception(f"Failed to generate embeddings: {str(e)}")
    
    async def get_relevant_context(
        self, 
        query: str, 
        document_id: Optional[int] = None, 
        n_results: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get relevant text chunks for a query using similarity search.
        
        Args:
            query: Search query
            document_id: Optional document ID to filter results
            n_results: Number of results to return
            
        Returns:
            List of relevant text chunks with metadata
            
        Raises:
            Exception: If search fails
        """
        try:
            # Generate embedding for query
            query_embedding = await self._get_embeddings([query])
            
            # Prepare where clause for filtering
            where_clause = None
            if document_id is not None:
                where_clause = {"document_id": document_id}
            
            # Search for similar chunks
            results = self.collection.query(
                query_embeddings=query_embedding,
                n_results=n_results,
                where=where_clause,
                include=["documents", "metadatas", "distances"]
            )
            
            # Format results
            relevant_chunks = []
            if results['documents'] and results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    relevant_chunks.append({
                        "text": doc,
                        "metadata": metadata,
                        "similarity_score": 1 - distance,  # Convert distance to similarity
                        "rank": i + 1
                    })
            
            logger.info(f"Found {len(relevant_chunks)} relevant chunks for query")
            return relevant_chunks
            
        except Exception as e:
            logger.error(f"Error searching for relevant context: {e}")
            raise Exception(f"Failed to search for relevant context: {str(e)}")
    
    def delete_document_embeddings(self, document_id: int) -> bool:
        """
        Delete all embeddings for a specific document.
        
        Args:
            document_id: ID of the document
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get all chunk IDs for the document
            results = self.collection.get(
                where={"document_id": document_id},
                include=["metadatas"]
            )
            
            if results['ids']:
                # Delete the chunks
                self.collection.delete(ids=results['ids'])
                logger.info(f"Deleted {len(results['ids'])} embeddings for document {document_id}")
                return True
            else:
                logger.info(f"No embeddings found for document {document_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error deleting embeddings for document {document_id}: {e}")
            return False
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the ChromaDB collection.
        
        Returns:
            Dictionary with collection statistics
        """
        try:
            count = self.collection.count()
            return {
                "total_embeddings": count,
                "collection_name": self.collection_name,
                "persist_directory": settings.chroma_persist_directory
            }
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {
                "total_embeddings": 0,
                "collection_name": self.collection_name,
                "error": str(e)
            }
