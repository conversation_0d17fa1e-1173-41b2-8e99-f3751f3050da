{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Palette = createLucideIcon(\"Palette\", [[\"circle\", {\n  cx: \"13.5\",\n  cy: \"6.5\",\n  r: \".5\",\n  key: \"1xcu5\"\n}], [\"circle\", {\n  cx: \"17.5\",\n  cy: \"10.5\",\n  r: \".5\",\n  key: \"736e4u\"\n}], [\"circle\", {\n  cx: \"8.5\",\n  cy: \"7.5\",\n  r: \".5\",\n  key: \"clrty\"\n}], [\"circle\", {\n  cx: \"6.5\",\n  cy: \"12.5\",\n  r: \".5\",\n  key: \"1s4xz9\"\n}], [\"path\", {\n  d: \"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z\",\n  key: \"12rzf8\"\n}]]);\nexport { Palette as default };", "map": {"version": 3, "names": ["Palette", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\palette.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Palette\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMy41IiBjeT0iNi41IiByPSIuNSIgLz4KICA8Y2lyY2xlIGN4PSIxNy41IiBjeT0iMTAuNSIgcj0iLjUiIC8+CiAgPGNpcmNsZSBjeD0iOC41IiBjeT0iNy41IiByPSIuNSIgLz4KICA8Y2lyY2xlIGN4PSI2LjUiIGN5PSIxMi41IiByPSIuNSIgLz4KICA8cGF0aCBkPSJNMTIgMkM2LjUgMiAyIDYuNSAyIDEyczQuNSAxMCAxMCAxMGMuOTI2IDAgMS42NDgtLjc0NiAxLjY0OC0xLjY4OCAwLS40MzctLjE4LS44MzUtLjQzNy0xLjEyNS0uMjktLjI4OS0uNDM4LS42NTItLjQzOC0xLjEyNWExLjY0IDEuNjQgMCAwIDEgMS42NjgtMS42NjhoMS45OTZjMy4wNTEgMCA1LjU1NS0yLjUwMyA1LjU1NS01LjU1NEMyMS45NjUgNi4wMTIgMTcuNDYxIDIgMTIgMnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/palette\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Palette = createLucideIcon('Palette', [\n  ['circle', { cx: '13.5', cy: '6.5', r: '.5', key: '1xcu5' }],\n  ['circle', { cx: '17.5', cy: '10.5', r: '.5', key: '736e4u' }],\n  ['circle', { cx: '8.5', cy: '7.5', r: '.5', key: 'clrty' }],\n  ['circle', { cx: '6.5', cy: '12.5', r: '.5', key: '1s4xz9' }],\n  [\n    'path',\n    {\n      d: 'M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z',\n      key: '12rzf8',\n    },\n  ],\n]);\n\nexport default Palette;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAU;EAAEC,EAAI;EAAQC,EAAI;EAAOC,CAAG;EAAMC,GAAK;AAAA,CAAS,GAC3D,CAAC,QAAU;EAAEH,EAAI;EAAQC,EAAI;EAAQC,CAAG;EAAMC,GAAK;AAAA,CAAU,GAC7D,CAAC,QAAU;EAAEH,EAAI;EAAOC,EAAI;EAAOC,CAAG;EAAMC,GAAK;AAAA,CAAS,GAC1D,CAAC,QAAU;EAAEH,EAAI;EAAOC,EAAI;EAAQC,CAAG;EAAMC,GAAK;AAAA,CAAU,GAC5D,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}