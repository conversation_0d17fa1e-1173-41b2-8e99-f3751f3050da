import axios from 'axios';

// Base API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api';

// Create axios instance with default configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens, logging, etc.
api.interceptors.request.use(
  (config) => {
    // Add timestamp to requests for debugging
    config.metadata = { startTime: new Date() };
    
    // Log request in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    }
    
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors and logging
api.interceptors.response.use(
  (response) => {
    // Calculate request duration
    const duration = new Date() - response.config.metadata.startTime;
    
    // Log response in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);
    }
    
    return response;
  },
  (error) => {
    // Calculate request duration
    const duration = error.config?.metadata ? new Date() - error.config.metadata.startTime : 0;
    
    // Log error
    console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} (${duration}ms)`, {
      status: error.response?.status,
      message: error.response?.data?.detail || error.message,
      data: error.response?.data,
    });
    
    // Handle common error scenarios
    if (error.response?.status === 401) {
      // Handle unauthorized access
      console.warn('Unauthorized access - redirecting to login');
      // You could dispatch a logout action here
    } else if (error.response?.status === 500) {
      // Handle server errors
      console.error('Server error occurred');
    } else if (error.code === 'ECONNABORTED') {
      // Handle timeout
      console.error('Request timeout');
    }
    
    return Promise.reject(error);
  }
);

// Health check endpoint
export const healthCheck = async () => {
  try {
    const response = await api.get('/health');
    return response.data;
  } catch (error) {
    throw new Error(`Health check failed: ${error.message}`);
  }
};

// Generic API methods
export const apiGet = async (endpoint, params = {}) => {
  const response = await api.get(endpoint, { params });
  return response.data;
};

export const apiPost = async (endpoint, data = {}) => {
  const response = await api.post(endpoint, data);
  return response.data;
};

export const apiPut = async (endpoint, data = {}) => {
  const response = await api.put(endpoint, data);
  return response.data;
};

export const apiDelete = async (endpoint) => {
  const response = await api.delete(endpoint);
  return response.data;
};

// File upload helper
export const apiUpload = async (endpoint, file, onProgress = null) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };
  
  if (onProgress) {
    config.onUploadProgress = (progressEvent) => {
      const percentCompleted = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total
      );
      onProgress(percentCompleted);
    };
  }
  
  const response = await api.post(endpoint, formData, config);
  return response.data;
};

// Error handling utility
export const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return {
      type: 'server_error',
      status,
      message: data?.detail || data?.message || 'Server error occurred',
      details: data,
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      type: 'network_error',
      message: 'Network error - please check your connection',
      details: error.request,
    };
  } else {
    // Something else happened
    return {
      type: 'unknown_error',
      message: error.message || 'An unexpected error occurred',
      details: error,
    };
  }
};

// API status checker
export const checkApiStatus = async () => {
  try {
    await healthCheck();
    return { status: 'connected', message: 'API is accessible' };
  } catch (error) {
    return { 
      status: 'disconnected', 
      message: 'API is not accessible',
      error: handleApiError(error)
    };
  }
};

export default api;
