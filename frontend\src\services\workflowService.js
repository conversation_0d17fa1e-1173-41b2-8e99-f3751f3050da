import { apiGet, apiPost, handleApiError } from './api';

/**
 * Workflow Service
 * Handles all workflow-related API operations including execution, validation, and management
 */

/**
 * Execute a workflow with user query
 * @param {Object} params - Execution parameters
 * @param {string} params.userId - ID of the user
 * @param {string} params.query - User's query
 * @param {Object} params.workflow - Workflow definition
 * @returns {Promise<Object>} Execution response
 */
export const runWorkflow = async (params) => {
  try {
    const { userId, query, workflow } = params;
    
    // Validate required parameters
    if (!userId) {
      throw new Error('User ID is required');
    }
    
    if (!query || query.trim().length === 0) {
      throw new Error('Query is required');
    }
    
    if (!workflow || !workflow.nodes || workflow.nodes.length === 0) {
      throw new Error('Valid workflow definition is required');
    }
    
    // Validate workflow structure
    const validationResult = validateWorkflow(workflow);
    if (!validationResult.isValid) {
      throw new Error(`Invalid workflow: ${validationResult.errors.join(', ')}`);
    }
    
    const response = await apiPost('/workflow/execute', {
      user_id: userId,
      query: query.trim(),
      workflow: workflow,
    });
    
    return {
      success: true,
      data: response,
      message: 'Workflow executed successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Validate workflow definition
 * @param {Object} workflow - Workflow to validate
 * @returns {Promise<Object>} Validation response
 */
export const validateWorkflowAPI = async (workflow) => {
  try {
    if (!workflow) {
      throw new Error('Workflow definition is required');
    }
    
    const response = await apiPost('/workflow/validate', workflow);
    
    return {
      success: true,
      data: response,
      message: 'Workflow validation completed',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get workflow execution history for a user
 * @param {Object} params - Query parameters
 * @param {string} params.userId - ID of the user
 * @param {number} params.skip - Number of executions to skip
 * @param {number} params.limit - Maximum number of executions to return
 * @returns {Promise<Object>} Execution history
 */
export const getWorkflowExecutions = async (params) => {
  try {
    const { userId, skip = 0, limit = 50 } = params;
    
    if (!userId) {
      throw new Error('User ID is required');
    }
    
    const response = await apiGet(`/workflow/executions/${userId}`, { skip, limit });
    
    return {
      success: true,
      data: response,
      message: 'Execution history retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get detailed information about a specific execution
 * @param {number} executionId - ID of the execution
 * @returns {Promise<Object>} Execution details
 */
export const getExecutionDetails = async (executionId) => {
  try {
    if (!executionId) {
      throw new Error('Execution ID is required');
    }
    
    const response = await apiGet(`/workflow/execution/${executionId}`);
    
    return {
      success: true,
      data: response,
      message: 'Execution details retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get workflow execution statistics
 * @returns {Promise<Object>} Workflow statistics
 */
export const getWorkflowStats = async () => {
  try {
    const response = await apiGet('/workflow/stats');
    
    return {
      success: true,
      data: response,
      message: 'Workflow statistics retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Client-side workflow validation
 * @param {Object} workflow - Workflow to validate
 * @returns {Object} Validation result
 */
export const validateWorkflow = (workflow) => {
  const errors = [];
  
  if (!workflow) {
    errors.push('Workflow definition is required');
    return { isValid: false, errors };
  }
  
  const { nodes, connections } = workflow;
  
  // Validate nodes
  if (!nodes || !Array.isArray(nodes) || nodes.length === 0) {
    errors.push('Workflow must contain at least one node');
  } else {
    // Check for required node types
    const nodeTypes = nodes.map(node => node.type);
    const requiredTypes = ['userQuery', 'output'];
    
    requiredTypes.forEach(type => {
      if (!nodeTypes.includes(type)) {
        errors.push(`Workflow must contain a '${type}' node`);
      }
    });
    
    // Validate node structure
    nodes.forEach((node, index) => {
      if (!node.id) {
        errors.push(`Node ${index + 1} is missing an ID`);
      }
      if (!node.type) {
        errors.push(`Node ${index + 1} is missing a type`);
      }
      if (!node.position) {
        errors.push(`Node ${index + 1} is missing position information`);
      }
    });
    
    // Check for duplicate node IDs
    const nodeIds = nodes.map(node => node.id).filter(Boolean);
    const uniqueIds = [...new Set(nodeIds)];
    if (nodeIds.length !== uniqueIds.length) {
      errors.push('All node IDs must be unique');
    }
  }
  
  // Validate connections
  if (connections && Array.isArray(connections)) {
    const nodeIds = nodes ? nodes.map(node => node.id) : [];
    
    connections.forEach((connection, index) => {
      if (!connection.source) {
        errors.push(`Connection ${index + 1} is missing a source`);
      } else if (!nodeIds.includes(connection.source)) {
        errors.push(`Connection ${index + 1} references unknown source node: ${connection.source}`);
      }
      
      if (!connection.target) {
        errors.push(`Connection ${index + 1} is missing a target`);
      } else if (!nodeIds.includes(connection.target)) {
        errors.push(`Connection ${index + 1} references unknown target node: ${connection.target}`);
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Create a default workflow template
 * @returns {Object} Default workflow definition
 */
export const createDefaultWorkflow = () => {
  return {
    nodes: [
      {
        id: 'user-query-1',
        type: 'userQuery',
        position: { x: 100, y: 100 },
        data: {
          label: 'User Query',
          config: { placeholder: 'Enter your question...' }
        }
      },
      {
        id: 'knowledge-base-1',
        type: 'knowledgeBase',
        position: { x: 400, y: 100 },
        data: {
          label: 'Knowledge Base',
          config: { documentId: null, maxResults: 5 }
        }
      },
      {
        id: 'llm-engine-1',
        type: 'llmEngine',
        position: { x: 700, y: 100 },
        data: {
          label: 'LLM Engine',
          config: {
            model: 'gpt-3.5-turbo',
            temperature: 0.7,
            maxTokens: 1000,
            enableWebSearch: false
          }
        }
      },
      {
        id: 'output-1',
        type: 'output',
        position: { x: 1000, y: 100 },
        data: {
          label: 'Output',
          config: { format: 'text', showSources: true }
        }
      }
    ],
    connections: [
      { source: 'user-query-1', target: 'knowledge-base-1' },
      { source: 'user-query-1', target: 'llm-engine-1', targetHandle: 'query' },
      { source: 'knowledge-base-1', target: 'llm-engine-1', targetHandle: 'context' },
      { source: 'llm-engine-1', target: 'output-1' }
    ]
  };
};

/**
 * Format execution time for display
 * @param {number} milliseconds - Execution time in milliseconds
 * @returns {string} Formatted time string
 */
export const formatExecutionTime = (milliseconds) => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(1)}s`;
  } else {
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = ((milliseconds % 60000) / 1000).toFixed(0);
    return `${minutes}m ${seconds}s`;
  }
};

/**
 * Get workflow complexity score
 * @param {Object} workflow - Workflow to analyze
 * @returns {Object} Complexity analysis
 */
export const getWorkflowComplexity = (workflow) => {
  if (!workflow || !workflow.nodes) {
    return { score: 0, level: 'invalid' };
  }
  
  const nodeCount = workflow.nodes.length;
  const connectionCount = workflow.connections ? workflow.connections.length : 0;
  
  // Simple complexity calculation
  const score = nodeCount + (connectionCount * 0.5);
  
  let level;
  if (score <= 2) level = 'simple';
  else if (score <= 5) level = 'moderate';
  else if (score <= 10) level = 'complex';
  else level = 'very complex';
  
  return {
    score: Math.round(score * 10) / 10,
    level,
    nodeCount,
    connectionCount,
  };
};

export default {
  runWorkflow,
  validateWorkflowAPI,
  getWorkflowExecutions,
  getExecutionDetails,
  getWorkflowStats,
  validateWorkflow,
  createDefaultWorkflow,
  formatExecutionTime,
  getWorkflowComplexity,
};
