{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PowerCircle = createLucideIcon(\"PowerCircle\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"M12 12V6\",\n  key: \"30zewn\"\n}], [\"path\", {\n  d: \"M8 7.5A6.1 6.1 0 0 0 12 18a6 6 0 0 0 4-10.5\",\n  key: \"1r0tk2\"\n}]]);\nexport { PowerCircle as default };", "map": {"version": 3, "names": ["PowerCircle", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\power-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PowerCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMTJWNiIgLz4KICA8cGF0aCBkPSJNOCA3LjVBNi4xIDYuMSAwIDAgMCAxMiAxOGE2IDYgMCAwIDAgNC0xMC41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/power-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PowerCircle = createLucideIcon('PowerCircle', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 12V6', key: '30zewn' }],\n  ['path', { d: 'M8 7.5A6.1 6.1 0 0 0 12 18a6 6 0 0 0 4-10.5', key: '1r0tk2' }],\n]);\n\nexport default PowerCircle;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMC,GAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,6CAA+C;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}