import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import WorkflowBuilder from './pages/WorkflowBuilder';
import ChatUI from './pages/ChatUI';
import Navigation from './components/layout/Navigation';
import { ToastProvider } from './components/ui/toast';

function App() {
  return (
    <ToastProvider>
      <Router>
        <div className="min-h-screen bg-background text-foreground">
          <Navigation />
          <main className="h-screen pt-16 overflow-hidden">
            <Routes>
              <Route path="/" element={<Navigate to="/workflow" replace />} />
              <Route path="/workflow" element={<WorkflowBuilder />} />
              <Route path="/chat" element={<ChatUI />} />
            </Routes>
          </main>
        </div>
      </Router>
    </ToastProvider>
  );
}

export default App;
