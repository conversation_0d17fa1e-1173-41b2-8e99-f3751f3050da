-- Database initialization script for GenAI Workflow Builder
-- This script creates the database and sets up initial configuration

-- Create database (if running manually)
-- CREATE DATABASE genai_workflow;

-- Connect to the database
\c genai_workflow;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- The tables will be created automatically by SQLAlchemy
-- This file serves as documentation and can be used for manual setup

-- Example queries for testing (uncomment to use):

-- Check if tables exist
-- SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- Sample data insertion (for testing purposes)
-- INSERT INTO documents (filename, original_filename, file_path, file_size, content_type, extracted_text, page_count, processed)
-- VALUES ('test.pdf', 'sample.pdf', '/uploads/test.pdf', 1024, 'application/pdf', 'Sample text content', 1, true);

-- Create indexes for better performance
-- These will be created automatically by SQLAlchemy, but can be added manually if needed

-- Index on user_id for chat messages
-- CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id);

-- Index on document_id for chat messages
-- CREATE INDEX IF NOT EXISTS idx_chat_messages_document_id ON chat_messages(document_id);

-- Index on timestamp for chat messages
-- CREATE INDEX IF NOT EXISTS idx_chat_messages_timestamp ON chat_messages(timestamp);

-- Index on user_id for workflow executions
-- CREATE INDEX IF NOT EXISTS idx_workflow_executions_user_id ON workflow_executions(user_id);

-- Index on status for workflow executions
-- CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON workflow_executions(status);

-- Index on timestamp for workflow executions
-- CREATE INDEX IF NOT EXISTS idx_workflow_executions_timestamp ON workflow_executions(timestamp);
