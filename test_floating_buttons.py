#!/usr/bin/env python3
"""
Test script to verify the floating buttons functionality:
1. Test workflow execution via API
2. Test chat functionality
3. Verify all components work together
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_workflow_execution():
    """Test the workflow execution endpoint"""
    print("🚀 Testing workflow execution...")

    # Find a document with embeddings
    docs_response = requests.get(f"{BASE_URL}/api/documents/")
    docs = docs_response.json() if docs_response.status_code == 200 else []

    # Find a document with embeddings
    doc_with_embeddings = None
    for doc in docs:
        if doc.get('embeddings_generated', False):
            doc_with_embeddings = doc['id']
            print(f"📄 Using document: {doc['original_filename']} (ID: {doc['id']})")
            break

    if not doc_with_embeddings:
        print("⚠️  No documents with embeddings found, using document ID 1")
        doc_with_embeddings = 1

    # Create a simple workflow
    workflow = {
        "nodes": [
            {
                "id": "1",
                "type": "user_query",
                "position": {"x": 100, "y": 100},
                "data": {"config": {}}
            },
            {
                "id": "2",
                "type": "knowledge_base",
                "position": {"x": 200, "y": 100},
                "data": {"config": {"selectedDocuments": [doc_with_embeddings], "maxResults": 5}}
            },
            {
                "id": "3",
                "type": "llm_engine", 
                "position": {"x": 300, "y": 100},
                "data": {"config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000}}
            },
            {
                "id": "4",
                "type": "output",
                "position": {"x": 400, "y": 100}, 
                "data": {"config": {}}
            }
        ],
        "connections": [
            {"source": "1", "target": "2"},
            {"source": "2", "target": "3"},
            {"source": "3", "target": "4"}
        ]
    }
    
    data = {
        "user_id": "test-user",
        "query": "What is my name?",
        "workflow": workflow
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/workflow/execute", json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Workflow execution successful!")
            print(f"📝 Response: {result.get('response', 'No response')[:200]}...")
            return True
        else:
            print(f"❌ Workflow execution failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Workflow execution error: {e}")
        return False

def test_chat_functionality():
    """Test the chat endpoint"""
    print("\n💬 Testing chat functionality...")

    # Find a document with embeddings
    docs_response = requests.get(f"{BASE_URL}/api/documents/")
    docs = docs_response.json() if docs_response.status_code == 200 else []

    # Find a document with embeddings
    doc_with_embeddings = None
    for doc in docs:
        if doc.get('embeddings_generated', False):
            doc_with_embeddings = doc['id']
            break

    if not doc_with_embeddings:
        doc_with_embeddings = 1

    # Create a simple workflow for chat
    workflow = {
        "nodes": [
            {
                "id": "1",
                "type": "user_query",
                "position": {"x": 100, "y": 100},
                "data": {"config": {}}
            },
            {
                "id": "2",
                "type": "knowledge_base",
                "position": {"x": 200, "y": 100},
                "data": {"config": {"selectedDocuments": [doc_with_embeddings], "maxResults": 5}}
            },
            {
                "id": "3",
                "type": "llm_engine", 
                "position": {"x": 300, "y": 100},
                "data": {"config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000}}
            },
            {
                "id": "4",
                "type": "output",
                "position": {"x": 400, "y": 100}, 
                "data": {"config": {}}
            }
        ],
        "connections": [
            {"source": "1", "target": "2"},
            {"source": "2", "target": "3"},
            {"source": "3", "target": "4"}
        ]
    }
    
    data = {
        "message": "What is my name?",
        "workflow": workflow
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/chat", json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Chat functionality working!")
            print(f"💬 Response: {result.get('response', 'No response')[:200]}...")
            return True
        else:
            print(f"❌ Chat failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat error: {e}")
        return False

def test_backend_health():
    """Test if backend is running"""
    print("🏥 Testing backend health...")
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is healthy!")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
        return False

def test_documents():
    """Test document availability"""
    print("\n📄 Testing document availability...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/documents/", timeout=5)
        if response.status_code == 200:
            docs = response.json()
            print(f"✅ Found {len(docs)} documents")
            for doc in docs:
                print(f"   📄 {doc['original_filename']} (ID: {doc['id']}, Embeddings: {doc.get('embeddings_generated', False)})")
            return len(docs) > 0
        else:
            print(f"❌ Document check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Document check error: {e}")
        return False

def main():
    print("🧪 Floating Buttons Functionality Test Suite")
    print("=" * 60)
    
    # Test backend health
    if not test_backend_health():
        print("❌ Backend not available. Please start the backend first.")
        return
    
    # Test documents
    if not test_documents():
        print("⚠️  No documents found. Upload a PDF first for full testing.")
    
    # Test workflow execution (Play button functionality)
    print("\n" + "="*40)
    workflow_success = test_workflow_execution()
    
    # Test chat functionality (Chat button functionality)
    print("\n" + "="*40)
    chat_success = test_chat_functionality()
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY:")
    print(f"   🚀 Workflow Execution (Play Button): {'✅ PASS' if workflow_success else '❌ FAIL'}")
    print(f"   💬 Chat Functionality (Chat Button): {'✅ PASS' if chat_success else '❌ FAIL'}")
    
    if workflow_success and chat_success:
        print("\n🎉 All floating button functionality is working correctly!")
    else:
        print("\n⚠️  Some functionality needs attention. Check the logs above.")

if __name__ == "__main__":
    main()
