{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\aiplanet\\\\frontend\\\\src\\\\pages\\\\ChatUI.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '../components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Input } from '../components/ui/input';\nimport { Send, Bot, User, Loader2, FileText, Zap } from 'lucide-react';\nimport { chatService } from '../services/chatService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatUI = () => {\n  _s();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    type: 'bot',\n    content: 'Hello! I\\'m your AI assistant powered by the workflow you\\'ve built. How can I help you today?',\n    timestamp: new Date()\n  }]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // Sample workflow configuration (would come from WorkflowBuilder)\n  const sampleWorkflow = {\n    nodes: [{\n      id: 1,\n      type: 'userQuery',\n      label: 'User Query',\n      config: {\n        placeholder: 'Ask me anything...'\n      }\n    }, {\n      id: 2,\n      type: 'knowledgeBase',\n      label: 'Knowledge Base',\n      config: {\n        documentId: null,\n        maxResults: 5\n      }\n    }, {\n      id: 3,\n      type: 'llmEngine',\n      label: 'LLM Engine',\n      config: {\n        model: 'gpt-3.5-turbo',\n        temperature: 0.7,\n        maxTokens: 1000,\n        enableWebSearch: false\n      }\n    }, {\n      id: 4,\n      type: 'output',\n      label: 'Output',\n      config: {\n        format: 'text',\n        showSources: true\n      }\n    }]\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n    try {\n      // Simulate workflow execution\n      const response = await chatService.sendMessage(inputMessage, sampleWorkflow);\n      const botMessage = {\n        id: Date.now() + 1,\n        type: 'bot',\n        content: response.content,\n        sources: response.sources,\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, botMessage]);\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'bot',\n        content: 'Sorry, I encountered an error while processing your request. Please try again.',\n        timestamp: new Date(),\n        isError: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTime = timestamp => {\n    return timestamp.toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-full bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-border bg-card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center w-10 h-10 bg-primary rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(Bot, {\n                className: \"w-6 h-6 text-primary-foreground\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold text-foreground\",\n                children: \"AI Assistant\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-muted-foreground\",\n                children: \"Powered by your custom workflow\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-muted-foreground\",\n                children: \"Online\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n        children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `max-w-[70%] ${message.type === 'user' ? 'bg-primary text-primary-foreground' : message.isError ? 'bg-destructive text-destructive-foreground' : 'bg-muted text-muted-foreground'} rounded-lg p-3 shadow-sm`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(User, {\n                  className: \"w-4 h-4 mt-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(Bot, {\n                  className: \"w-4 h-4 mt-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm whitespace-pre-wrap\",\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this), message.sources && message.sources.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 pt-2 border-t border-border/20\",\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs opacity-75 mb-1\",\n                    children: \"Sources:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-1\",\n                    children: message.sources.map((source, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1 text-xs opacity-75\",\n                      children: [/*#__PURE__*/_jsxDEV(FileText, {\n                        className: \"w-3 h-3\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 152,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: source\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 153,\n                        columnNumber: 31\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 151,\n                      columnNumber: 29\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs opacity-50 mt-1\",\n                  children: formatTime(message.timestamp)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 13\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-start\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-muted text-muted-foreground rounded-lg p-3 shadow-sm\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Bot, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"Thinking...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-border bg-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            value: inputMessage,\n            onChange: e => setInputMessage(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Type your message here...\",\n            disabled: isLoading,\n            className: \"flex-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSendMessage,\n            disabled: !inputMessage.trim() || isLoading,\n            size: \"icon\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(Loader2, {\n              className: \"w-4 h-4 animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Send, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xs text-muted-foreground mt-2\",\n          children: \"Press Enter to send, Shift+Enter for new line\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-80 bg-card border-l border-border flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground\",\n          children: \"Active Workflow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-muted-foreground\",\n          children: \"Current AI processing pipeline\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-4 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: sampleWorkflow.nodes.map((node, index) => /*#__PURE__*/_jsxDEV(Card, {\n            className: \"bg-background\",\n            children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n              className: \"pb-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-primary rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(CardTitle, {\n                  className: \"text-sm\",\n                  children: node.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              className: \"pt-0\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-muted-foreground\",\n                children: Object.entries(node.config).slice(0, 2).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"capitalize\",\n                    children: [key, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"truncate ml-2\",\n                    children: String(value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 25\n                  }, this)]\n                }, key, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this)]\n          }, node.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-border\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          size: \"sm\",\n          className: \"w-full\",\n          children: [/*#__PURE__*/_jsxDEV(Zap, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), \"Modify Workflow\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatUI, \"s4dgwM1BI7A06ohOwhhoiiQ6z9M=\");\n_c = ChatUI;\nexport default ChatUI;\nvar _c;\n$RefreshReg$(_c, \"ChatUI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Input", "Send", "Bot", "User", "Loader2", "FileText", "Zap", "chatService", "jsxDEV", "_jsxDEV", "ChatUI", "_s", "messages", "setMessages", "id", "type", "content", "timestamp", "Date", "inputMessage", "setInputMessage", "isLoading", "setIsLoading", "messagesEndRef", "sampleWorkflow", "nodes", "label", "config", "placeholder", "documentId", "maxResults", "model", "temperature", "maxTokens", "enableWebSearch", "format", "showSources", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "trim", "userMessage", "now", "prev", "response", "sendMessage", "botMessage", "sources", "error", "console", "errorMessage", "isError", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTime", "toLocaleTimeString", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "message", "length", "source", "index", "ref", "value", "onChange", "target", "onKeyPress", "disabled", "onClick", "size", "node", "Object", "entries", "slice", "String", "variant", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/aiplanet/frontend/src/pages/ChatUI.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '../components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Input } from '../components/ui/input';\nimport { Send, Bot, User, Loader2, FileText, Zap } from 'lucide-react';\nimport { chatService } from '../services/chatService';\n\nconst ChatUI = () => {\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      type: 'bot',\n      content: 'Hello! I\\'m your AI assistant powered by the workflow you\\'ve built. How can I help you today?',\n      timestamp: new Date(),\n    }\n  ]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  // Sample workflow configuration (would come from WorkflowBuilder)\n  const sampleWorkflow = {\n    nodes: [\n      { id: 1, type: 'userQuery', label: 'User Query', config: { placeholder: 'Ask me anything...' } },\n      { id: 2, type: 'knowledgeBase', label: 'Knowledge Base', config: { documentId: null, maxResults: 5 } },\n      { id: 3, type: 'llmEngine', label: 'LLM Engine', config: { model: 'gpt-3.5-turbo', temperature: 0.7, maxTokens: 1000, enableWebSearch: false } },\n      { id: 4, type: 'output', label: 'Output', config: { format: 'text', showSources: true } }\n    ]\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    try {\n      // Simulate workflow execution\n      const response = await chatService.sendMessage(inputMessage, sampleWorkflow);\n      \n      const botMessage = {\n        id: Date.now() + 1,\n        type: 'bot',\n        content: response.content,\n        sources: response.sources,\n        timestamp: new Date(),\n      };\n\n      setMessages(prev => [...prev, botMessage]);\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'bot',\n        content: 'Sorry, I encountered an error while processing your request. Please try again.',\n        timestamp: new Date(),\n        isError: true,\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const formatTime = (timestamp) => {\n    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  return (\n    <div className=\"flex h-full bg-background\">\n      {/* Chat Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Chat Header */}\n        <div className=\"p-4 border-b border-border bg-card\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex items-center justify-center w-10 h-10 bg-primary rounded-lg\">\n                <Bot className=\"w-6 h-6 text-primary-foreground\" />\n              </div>\n              <div>\n                <h2 className=\"text-lg font-semibold text-foreground\">AI Assistant</h2>\n                <p className=\"text-sm text-muted-foreground\">\n                  Powered by your custom workflow\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center space-x-1\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                <span className=\"text-sm text-muted-foreground\">Online</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages */}\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n          {messages.map((message) => (\n            <div\n              key={message.id}\n              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div\n                className={`max-w-[70%] ${\n                  message.type === 'user'\n                    ? 'bg-primary text-primary-foreground'\n                    : message.isError\n                    ? 'bg-destructive text-destructive-foreground'\n                    : 'bg-muted text-muted-foreground'\n                } rounded-lg p-3 shadow-sm`}\n              >\n                <div className=\"flex items-start space-x-2\">\n                  <div className=\"flex-shrink-0\">\n                    {message.type === 'user' ? (\n                      <User className=\"w-4 h-4 mt-0.5\" />\n                    ) : (\n                      <Bot className=\"w-4 h-4 mt-0.5\" />\n                    )}\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n                    \n                    {message.sources && message.sources.length > 0 && (\n                      <div className=\"mt-2 pt-2 border-t border-border/20\">\n                        <p className=\"text-xs opacity-75 mb-1\">Sources:</p>\n                        <div className=\"space-y-1\">\n                          {message.sources.map((source, index) => (\n                            <div key={index} className=\"flex items-center space-x-1 text-xs opacity-75\">\n                              <FileText className=\"w-3 h-3\" />\n                              <span>{source}</span>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                    \n                    <p className=\"text-xs opacity-50 mt-1\">\n                      {formatTime(message.timestamp)}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n          \n          {isLoading && (\n            <div className=\"flex justify-start\">\n              <div className=\"bg-muted text-muted-foreground rounded-lg p-3 shadow-sm\">\n                <div className=\"flex items-center space-x-2\">\n                  <Bot className=\"w-4 h-4\" />\n                  <div className=\"flex items-center space-x-1\">\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span className=\"text-sm\">Thinking...</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Input Area */}\n        <div className=\"p-4 border-t border-border bg-card\">\n          <div className=\"flex space-x-2\">\n            <Input\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Type your message here...\"\n              disabled={isLoading}\n              className=\"flex-1\"\n            />\n            <Button\n              onClick={handleSendMessage}\n              disabled={!inputMessage.trim() || isLoading}\n              size=\"icon\"\n            >\n              {isLoading ? (\n                <Loader2 className=\"w-4 h-4 animate-spin\" />\n              ) : (\n                <Send className=\"w-4 h-4\" />\n              )}\n            </Button>\n          </div>\n          <p className=\"text-xs text-muted-foreground mt-2\">\n            Press Enter to send, Shift+Enter for new line\n          </p>\n        </div>\n      </div>\n\n      {/* Workflow Info Sidebar */}\n      <div className=\"w-80 bg-card border-l border-border flex flex-col\">\n        <div className=\"p-4 border-b border-border\">\n          <h3 className=\"text-lg font-semibold text-foreground\">Active Workflow</h3>\n          <p className=\"text-sm text-muted-foreground\">\n            Current AI processing pipeline\n          </p>\n        </div>\n        \n        <div className=\"flex-1 p-4 overflow-y-auto\">\n          <div className=\"space-y-3\">\n            {sampleWorkflow.nodes.map((node, index) => (\n              <Card key={node.id} className=\"bg-background\">\n                <CardHeader className=\"pb-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n                    <CardTitle className=\"text-sm\">{node.label}</CardTitle>\n                  </div>\n                </CardHeader>\n                <CardContent className=\"pt-0\">\n                  <div className=\"text-xs text-muted-foreground\">\n                    {Object.entries(node.config).slice(0, 2).map(([key, value]) => (\n                      <div key={key} className=\"flex justify-between\">\n                        <span className=\"capitalize\">{key}:</span>\n                        <span className=\"truncate ml-2\">{String(value)}</span>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n        \n        <div className=\"p-4 border-t border-border\">\n          <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n            <Zap className=\"w-4 h-4 mr-2\" />\n            Modify Workflow\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ChatUI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,uBAAuB;AAChF,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,cAAc;AACtE,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,CACvC;IACEsB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,gGAAgG;IACzGC,SAAS,EAAE,IAAIC,IAAI,CAAC;EACtB,CAAC,CACF,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM+B,cAAc,GAAG9B,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM+B,cAAc,GAAG;IACrBC,KAAK,EAAE,CACL;MAAEX,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,WAAW;MAAEW,KAAK,EAAE,YAAY;MAAEC,MAAM,EAAE;QAAEC,WAAW,EAAE;MAAqB;IAAE,CAAC,EAChG;MAAEd,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,eAAe;MAAEW,KAAK,EAAE,gBAAgB;MAAEC,MAAM,EAAE;QAAEE,UAAU,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAE;IAAE,CAAC,EACtG;MAAEhB,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,WAAW;MAAEW,KAAK,EAAE,YAAY;MAAEC,MAAM,EAAE;QAAEI,KAAK,EAAE,eAAe;QAAEC,WAAW,EAAE,GAAG;QAAEC,SAAS,EAAE,IAAI;QAAEC,eAAe,EAAE;MAAM;IAAE,CAAC,EAChJ;MAAEpB,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,QAAQ;MAAEW,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE;QAAEQ,MAAM,EAAE,MAAM;QAAEC,WAAW,EAAE;MAAK;IAAE,CAAC;EAE7F,CAAC;EAED,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAf,cAAc,CAACgB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED/C,SAAS,CAAC,MAAM;IACd2C,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACzB,QAAQ,CAAC,CAAC;EAEd,MAAM8B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACvB,YAAY,CAACwB,IAAI,CAAC,CAAC,IAAItB,SAAS,EAAE;IAEvC,MAAMuB,WAAW,GAAG;MAClB9B,EAAE,EAAEI,IAAI,CAAC2B,GAAG,CAAC,CAAC;MACd9B,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEG,YAAY;MACrBF,SAAS,EAAE,IAAIC,IAAI,CAAC;IACtB,CAAC;IAEDL,WAAW,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,WAAW,CAAC,CAAC;IAC3CxB,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAMyB,QAAQ,GAAG,MAAMxC,WAAW,CAACyC,WAAW,CAAC7B,YAAY,EAAEK,cAAc,CAAC;MAE5E,MAAMyB,UAAU,GAAG;QACjBnC,EAAE,EAAEI,IAAI,CAAC2B,GAAG,CAAC,CAAC,GAAG,CAAC;QAClB9B,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE+B,QAAQ,CAAC/B,OAAO;QACzBkC,OAAO,EAAEH,QAAQ,CAACG,OAAO;QACzBjC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC;MAEDL,WAAW,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEG,UAAU,CAAC,CAAC;IAC5C,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAME,YAAY,GAAG;QACnBvC,EAAE,EAAEI,IAAI,CAAC2B,GAAG,CAAC,CAAC,GAAG,CAAC;QAClB9B,IAAI,EAAE,KAAK;QACXC,OAAO,EAAE,gFAAgF;QACzFC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBoC,OAAO,EAAE;MACX,CAAC;MACDzC,WAAW,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEO,YAAY,CAAC,CAAC;IAC9C,CAAC,SAAS;MACR/B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMiC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBjB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMkB,UAAU,GAAI3C,SAAS,IAAK;IAChC,OAAOA,SAAS,CAAC4C,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACjF,CAAC;EAED,oBACEtD,OAAA;IAAKuD,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAExCxD,OAAA;MAAKuD,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnCxD,OAAA;QAAKuD,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eACjDxD,OAAA;UAAKuD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDxD,OAAA;YAAKuD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CxD,OAAA;cAAKuD,SAAS,EAAC,kEAAkE;cAAAC,QAAA,eAC/ExD,OAAA,CAACP,GAAG;gBAAC8D,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACN5D,OAAA;cAAAwD,QAAA,gBACExD,OAAA;gBAAIuD,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvE5D,OAAA;gBAAGuD,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAE7C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5D,OAAA;YAAKuD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1CxD,OAAA;cAAKuD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CxD,OAAA;gBAAKuD,SAAS,EAAC;cAAiD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvE5D,OAAA;gBAAMuD,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5D,OAAA;QAAKuD,SAAS,EAAC,sCAAsC;QAAAC,QAAA,GAClDrD,QAAQ,CAAC0D,GAAG,CAAEC,OAAO,iBACpB9D,OAAA;UAEEuD,SAAS,EAAE,QAAQO,OAAO,CAACxD,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAe,EAAG;UAAAkD,QAAA,eAE/ExD,OAAA;YACEuD,SAAS,EAAE,eACTO,OAAO,CAACxD,IAAI,KAAK,MAAM,GACnB,oCAAoC,GACpCwD,OAAO,CAACjB,OAAO,GACf,4CAA4C,GAC5C,gCAAgC,2BACV;YAAAW,QAAA,eAE5BxD,OAAA;cAAKuD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCxD,OAAA;gBAAKuD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BM,OAAO,CAACxD,IAAI,KAAK,MAAM,gBACtBN,OAAA,CAACN,IAAI;kBAAC6D,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEnC5D,OAAA,CAACP,GAAG;kBAAC8D,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAClC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN5D,OAAA;gBAAKuD,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBxD,OAAA;kBAAGuD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEM,OAAO,CAACvD;gBAAO;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAE/DE,OAAO,CAACrB,OAAO,IAAIqB,OAAO,CAACrB,OAAO,CAACsB,MAAM,GAAG,CAAC,iBAC5C/D,OAAA;kBAAKuD,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,gBAClDxD,OAAA;oBAAGuD,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACnD5D,OAAA;oBAAKuD,SAAS,EAAC,WAAW;oBAAAC,QAAA,EACvBM,OAAO,CAACrB,OAAO,CAACoB,GAAG,CAAC,CAACG,MAAM,EAAEC,KAAK,kBACjCjE,OAAA;sBAAiBuD,SAAS,EAAC,gDAAgD;sBAAAC,QAAA,gBACzExD,OAAA,CAACJ,QAAQ;wBAAC2D,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAChC5D,OAAA;wBAAAwD,QAAA,EAAOQ;sBAAM;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,GAFbK,KAAK;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAED5D,OAAA;kBAAGuD,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EACnCL,UAAU,CAACW,OAAO,CAACtD,SAAS;gBAAC;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA1CDE,OAAO,CAACzD,EAAE;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2CZ,CACN,CAAC,EAEDhD,SAAS,iBACRZ,OAAA;UAAKuD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCxD,OAAA;YAAKuD,SAAS,EAAC,yDAAyD;YAAAC,QAAA,eACtExD,OAAA;cAAKuD,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CxD,OAAA,CAACP,GAAG;gBAAC8D,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3B5D,OAAA;gBAAKuD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CxD,OAAA,CAACL,OAAO;kBAAC4D,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5C5D,OAAA;kBAAMuD,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED5D,OAAA;UAAKkE,GAAG,EAAEpD;QAAe;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGN5D,OAAA;QAAKuD,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDxD,OAAA;UAAKuD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BxD,OAAA,CAACT,KAAK;YACJ4E,KAAK,EAAEzD,YAAa;YACpB0D,QAAQ,EAAGrB,CAAC,IAAKpC,eAAe,CAACoC,CAAC,CAACsB,MAAM,CAACF,KAAK,CAAE;YACjDG,UAAU,EAAExB,cAAe;YAC3B3B,WAAW,EAAC,2BAA2B;YACvCoD,QAAQ,EAAE3D,SAAU;YACpB2C,SAAS,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACF5D,OAAA,CAACd,MAAM;YACLsF,OAAO,EAAEvC,iBAAkB;YAC3BsC,QAAQ,EAAE,CAAC7D,YAAY,CAACwB,IAAI,CAAC,CAAC,IAAItB,SAAU;YAC5C6D,IAAI,EAAC,MAAM;YAAAjB,QAAA,EAEV5C,SAAS,gBACRZ,OAAA,CAACL,OAAO;cAAC4D,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE5C5D,OAAA,CAACR,IAAI;cAAC+D,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC5B;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN5D,OAAA;UAAGuD,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5D,OAAA;MAAKuD,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAChExD,OAAA;QAAKuD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCxD,OAAA;UAAIuD,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1E5D,OAAA;UAAGuD,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCxD,OAAA;UAAKuD,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBzC,cAAc,CAACC,KAAK,CAAC6C,GAAG,CAAC,CAACa,IAAI,EAAET,KAAK,kBACpCjE,OAAA,CAACb,IAAI;YAAeoE,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC3CxD,OAAA,CAACX,UAAU;cAACkE,SAAS,EAAC,MAAM;cAAAC,QAAA,eAC1BxD,OAAA;gBAAKuD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CxD,OAAA;kBAAKuD,SAAS,EAAC;gBAAiC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACvD5D,OAAA,CAACV,SAAS;kBAACiE,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAEkB,IAAI,CAACzD;gBAAK;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACb5D,OAAA,CAACZ,WAAW;cAACmE,SAAS,EAAC,MAAM;cAAAC,QAAA,eAC3BxD,OAAA;gBAAKuD,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC3CmB,MAAM,CAACC,OAAO,CAACF,IAAI,CAACxD,MAAM,CAAC,CAAC2D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,GAAG,CAAC,CAAC,CAACb,GAAG,EAAEmB,KAAK,CAAC,kBACxDnE,OAAA;kBAAeuD,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBAC7CxD,OAAA;oBAAMuD,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAER,GAAG,EAAC,GAAC;kBAAA;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1C5D,OAAA;oBAAMuD,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAEsB,MAAM,CAACX,KAAK;kBAAC;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAF9CZ,GAAG;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGR,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA,GAhBLc,IAAI,CAACrE,EAAE;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBZ,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5D,OAAA;QAAKuD,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCxD,OAAA,CAACd,MAAM;UAAC6F,OAAO,EAAC,SAAS;UAACN,IAAI,EAAC,IAAI;UAAClB,SAAS,EAAC,QAAQ;UAAAC,QAAA,gBACpDxD,OAAA,CAACH,GAAG;YAAC0D,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAElC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CA1PID,MAAM;AAAA+E,EAAA,GAAN/E,MAAM;AA4PZ,eAAeA,MAAM;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}