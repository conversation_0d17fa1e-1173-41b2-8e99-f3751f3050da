"""
Embeddings API endpoints for generating and managing vector embeddings.
Handles ChromaDB operations and OpenAI embedding generation.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import logging

from app.core.database import get_db
from app.models.database import Document
from app.models.schemas import (
    EmbeddingGenerationRequest, 
    EmbeddingGenerationResponse,
    ErrorResponse
)
from app.services.vector_service_manager import get_shared_vector_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/embeddings", tags=["embeddings"])
# Get shared vector service instance
vector_service = get_shared_vector_service()
logger.info(f"Using {type(vector_service).__name__} for embeddings")


@router.post("/generate", response_model=EmbeddingGenerationResponse)
async def generate_embeddings(
    request: EmbeddingGenerationRequest,
    db: Session = Depends(get_db)
):
    """
    Generate embeddings for a document and store them in ChromaDB.
    
    Args:
        request: Embedding generation request with document ID and parameters
        db: Database session
        
    Returns:
        Embedding generation response with processing results
        
    Raises:
        HTTPException: If document not found or embedding generation fails
    """
    try:
        # Get document from database
        document = db.query(Document).filter(Document.id == request.document_id).first()
        
        if not document:
            raise HTTPException(
                status_code=404,
                detail="Document not found"
            )
        
        if not document.extracted_text:
            raise HTTPException(
                status_code=400,
                detail="Document has no extracted text to process"
            )
        
        # Generate embeddings
        result = await vector_service.generate_embeddings(
            document_id=document.id,
            text=document.extracted_text,
            chunk_size=request.chunk_size,
            chunk_overlap=request.chunk_overlap
        )
        
        # Update document status
        if result["embeddings_generated"]:
            document.embeddings_generated = True
            db.commit()
        
        logger.info(f"Embeddings generated for document {request.document_id}")
        
        return EmbeddingGenerationResponse(
            document_id=result["document_id"],
            chunks_processed=result["chunks_processed"],
            embeddings_generated=result["embeddings_generated"],
            message=result["message"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate embeddings: {str(e)}"
        )


@router.get("/search")
async def search_embeddings(
    query: str,
    document_id: int = None,
    n_results: int = 5,
    db: Session = Depends(get_db)
):
    """
    Search for relevant text chunks using similarity search.
    
    Args:
        query: Search query text
        document_id: Optional document ID to filter results
        n_results: Number of results to return (max 20)
        db: Database session
        
    Returns:
        List of relevant text chunks with similarity scores
        
    Raises:
        HTTPException: If search fails
    """
    try:
        # Validate parameters
        if not query.strip():
            raise HTTPException(
                status_code=400,
                detail="Query cannot be empty"
            )
        
        if n_results > 20:
            n_results = 20
        
        # If document_id is provided, verify it exists
        if document_id is not None:
            document = db.query(Document).filter(Document.id == document_id).first()
            if not document:
                raise HTTPException(
                    status_code=404,
                    detail="Document not found"
                )
        
        # Search for relevant context
        results = await vector_service.get_relevant_context(
            query=query,
            document_id=document_id,
            n_results=n_results
        )
        
        logger.info(f"Search completed for query: '{query[:50]}...'")
        
        return {
            "query": query,
            "document_id": document_id,
            "results_count": len(results),
            "results": results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error searching embeddings: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to search embeddings: {str(e)}"
        )


@router.delete("/{document_id}")
async def delete_document_embeddings(
    document_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete all embeddings for a specific document.
    
    Args:
        document_id: ID of the document
        db: Database session
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If document not found or deletion fails
    """
    try:
        # Verify document exists
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            raise HTTPException(
                status_code=404,
                detail="Document not found"
            )
        
        # Delete embeddings from Qdrant
        success = vector_service.delete_document_embeddings(document_id)
        
        if success:
            # Update document status
            document.embeddings_generated = False
            db.commit()
            
            logger.info(f"Embeddings deleted for document {document_id}")
            return {"message": "Document embeddings deleted successfully"}
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to delete embeddings"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting embeddings for document {document_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete embeddings: {str(e)}"
        )


@router.get("/stats")
async def get_embeddings_stats():
    """
    Get statistics about the embeddings collection.
    
    Returns:
        Collection statistics and information
    """
    try:
        stats = vector_service.get_collection_stats()
        return stats
    except Exception as e:
        logger.error(f"Error getting embeddings stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get embeddings statistics: {str(e)}"
        )
