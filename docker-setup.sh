#!/bin/bash

# =============================================================================
# GenAI Workflow Builder - Docker Setup Script
# =============================================================================

set -e

echo "🚀 Setting up GenAI Workflow Builder with Docker..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env.docker ]; then
    echo "📝 Creating .env.docker file..."
    cp .env.docker.example .env.docker
    echo "⚠️  Please edit .env.docker and add your OpenAI API key!"
    echo "   OPENAI_API_KEY=your_openai_api_key_here"
fi

# Create uploads directory
mkdir -p uploads
mkdir -p backend/uploads

echo "🐳 Starting Docker services..."

# Start only infrastructure services first
echo "📦 Starting PostgreSQL and Qdrant..."
docker-compose up -d postgres qdrant

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 30

# Check if services are healthy
echo "🔍 Checking service health..."
docker-compose ps

echo "✅ Infrastructure services are running!"
echo ""
echo "📋 Service URLs:"
echo "   🐘 PostgreSQL: localhost:5432"
echo "   🔍 Qdrant: http://localhost:6333"
echo "   🔧 pgAdmin: http://localhost:5050 (<EMAIL> / admin123)"
echo ""
echo "🚀 To start the full application stack:"
echo "   docker-compose --profile full-stack up -d"
echo ""
echo "🛑 To stop all services:"
echo "   docker-compose down"
echo ""
echo "🗑️  To remove all data (CAUTION!):"
echo "   docker-compose down -v"
