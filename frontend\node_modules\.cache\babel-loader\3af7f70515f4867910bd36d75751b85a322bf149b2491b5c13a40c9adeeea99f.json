{"ast": null, "code": "var nextId = 0;\nexport default function local() {\n  return new Local();\n}\nfunction Local() {\n  this._ = \"@\" + (++nextId).toString(36);\n}\nLocal.prototype = local.prototype = {\n  constructor: Local,\n  get: function (node) {\n    var id = this._;\n    while (!(id in node)) if (!(node = node.parentNode)) return;\n    return node[id];\n  },\n  set: function (node, value) {\n    return node[this._] = value;\n  },\n  remove: function (node) {\n    return this._ in node && delete node[this._];\n  },\n  toString: function () {\n    return this._;\n  }\n};", "map": {"version": 3, "names": ["nextId", "local", "Local", "_", "toString", "prototype", "constructor", "get", "node", "id", "parentNode", "set", "value", "remove"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/local.js"], "sourcesContent": ["var nextId = 0;\n\nexport default function local() {\n  return new Local;\n}\n\nfunction Local() {\n  this._ = \"@\" + (++nextId).toString(36);\n}\n\nLocal.prototype = local.prototype = {\n  constructor: Local,\n  get: function(node) {\n    var id = this._;\n    while (!(id in node)) if (!(node = node.parentNode)) return;\n    return node[id];\n  },\n  set: function(node, value) {\n    return node[this._] = value;\n  },\n  remove: function(node) {\n    return this._ in node && delete node[this._];\n  },\n  toString: function() {\n    return this._;\n  }\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAG,CAAC;AAEd,eAAe,SAASC,KAAKA,CAAA,EAAG;EAC9B,OAAO,IAAIC,KAAK,CAAD,CAAC;AAClB;AAEA,SAASA,KAAKA,CAAA,EAAG;EACf,IAAI,CAACC,CAAC,GAAG,GAAG,GAAG,CAAC,EAAEH,MAAM,EAAEI,QAAQ,CAAC,EAAE,CAAC;AACxC;AAEAF,KAAK,CAACG,SAAS,GAAGJ,KAAK,CAACI,SAAS,GAAG;EAClCC,WAAW,EAAEJ,KAAK;EAClBK,GAAG,EAAE,SAAAA,CAASC,IAAI,EAAE;IAClB,IAAIC,EAAE,GAAG,IAAI,CAACN,CAAC;IACf,OAAO,EAAEM,EAAE,IAAID,IAAI,CAAC,EAAE,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACE,UAAU,CAAC,EAAE;IACrD,OAAOF,IAAI,CAACC,EAAE,CAAC;EACjB,CAAC;EACDE,GAAG,EAAE,SAAAA,CAASH,IAAI,EAAEI,KAAK,EAAE;IACzB,OAAOJ,IAAI,CAAC,IAAI,CAACL,CAAC,CAAC,GAAGS,KAAK;EAC7B,CAAC;EACDC,MAAM,EAAE,SAAAA,CAASL,IAAI,EAAE;IACrB,OAAO,IAAI,CAACL,CAAC,IAAIK,IAAI,IAAI,OAAOA,IAAI,CAAC,IAAI,CAACL,CAAC,CAAC;EAC9C,CAAC;EACDC,QAAQ,EAAE,SAAAA,CAAA,EAAW;IACnB,OAAO,IAAI,CAACD,CAAC;EACf;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}