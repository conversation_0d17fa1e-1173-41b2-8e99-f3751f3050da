import React, { memo } from 'react';
import { <PERSON><PERSON>, <PERSON>si<PERSON> } from 'reactflow';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../../ui/card';
import { Database, Settings, FileText } from 'lucide-react';

const KnowledgeBaseNode = ({ data, selected }) => {
  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-1.5 rounded-lg bg-green-500 text-white">
              <Database className="w-4 h-4" />
            </div>
            <CardTitle className="text-sm">Knowledge Base</CardTitle>
          </div>
          <Settings className="w-4 h-4 text-muted-foreground" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">
            Document processing & vector search
          </div>
          
          <div className="bg-muted/50 p-2 rounded text-xs">
            <div className="font-medium">Configuration:</div>
            <div>Max Results: {data.config?.maxResults || 5}</div>
            <div>Documents: {data.config?.documentCount || 0}</div>
            <div className="flex items-center space-x-1 mt-1">
              <FileText className="w-3 h-3" />
              <span>PDF Processing Enabled</span>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="query"
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="context"
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(KnowledgeBaseNode);
