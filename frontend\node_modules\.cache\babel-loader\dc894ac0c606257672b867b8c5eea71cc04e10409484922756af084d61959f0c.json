{"ast": null, "code": "// tpmt is two power minus ten times t scaled to [0,1]\nexport function tpmt(x) {\n  return (Math.pow(2, -10 * x) - 0.0009765625) * 1.0009775171065494;\n}", "map": {"version": 3, "names": ["tpmt", "x", "Math", "pow"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-ease/src/math.js"], "sourcesContent": ["// tpmt is two power minus ten times t scaled to [0,1]\nexport function tpmt(x) {\n  return (Math.pow(2, -10 * x) - 0.0009765625) * 1.0009775171065494;\n}\n"], "mappings": "AAAA;AACA,OAAO,SAASA,IAAIA,CAACC,CAAC,EAAE;EACtB,OAAO,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,GAAGF,CAAC,CAAC,GAAG,YAAY,IAAI,kBAAkB;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}