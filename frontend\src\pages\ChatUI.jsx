import React, { useState, useRef, useEffect } from 'react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import {
  Send,
  Loader2,
  User,
  Bot,
  Settings,
  MessageSquare
} from 'lucide-react';
import axios from 'axios';

const ChatUI = () => {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userId] = useState('user-' + Date.now()); // Simple user ID generation
  const messagesEndRef = useRef(null);

  // Sample workflow configuration (would come from WorkflowBuilder)
  const sampleWorkflow = {
    nodes: [
      {
        id: 'user-query-1',
        type: 'userQuery',
        position: { x: 100, y: 100 },
        data: { label: 'User Query', config: { placeholder: 'Ask me anything...' } }
      },
      {
        id: 'knowledge-base-1',
        type: 'knowledgeBase',
        position: { x: 400, y: 100 },
        data: { label: 'Knowledge Base', config: { documentId: null, maxResults: 5 } }
      },
      {
        id: 'llm-engine-1',
        type: 'llmEngine',
        position: { x: 700, y: 100 },
        data: { 
          label: 'LLM Engine', 
          config: { 
            model: 'gpt-3.5-turbo', 
            temperature: 0.7, 
            maxTokens: 1000,
            enableWebSearch: false
          } 
        }
      },
      {
        id: 'output-1',
        type: 'output',
        position: { x: 1000, y: 100 },
        data: { label: 'Output', config: { format: 'text', showSources: true } }
      }
    ],
    connections: [
      { source: 'user-query-1', target: 'knowledge-base-1' },
      { source: 'user-query-1', target: 'llm-engine-1', targetHandle: 'query' },
      { source: 'knowledge-base-1', target: 'llm-engine-1', targetHandle: 'context' },
      { source: 'llm-engine-1', target: 'output-1' }
    ]
  };

  // Scroll to bottom when new messages are added
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle sending message
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Call workflow execution API
      const response = await axios.post('http://localhost:8000/api/workflow/execute', {
        user_id: userId,
        query: inputMessage,
        workflow: sampleWorkflow
      });

      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: response.data.response,
        timestamp: new Date(),
        executionTime: response.data.execution_time_ms,
      };

      setMessages(prev => [...prev, aiMessage]);

      // Save chat message to backend
      await axios.post('http://localhost:8000/api/chat/save', {
        user_id: userId,
        query: inputMessage,
        response: response.data.response,
        workflow_config: sampleWorkflow
      });

    } catch (error) {
      console.error('Error sending message:', error);
      
      const errorMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date(),
        isError: true,
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Load chat history (placeholder)
  const loadChatHistory = async () => {
    try {
      const response = await axios.get(`http://localhost:8000/api/chat/history/${userId}`);
      const history = response.data.map(msg => ({
        id: msg.id,
        type: 'user',
        content: msg.query,
        timestamp: new Date(msg.timestamp),
      })).concat(response.data.map(msg => ({
        id: msg.id + '_response',
        type: 'ai',
        content: msg.response,
        timestamp: new Date(msg.timestamp),
      })));
      
      setMessages(history);
    } catch (error) {
      console.error('Error loading chat history:', error);
    }
  };

  // Clear chat
  const clearChat = () => {
    setMessages([]);
  };

  return (
    <div className="flex h-full bg-background">
      {/* Chat Sidebar - Hidden on mobile */}
      <div className="hidden lg:flex w-80 xl:w-96 bg-background border-r border-border flex-col shadow-lg">
        <div className="p-4 border-b border-border">
          <h2 className="text-lg font-semibold text-foreground">Chat Interface</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Ask questions and get AI-powered responses
          </p>
        </div>

        <div className="flex-1 p-4 space-y-3">
          <Button 
            onClick={loadChatHistory}
            variant="outline" 
            className="w-full justify-start"
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            Load Chat History
          </Button>
          
          <Button 
            onClick={clearChat}
            variant="outline" 
            className="w-full justify-start"
          >
            <Settings className="w-4 h-4 mr-2" />
            Clear Chat
          </Button>
        </div>

        <div className="p-4 border-t border-border">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Current Workflow</CardTitle>
            </CardHeader>
            <CardContent className="text-xs text-muted-foreground space-y-1">
              <p>• User Query → Knowledge Base</p>
              <p>• Knowledge Base → LLM Engine</p>
              <p>• LLM Engine → Output</p>
              <p className="text-green-600 mt-2">✓ Ready to chat</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Bot className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  Welcome to GenAI Workflow Builder
                </h3>
                <p className="text-sm text-muted-foreground max-w-md">
                  Start a conversation by typing your question below. 
                  The AI will process your query through the configured workflow.
                </p>
              </div>
            </div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] sm:max-w-[70%] rounded-lg p-3 sm:p-4 ${
                    message.type === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : message.isError
                      ? 'bg-destructive text-destructive-foreground'
                      : 'bg-muted text-foreground'
                  }`}
                >
                  <div className="flex items-start space-x-2">
                    <div className="flex-shrink-0">
                      {message.type === 'user' ? (
                        <User className="w-4 h-4 mt-0.5" />
                      ) : (
                        <Bot className="w-4 h-4 mt-0.5" />
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      <div className="flex items-center justify-between mt-2 text-xs opacity-70">
                        <span>
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                        {message.executionTime && (
                          <span>
                            {message.executionTime}ms
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-muted text-foreground rounded-lg p-4 max-w-[70%]">
                <div className="flex items-center space-x-2">
                  <Bot className="w-4 h-4" />
                  <div className="flex items-center space-x-1">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">AI is thinking...</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className="border-t border-border p-3 sm:p-4">
          <div className="flex space-x-2">
            <Input
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message here..."
              disabled={isLoading}
              className="flex-1 text-sm sm:text-base"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isLoading}
              size="icon"
              className="flex-shrink-0"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>

          <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
            <span className="hidden sm:inline">Press Enter to send, Shift+Enter for new line</span>
            <span className="sm:hidden">Enter to send</span>
            <span>{inputMessage.length}/1000</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatUI;
