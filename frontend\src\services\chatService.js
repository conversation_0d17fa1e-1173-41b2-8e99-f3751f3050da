import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

class ChatService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  async sendMessage(message, workflow) {
    try {
      // Simulate workflow execution with the backend
      const response = await this.api.post('/api/workflow/execute', {
        message,
        workflow,
        user_id: 'demo-user'
      });

      return {
        content: response.data.response || 'I received your message and processed it through the workflow.',
        sources: response.data.sources || [],
        executionTime: response.data.execution_time || 0
      };
    } catch (error) {
      console.error('Chat service error:', error);
      
      // Fallback response for demo purposes
      return {
        content: this.generateFallbackResponse(message),
        sources: ['Demo Knowledge Base'],
        executionTime: 1.2
      };
    }
  }

  generateFallbackResponse(message) {
    const responses = [
      `I understand you're asking about "${message}". Based on the workflow configuration, I would process this through the knowledge base and generate a comprehensive response.`,
      `Thank you for your question about "${message}". The AI workflow would typically search relevant documents and provide contextual information.`,
      `Your query "${message}" has been processed through the configured workflow. In a full implementation, this would involve document retrieval and AI-powered response generation.`,
      `I've received your message: "${message}". The workflow would normally execute knowledge base search, LLM processing, and formatted output generation.`
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
  }

  async saveMessage(message, userId = 'demo-user') {
    try {
      await this.api.post('/api/chat/save', {
        user_id: userId,
        message: message.content,
        message_type: message.type,
        timestamp: message.timestamp
      });
    } catch (error) {
      console.error('Error saving message:', error);
    }
  }

  async getChatHistory(userId = 'demo-user') {
    try {
      const response = await this.api.get(`/api/chat/history/${userId}`);
      return response.data.messages || [];
    } catch (error) {
      console.error('Error fetching chat history:', error);
      return [];
    }
  }
}

export const chatService = new ChatService();
