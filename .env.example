# GenAI Workflow Builder Environment Configuration

# =============================================================================
# REQUIRED CONFIGURATION
# =============================================================================

# OpenAI API Configuration (REQUIRED)
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database URL
# Format: postgresql://username:password@host:port/database_name
DATABASE_URL=postgresql://postgres:password@localhost:5432/genai_workflow

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Secret key for JWT tokens and encryption
# Generate a secure key: openssl rand -hex 32
SECRET_KEY=your-secret-key-change-in-production

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# ChromaDB vector database storage directory
CHROMA_PERSIST_DIRECTORY=./chroma_db

# File upload storage directory
UPLOAD_DIRECTORY=./uploads

# Maximum file size in bytes (default: 10MB)
MAX_FILE_SIZE=10485760

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Enable debug mode (true/false)
DEBUG=false

# Application environment (development/production)
ENVIRONMENT=development

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# Backend API URL for frontend
REACT_APP_API_URL=http://localhost:8000/api

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Redis URL for caching (optional)
REDIS_URL=redis://localhost:6379

# CORS allowed origins (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000

# Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Docker-specific overrides (used in docker-compose)
POSTGRES_DB=genai_workflow
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# =============================================================================
# INSTRUCTIONS
# =============================================================================

# 1. Copy this file to .env: cp .env.example .env
# 2. Replace 'your_openai_api_key_here' with your actual OpenAI API key
# 3. Update SECRET_KEY with a secure random string
# 4. Modify other values as needed for your environment
# 5. Never commit the .env file to version control
