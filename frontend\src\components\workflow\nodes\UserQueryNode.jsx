import React, { memo } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { MessageCircle, Settings } from 'lucide-react';

const UserQueryNode = ({ data, selected }) => {
  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-1.5 rounded-lg bg-blue-500 text-white">
              <MessageCircle className="w-4 h-4" />
            </div>
            <CardTitle className="text-sm">User Query</CardTitle>
          </div>
          <Settings className="w-4 h-4 text-muted-foreground" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">
            Entry point for user questions
          </div>
          
          <div className="bg-muted/50 p-2 rounded text-xs">
            <div className="font-medium">Configuration:</div>
            <div>Placeholder: {data.config?.placeholder || 'Enter your question...'}</div>
          </div>
        </div>
      </CardContent>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(UserQueryNode);
