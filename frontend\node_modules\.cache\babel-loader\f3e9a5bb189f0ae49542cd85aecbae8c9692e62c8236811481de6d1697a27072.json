{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\nclass ChatService {\n  constructor() {\n    this.api = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n  async sendMessage(message, workflow) {\n    try {\n      // Simulate workflow execution with the backend\n      const response = await this.api.post('/api/workflow/execute', {\n        message,\n        workflow,\n        user_id: 'demo-user'\n      });\n      return {\n        content: response.data.response || 'I received your message and processed it through the workflow.',\n        sources: response.data.sources || [],\n        executionTime: response.data.execution_time || 0\n      };\n    } catch (error) {\n      console.error('Chat service error:', error);\n\n      // Fallback response for demo purposes\n      return {\n        content: this.generateFallbackResponse(message),\n        sources: ['Demo Knowledge Base'],\n        executionTime: 1.2\n      };\n    }\n  }\n  generateFallbackResponse(message) {\n    const responses = [`I understand you're asking about \"${message}\". Based on the workflow configuration, I would process this through the knowledge base and generate a comprehensive response.`, `Thank you for your question about \"${message}\". The AI workflow would typically search relevant documents and provide contextual information.`, `Your query \"${message}\" has been processed through the configured workflow. In a full implementation, this would involve document retrieval and AI-powered response generation.`, `I've received your message: \"${message}\". The workflow would normally execute knowledge base search, LLM processing, and formatted output generation.`];\n    return responses[Math.floor(Math.random() * responses.length)];\n  }\n  async saveMessage(message, userId = 'demo-user') {\n    try {\n      await this.api.post('/api/chat/save', {\n        user_id: userId,\n        message: message.content,\n        message_type: message.type,\n        timestamp: message.timestamp\n      });\n    } catch (error) {\n      console.error('Error saving message:', error);\n    }\n  }\n  async getChatHistory(userId = 'demo-user') {\n    try {\n      const response = await this.api.get(`/api/chat/history/${userId}`);\n      return response.data.messages || [];\n    } catch (error) {\n      console.error('Error fetching chat history:', error);\n      return [];\n    }\n  }\n}\nexport const chatService = new ChatService();", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "ChatService", "constructor", "api", "create", "baseURL", "timeout", "headers", "sendMessage", "message", "workflow", "response", "post", "user_id", "content", "data", "sources", "executionTime", "execution_time", "error", "console", "generateFallbackResponse", "responses", "Math", "floor", "random", "length", "saveMessage", "userId", "message_type", "type", "timestamp", "getChatHistory", "get", "messages", "chatService"], "sources": ["D:/assignment for AI planet/aiplanet/frontend/src/services/chatService.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';\n\nclass ChatService {\n  constructor() {\n    this.api = axios.create({\n      baseURL: API_BASE_URL,\n      timeout: 30000,\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n  }\n\n  async sendMessage(message, workflow) {\n    try {\n      // Simulate workflow execution with the backend\n      const response = await this.api.post('/api/workflow/execute', {\n        message,\n        workflow,\n        user_id: 'demo-user'\n      });\n\n      return {\n        content: response.data.response || 'I received your message and processed it through the workflow.',\n        sources: response.data.sources || [],\n        executionTime: response.data.execution_time || 0\n      };\n    } catch (error) {\n      console.error('Chat service error:', error);\n      \n      // Fallback response for demo purposes\n      return {\n        content: this.generateFallbackResponse(message),\n        sources: ['Demo Knowledge Base'],\n        executionTime: 1.2\n      };\n    }\n  }\n\n  generateFallbackResponse(message) {\n    const responses = [\n      `I understand you're asking about \"${message}\". Based on the workflow configuration, I would process this through the knowledge base and generate a comprehensive response.`,\n      `Thank you for your question about \"${message}\". The AI workflow would typically search relevant documents and provide contextual information.`,\n      `Your query \"${message}\" has been processed through the configured workflow. In a full implementation, this would involve document retrieval and AI-powered response generation.`,\n      `I've received your message: \"${message}\". The workflow would normally execute knowledge base search, LLM processing, and formatted output generation.`\n    ];\n    \n    return responses[Math.floor(Math.random() * responses.length)];\n  }\n\n  async saveMessage(message, userId = 'demo-user') {\n    try {\n      await this.api.post('/api/chat/save', {\n        user_id: userId,\n        message: message.content,\n        message_type: message.type,\n        timestamp: message.timestamp\n      });\n    } catch (error) {\n      console.error('Error saving message:', error);\n    }\n  }\n\n  async getChatHistory(userId = 'demo-user') {\n    try {\n      const response = await this.api.get(`/api/chat/history/${userId}`);\n      return response.data.messages || [];\n    } catch (error) {\n      console.error('Error fetching chat history:', error);\n      return [];\n    }\n  }\n}\n\nexport const chatService = new ChatService();\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AAE7E,MAAMC,WAAW,CAAC;EAChBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;MACtBC,OAAO,EAAER,YAAY;MACrBS,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;EAEA,MAAMC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IACnC,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACR,GAAG,CAACS,IAAI,CAAC,uBAAuB,EAAE;QAC5DH,OAAO;QACPC,QAAQ;QACRG,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,OAAO;QACLC,OAAO,EAAEH,QAAQ,CAACI,IAAI,CAACJ,QAAQ,IAAI,gEAAgE;QACnGK,OAAO,EAAEL,QAAQ,CAACI,IAAI,CAACC,OAAO,IAAI,EAAE;QACpCC,aAAa,EAAEN,QAAQ,CAACI,IAAI,CAACG,cAAc,IAAI;MACjD,CAAC;IACH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACA,OAAO;QACLL,OAAO,EAAE,IAAI,CAACO,wBAAwB,CAACZ,OAAO,CAAC;QAC/CO,OAAO,EAAE,CAAC,qBAAqB,CAAC;QAChCC,aAAa,EAAE;MACjB,CAAC;IACH;EACF;EAEAI,wBAAwBA,CAACZ,OAAO,EAAE;IAChC,MAAMa,SAAS,GAAG,CAChB,qCAAqCb,OAAO,gIAAgI,EAC5K,sCAAsCA,OAAO,kGAAkG,EAC/I,eAAeA,OAAO,2JAA2J,EACjL,gCAAgCA,OAAO,gHAAgH,CACxJ;IAED,OAAOa,SAAS,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,SAAS,CAACI,MAAM,CAAC,CAAC;EAChE;EAEA,MAAMC,WAAWA,CAAClB,OAAO,EAAEmB,MAAM,GAAG,WAAW,EAAE;IAC/C,IAAI;MACF,MAAM,IAAI,CAACzB,GAAG,CAACS,IAAI,CAAC,gBAAgB,EAAE;QACpCC,OAAO,EAAEe,MAAM;QACfnB,OAAO,EAAEA,OAAO,CAACK,OAAO;QACxBe,YAAY,EAAEpB,OAAO,CAACqB,IAAI;QAC1BC,SAAS,EAAEtB,OAAO,CAACsB;MACrB,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF;EAEA,MAAMa,cAAcA,CAACJ,MAAM,GAAG,WAAW,EAAE;IACzC,IAAI;MACF,MAAMjB,QAAQ,GAAG,MAAM,IAAI,CAACR,GAAG,CAAC8B,GAAG,CAAC,qBAAqBL,MAAM,EAAE,CAAC;MAClE,OAAOjB,QAAQ,CAACI,IAAI,CAACmB,QAAQ,IAAI,EAAE;IACrC,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,EAAE;IACX;EACF;AACF;AAEA,OAAO,MAAMgB,WAAW,GAAG,IAAIlC,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}