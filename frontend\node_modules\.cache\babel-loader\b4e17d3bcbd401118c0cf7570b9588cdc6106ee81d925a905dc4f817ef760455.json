{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PowerSquare = createLucideIcon(\"PowerSquare\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}], [\"path\", {\n  d: \"M12 7v5\",\n  key: \"ma6bk\"\n}], [\"path\", {\n  d: \"M8 9a5.14 5.14 0 0 0 4 8 4.95 4.95 0 0 0 4-8\",\n  key: \"15eubv\"\n}]]);\nexport { PowerSquare as default };", "map": {"version": 3, "names": ["PowerSquare", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\power-square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PowerSquare\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0xMiA3djUiIC8+CiAgPHBhdGggZD0iTTggOWE1LjE0IDUuMTQgMCAwIDAgNCA4IDQuOTUgNC45NSAwIDAgMCA0LTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/power-square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PowerSquare = createLucideIcon('PowerSquare', [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n  ['path', { d: 'M12 7v5', key: 'ma6bk' }],\n  ['path', { d: 'M8 9a5.14 5.14 0 0 0 4 8 4.95 4.95 0 0 0 4-8', key: '15eubv' }],\n]);\n\nexport default PowerSquare;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAQ;EAAMC,CAAG;EAAKC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAS,GACvC,CAAC,MAAQ;EAAEC,CAAA,EAAG,8CAAgD;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}