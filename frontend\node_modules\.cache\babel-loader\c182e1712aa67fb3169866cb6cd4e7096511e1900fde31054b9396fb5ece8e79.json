{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FileBox = createLucideIcon(\"FileBox\", [[\"path\", {\n  d: \"M14.5 22H18a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4\",\n  key: \"h7jej2\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"path\", {\n  d: \"M2.97 13.12c-.6.36-.97 1.02-.97 1.74v3.28c0 .72.37 1.38.97 1.74l3 1.83c.63.39 1.43.39 2.06 0l3-1.83c.6-.36.97-1.02.97-1.74v-3.28c0-.72-.37-1.38-.97-1.74l-3-1.83a1.97 1.97 0 0 0-2.06 0l-3 1.83Z\",\n  key: \"f4a3oc\"\n}], [\"path\", {\n  d: \"m7 17-4.74-2.85\",\n  key: \"etm6su\"\n}], [\"path\", {\n  d: \"m7 17 4.74-2.85\",\n  key: \"5xuooz\"\n}], [\"path\", {\n  d: \"M7 17v5\",\n  key: \"1yj1jh\"\n}]]);\nexport { FileBox as default };", "map": {"version": 3, "names": ["FileBox", "createLucideIcon", "d", "key", "points"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\file-box.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileBox\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNSAyMkgxOGEyIDIgMCAwIDAgMi0yVjcuNUwxNC41IDJINmEyIDIgMCAwIDAtMiAydjQiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPHBhdGggZD0iTTIuOTcgMTMuMTJjLS42LjM2LS45NyAxLjAyLS45NyAxLjc0djMuMjhjMCAuNzIuMzcgMS4zOC45NyAxLjc0bDMgMS44M2MuNjMuMzkgMS40My4zOSAyLjA2IDBsMy0xLjgzYy42LS4zNi45Ny0xLjAyLjk3LTEuNzR2LTMuMjhjMC0uNzItLjM3LTEuMzgtLjk3LTEuNzRsLTMtMS44M2ExLjk3IDEuOTcgMCAwIDAtMi4wNiAwbC0zIDEuODNaIiAvPgogIDxwYXRoIGQ9Im03IDE3LTQuNzQtMi44NSIgLz4KICA8cGF0aCBkPSJtNyAxNyA0Ljc0LTIuODUiIC8+CiAgPHBhdGggZD0iTTcgMTd2NSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/file-box\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileBox = createLucideIcon('FileBox', [\n  ['path', { d: 'M14.5 22H18a2 2 0 0 0 2-2V7.5L14.5 2H6a2 2 0 0 0-2 2v4', key: 'h7jej2' }],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  [\n    'path',\n    {\n      d: 'M2.97 13.12c-.6.36-.97 1.02-.97 1.74v3.28c0 .72.37 1.38.97 1.74l3 1.83c.63.39 1.43.39 2.06 0l3-1.83c.6-.36.97-1.02.97-1.74v-3.28c0-.72-.37-1.38-.97-1.74l-3-1.83a1.97 1.97 0 0 0-2.06 0l-3 1.83Z',\n      key: 'f4a3oc',\n    },\n  ],\n  ['path', { d: 'm7 17-4.74-2.85', key: 'etm6su' }],\n  ['path', { d: 'm7 17 4.74-2.85', key: '5xuooz' }],\n  ['path', { d: 'M7 17v5', key: '1yj1jh' }],\n]);\n\nexport default FileBox;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,wDAA0D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}