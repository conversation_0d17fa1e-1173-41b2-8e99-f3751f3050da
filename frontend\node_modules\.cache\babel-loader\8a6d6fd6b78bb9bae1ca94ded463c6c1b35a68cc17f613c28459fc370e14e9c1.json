{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst SeparatorHorizontal = createLucideIcon(\"SeparatorHorizontal\", [[\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"10d38w\"\n}], [\"polyline\", {\n  points: \"8 8 12 4 16 8\",\n  key: \"zo8t4w\"\n}], [\"polyline\", {\n  points: \"16 16 12 20 8 16\",\n  key: \"1oyrid\"\n}]]);\nexport { SeparatorHorizontal as default };", "map": {"version": 3, "names": ["SeparatorHorizontal", "createLucideIcon", "x1", "x2", "y1", "y2", "key", "points"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\separator-horizontal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name SeparatorHorizontal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMyIgeDI9IjIxIiB5MT0iMTIiIHkyPSIxMiIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSI4IDggMTIgNCAxNiA4IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjE2IDE2IDEyIDIwIDggMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/separator-horizontal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SeparatorHorizontal = createLucideIcon('SeparatorHorizontal', [\n  ['line', { x1: '3', x2: '21', y1: '12', y2: '12', key: '10d38w' }],\n  ['polyline', { points: '8 8 12 4 16 8', key: 'zo8t4w' }],\n  ['polyline', { points: '16 16 12 20 8 16', key: '1oyrid' }],\n]);\n\nexport default SeparatorHorizontal;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,mBAAA,GAAsBC,gBAAA,CAAiB,qBAAuB,GAClE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,UAAY;EAAEC,MAAA,EAAQ,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,UAAY;EAAEC,MAAA,EAAQ,kBAAoB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}