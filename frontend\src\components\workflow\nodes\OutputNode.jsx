import React, { memo } from 'react';
import { <PERSON><PERSON>, <PERSON>si<PERSON> } from 'reactflow';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../../ui/card';
import { Monitor, Settings, MessageSquare, FileText } from 'lucide-react';

const OutputNode = ({ data, selected }) => {
  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-1.5 rounded-lg bg-red-500 text-white">
              <Monitor className="w-4 h-4" />
            </div>
            <CardTitle className="text-sm">Output</CardTitle>
          </div>
          <Settings className="w-4 h-4 text-muted-foreground" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">
            Display final AI response
          </div>
          
          <div className="bg-muted/50 p-2 rounded text-xs">
            <div className="font-medium">Configuration:</div>
            <div>Format: {data.config?.format || 'text'}</div>
            <div>Show Sources: {data.config?.showSources ? 'Yes' : 'No'}</div>
            
            <div className="flex items-center space-x-1 mt-1">
              <MessageSquare className="w-3 h-3" />
              <span>Chat Interface</span>
            </div>
            
            {data.config?.showSources && (
              <div className="flex items-center space-x-1 mt-1">
                <FileText className="w-3 h-3" />
                <span>Source Citations</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="response"
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(OutputNode);
