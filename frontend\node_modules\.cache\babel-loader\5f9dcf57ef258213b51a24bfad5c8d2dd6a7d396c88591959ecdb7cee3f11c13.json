{"ast": null, "code": "'use strict';\n\nimport utils from '../utils.js';\nimport AxiosHeaders from \"./AxiosHeaders.js\";\nconst headersToObject = thing => thing instanceof AxiosHeaders ? thing.toJSON() : thing;\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */\nexport default function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  const config = {};\n  function getMergedValue(target, source, caseless) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge.call({\n        caseless\n      }, target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDeepProperties(a, b, caseless) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(a, b, caseless);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a, caseless);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function valueFromConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function defaultToConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDirectKeys(a, b, prop) {\n    if (prop in config2) {\n      return getMergedValue(a, b);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, a);\n    }\n  }\n  const mergeMap = {\n    url: valueFromConfig2,\n    method: valueFromConfig2,\n    data: valueFromConfig2,\n    baseURL: defaultToConfig2,\n    transformRequest: defaultToConfig2,\n    transformResponse: defaultToConfig2,\n    paramsSerializer: defaultToConfig2,\n    timeout: defaultToConfig2,\n    timeoutMessage: defaultToConfig2,\n    withCredentials: defaultToConfig2,\n    withXSRFToken: defaultToConfig2,\n    adapter: defaultToConfig2,\n    responseType: defaultToConfig2,\n    xsrfCookieName: defaultToConfig2,\n    xsrfHeaderName: defaultToConfig2,\n    onUploadProgress: defaultToConfig2,\n    onDownloadProgress: defaultToConfig2,\n    decompress: defaultToConfig2,\n    maxContentLength: defaultToConfig2,\n    maxBodyLength: defaultToConfig2,\n    beforeRedirect: defaultToConfig2,\n    transport: defaultToConfig2,\n    httpAgent: defaultToConfig2,\n    httpsAgent: defaultToConfig2,\n    cancelToken: defaultToConfig2,\n    socketPath: defaultToConfig2,\n    responseEncoding: defaultToConfig2,\n    validateStatus: mergeDirectKeys,\n    headers: (a, b) => mergeDeepProperties(headersToObject(a), headersToObject(b), true)\n  };\n  utils.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {\n    const merge = mergeMap[prop] || mergeDeepProperties;\n    const configValue = merge(config1[prop], config2[prop], prop);\n    utils.isUndefined(configValue) && merge !== mergeDirectKeys || (config[prop] = configValue);\n  });\n  return config;\n}", "map": {"version": 3, "names": ["utils", "AxiosHeaders", "headersToObject", "thing", "toJSON", "mergeConfig", "config1", "config2", "config", "getMergedValue", "target", "source", "caseless", "isPlainObject", "merge", "call", "isArray", "slice", "mergeDeepProperties", "a", "b", "isUndefined", "undefined", "valueFromConfig2", "defaultToConfig2", "mergeDirectKeys", "prop", "mergeMap", "url", "method", "data", "baseURL", "transformRequest", "transformResponse", "paramsSerializer", "timeout", "timeoutMessage", "withCredentials", "withXSRFToken", "adapter", "responseType", "xsrfCookieName", "xsrfHeaderName", "onUploadProgress", "onDownloadProgress", "decompress", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "beforeRedirect", "transport", "httpAgent", "httpsAgent", "cancelToken", "socketPath", "responseEncoding", "validateStatus", "headers", "for<PERSON>ach", "Object", "keys", "assign", "computeConfigValue", "config<PERSON><PERSON><PERSON>"], "sources": ["D:/assignment for AI planet/aiplanet/frontend/node_modules/axios/lib/core/mergeConfig.js"], "sourcesContent": ["'use strict';\n\nimport utils from '../utils.js';\nimport AxiosHeaders from \"./AxiosHeaders.js\";\n\nconst headersToObject = (thing) => thing instanceof AxiosHeaders ? thing.toJSON() : thing;\n\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */\nexport default function mergeConfig(config1, config2) {\n  // eslint-disable-next-line no-param-reassign\n  config2 = config2 || {};\n  const config = {};\n\n  function getMergedValue(target, source, caseless) {\n    if (utils.isPlainObject(target) && utils.isPlainObject(source)) {\n      return utils.merge.call({caseless}, target, source);\n    } else if (utils.isPlainObject(source)) {\n      return utils.merge({}, source);\n    } else if (utils.isArray(source)) {\n      return source.slice();\n    }\n    return source;\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDeepProperties(a, b, caseless) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(a, b, caseless);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a, caseless);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function valueFromConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function defaultToConfig2(a, b) {\n    if (!utils.isUndefined(b)) {\n      return getMergedValue(undefined, b);\n    } else if (!utils.isUndefined(a)) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  // eslint-disable-next-line consistent-return\n  function mergeDirectKeys(a, b, prop) {\n    if (prop in config2) {\n      return getMergedValue(a, b);\n    } else if (prop in config1) {\n      return getMergedValue(undefined, a);\n    }\n  }\n\n  const mergeMap = {\n    url: valueFromConfig2,\n    method: valueFromConfig2,\n    data: valueFromConfig2,\n    baseURL: defaultToConfig2,\n    transformRequest: defaultToConfig2,\n    transformResponse: defaultToConfig2,\n    paramsSerializer: defaultToConfig2,\n    timeout: defaultToConfig2,\n    timeoutMessage: defaultToConfig2,\n    withCredentials: defaultToConfig2,\n    withXSRFToken: defaultToConfig2,\n    adapter: defaultToConfig2,\n    responseType: defaultToConfig2,\n    xsrfCookieName: defaultToConfig2,\n    xsrfHeaderName: defaultToConfig2,\n    onUploadProgress: defaultToConfig2,\n    onDownloadProgress: defaultToConfig2,\n    decompress: defaultToConfig2,\n    maxContentLength: defaultToConfig2,\n    maxBodyLength: defaultToConfig2,\n    beforeRedirect: defaultToConfig2,\n    transport: defaultToConfig2,\n    httpAgent: defaultToConfig2,\n    httpsAgent: defaultToConfig2,\n    cancelToken: defaultToConfig2,\n    socketPath: defaultToConfig2,\n    responseEncoding: defaultToConfig2,\n    validateStatus: mergeDirectKeys,\n    headers: (a, b) => mergeDeepProperties(headersToObject(a), headersToObject(b), true)\n  };\n\n  utils.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {\n    const merge = mergeMap[prop] || mergeDeepProperties;\n    const configValue = merge(config1[prop], config2[prop], prop);\n    (utils.isUndefined(configValue) && merge !== mergeDirectKeys) || (config[prop] = configValue);\n  });\n\n  return config;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,YAAY,MAAM,mBAAmB;AAE5C,MAAMC,eAAe,GAAIC,KAAK,IAAKA,KAAK,YAAYF,YAAY,GAAGE,KAAK,CAACC,MAAM,CAAC,CAAC,GAAGD,KAAK;;AAEzF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASE,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACpD;EACAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EACvB,MAAMC,MAAM,GAAG,CAAC,CAAC;EAEjB,SAASC,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAE;IAChD,IAAIZ,KAAK,CAACa,aAAa,CAACH,MAAM,CAAC,IAAIV,KAAK,CAACa,aAAa,CAACF,MAAM,CAAC,EAAE;MAC9D,OAAOX,KAAK,CAACc,KAAK,CAACC,IAAI,CAAC;QAACH;MAAQ,CAAC,EAAEF,MAAM,EAAEC,MAAM,CAAC;IACrD,CAAC,MAAM,IAAIX,KAAK,CAACa,aAAa,CAACF,MAAM,CAAC,EAAE;MACtC,OAAOX,KAAK,CAACc,KAAK,CAAC,CAAC,CAAC,EAAEH,MAAM,CAAC;IAChC,CAAC,MAAM,IAAIX,KAAK,CAACgB,OAAO,CAACL,MAAM,CAAC,EAAE;MAChC,OAAOA,MAAM,CAACM,KAAK,CAAC,CAAC;IACvB;IACA,OAAON,MAAM;EACf;;EAEA;EACA,SAASO,mBAAmBA,CAACC,CAAC,EAAEC,CAAC,EAAER,QAAQ,EAAE;IAC3C,IAAI,CAACZ,KAAK,CAACqB,WAAW,CAACD,CAAC,CAAC,EAAE;MACzB,OAAOX,cAAc,CAACU,CAAC,EAAEC,CAAC,EAAER,QAAQ,CAAC;IACvC,CAAC,MAAM,IAAI,CAACZ,KAAK,CAACqB,WAAW,CAACF,CAAC,CAAC,EAAE;MAChC,OAAOV,cAAc,CAACa,SAAS,EAAEH,CAAC,EAAEP,QAAQ,CAAC;IAC/C;EACF;;EAEA;EACA,SAASW,gBAAgBA,CAACJ,CAAC,EAAEC,CAAC,EAAE;IAC9B,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACD,CAAC,CAAC,EAAE;MACzB,OAAOX,cAAc,CAACa,SAAS,EAAEF,CAAC,CAAC;IACrC;EACF;;EAEA;EACA,SAASI,gBAAgBA,CAACL,CAAC,EAAEC,CAAC,EAAE;IAC9B,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACD,CAAC,CAAC,EAAE;MACzB,OAAOX,cAAc,CAACa,SAAS,EAAEF,CAAC,CAAC;IACrC,CAAC,MAAM,IAAI,CAACpB,KAAK,CAACqB,WAAW,CAACF,CAAC,CAAC,EAAE;MAChC,OAAOV,cAAc,CAACa,SAAS,EAAEH,CAAC,CAAC;IACrC;EACF;;EAEA;EACA,SAASM,eAAeA,CAACN,CAAC,EAAEC,CAAC,EAAEM,IAAI,EAAE;IACnC,IAAIA,IAAI,IAAInB,OAAO,EAAE;MACnB,OAAOE,cAAc,CAACU,CAAC,EAAEC,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIM,IAAI,IAAIpB,OAAO,EAAE;MAC1B,OAAOG,cAAc,CAACa,SAAS,EAAEH,CAAC,CAAC;IACrC;EACF;EAEA,MAAMQ,QAAQ,GAAG;IACfC,GAAG,EAAEL,gBAAgB;IACrBM,MAAM,EAAEN,gBAAgB;IACxBO,IAAI,EAAEP,gBAAgB;IACtBQ,OAAO,EAAEP,gBAAgB;IACzBQ,gBAAgB,EAAER,gBAAgB;IAClCS,iBAAiB,EAAET,gBAAgB;IACnCU,gBAAgB,EAAEV,gBAAgB;IAClCW,OAAO,EAAEX,gBAAgB;IACzBY,cAAc,EAAEZ,gBAAgB;IAChCa,eAAe,EAAEb,gBAAgB;IACjCc,aAAa,EAAEd,gBAAgB;IAC/Be,OAAO,EAAEf,gBAAgB;IACzBgB,YAAY,EAAEhB,gBAAgB;IAC9BiB,cAAc,EAAEjB,gBAAgB;IAChCkB,cAAc,EAAElB,gBAAgB;IAChCmB,gBAAgB,EAAEnB,gBAAgB;IAClCoB,kBAAkB,EAAEpB,gBAAgB;IACpCqB,UAAU,EAAErB,gBAAgB;IAC5BsB,gBAAgB,EAAEtB,gBAAgB;IAClCuB,aAAa,EAAEvB,gBAAgB;IAC/BwB,cAAc,EAAExB,gBAAgB;IAChCyB,SAAS,EAAEzB,gBAAgB;IAC3B0B,SAAS,EAAE1B,gBAAgB;IAC3B2B,UAAU,EAAE3B,gBAAgB;IAC5B4B,WAAW,EAAE5B,gBAAgB;IAC7B6B,UAAU,EAAE7B,gBAAgB;IAC5B8B,gBAAgB,EAAE9B,gBAAgB;IAClC+B,cAAc,EAAE9B,eAAe;IAC/B+B,OAAO,EAAEA,CAACrC,CAAC,EAAEC,CAAC,KAAKF,mBAAmB,CAAChB,eAAe,CAACiB,CAAC,CAAC,EAAEjB,eAAe,CAACkB,CAAC,CAAC,EAAE,IAAI;EACrF,CAAC;EAEDpB,KAAK,CAACyD,OAAO,CAACC,MAAM,CAACC,IAAI,CAACD,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEtD,OAAO,EAAEC,OAAO,CAAC,CAAC,EAAE,SAASsD,kBAAkBA,CAACnC,IAAI,EAAE;IAChG,MAAMZ,KAAK,GAAGa,QAAQ,CAACD,IAAI,CAAC,IAAIR,mBAAmB;IACnD,MAAM4C,WAAW,GAAGhD,KAAK,CAACR,OAAO,CAACoB,IAAI,CAAC,EAAEnB,OAAO,CAACmB,IAAI,CAAC,EAAEA,IAAI,CAAC;IAC5D1B,KAAK,CAACqB,WAAW,CAACyC,WAAW,CAAC,IAAIhD,KAAK,KAAKW,eAAe,KAAMjB,MAAM,CAACkB,IAAI,CAAC,GAAGoC,WAAW,CAAC;EAC/F,CAAC,CAAC;EAEF,OAAOtD,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}