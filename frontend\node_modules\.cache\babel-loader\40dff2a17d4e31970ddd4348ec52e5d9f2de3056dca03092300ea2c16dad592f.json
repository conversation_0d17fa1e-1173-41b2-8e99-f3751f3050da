{"ast": null, "code": "import { selection } from \"d3-selection\";\nimport selection_interrupt from \"./interrupt.js\";\nimport selection_transition from \"./transition.js\";\nselection.prototype.interrupt = selection_interrupt;\nselection.prototype.transition = selection_transition;", "map": {"version": 3, "names": ["selection", "selection_interrupt", "selection_transition", "prototype", "interrupt", "transition"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-transition/src/selection/index.js"], "sourcesContent": ["import {selection} from \"d3-selection\";\nimport selection_interrupt from \"./interrupt.js\";\nimport selection_transition from \"./transition.js\";\n\nselection.prototype.interrupt = selection_interrupt;\nselection.prototype.transition = selection_transition;\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,cAAc;AACtC,OAAOC,mBAAmB,MAAM,gBAAgB;AAChD,OAAOC,oBAAoB,MAAM,iBAAiB;AAElDF,SAAS,CAACG,SAAS,CAACC,SAAS,GAAGH,mBAAmB;AACnDD,SAAS,CAACG,SAAS,CAACE,UAAU,GAAGH,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}