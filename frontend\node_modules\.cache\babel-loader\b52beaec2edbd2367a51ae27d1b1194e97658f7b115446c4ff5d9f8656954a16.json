{"ast": null, "code": "import { Selection } from \"./index.js\";\nimport { EnterNode } from \"./enter.js\";\nimport constant from \"../constant.js\";\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0,\n    node,\n    groupLength = group.length,\n    dataLength = data.length;\n\n  // Put any non-null nodes that fit into update.\n  // Put any null nodes into enter.\n  // Put any remaining data into enter.\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Put any non-null nodes that don’t fit into exit.\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i,\n    node,\n    nodeByKeyValue = new Map(),\n    groupLength = group.length,\n    dataLength = data.length,\n    keyValues = new Array(groupLength),\n    keyValue;\n\n  // Compute the key for each node.\n  // If multiple nodes have the same key, the duplicates are added to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n\n  // Compute the key for each datum.\n  // If there a node associated with this key, join and add it to update.\n  // If there is not (or the key is a duplicate), add it to enter.\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Add any remaining nodes that were not bound to data to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && nodeByKeyValue.get(keyValues[i]) === node) {\n      exit[i] = node;\n    }\n  }\n}\nfunction datum(node) {\n  return node.__data__;\n}\nexport default function (value, key) {\n  if (!arguments.length) return Array.from(this, datum);\n  var bind = key ? bindKey : bindIndex,\n    parents = this._parents,\n    groups = this._groups;\n  if (typeof value !== \"function\") value = constant(value);\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j],\n      group = groups[j],\n      groupLength = group.length,\n      data = arraylike(value.call(parent, parent && parent.__data__, j, parents)),\n      dataLength = data.length,\n      enterGroup = enter[j] = new Array(dataLength),\n      updateGroup = update[j] = new Array(dataLength),\n      exitGroup = exit[j] = new Array(groupLength);\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n\n    // Now connect the enter nodes to their following update node, such that\n    // appendChild can insert the materialized enter node before this node,\n    // rather than at the end of the parent node.\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength);\n        previous._next = next || null;\n      }\n    }\n  }\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\n\n// Given some data, this returns an array-like view of it: an object that\n// exposes a length property and allows numeric indexing. Note that unlike\n// selectAll, this isn’t worried about “live” collections because the resulting\n// array will only be used briefly while data is being bound. (It is possible to\n// cause the data to change while iterating by using a key function, but please\n// don’t; we’d rather avoid a gratuitous copy.)\nfunction arraylike(data) {\n  return typeof data === \"object\" && \"length\" in data ? data // Array, TypedArray, NodeList, array-like\n  : Array.from(data); // Map, Set, iterable, string, or anything else\n}", "map": {"version": 3, "names": ["Selection", "EnterNode", "constant", "bindIndex", "parent", "group", "enter", "update", "exit", "data", "i", "node", "groupLength", "length", "dataLength", "__data__", "<PERSON><PERSON><PERSON>", "key", "nodeByKeyValue", "Map", "keyV<PERSON><PERSON>", "Array", "keyValue", "call", "has", "set", "get", "delete", "datum", "value", "arguments", "from", "bind", "parents", "_parents", "groups", "_groups", "m", "j", "arraylike", "enterGroup", "updateGroup", "exitGroup", "i0", "i1", "previous", "next", "_next", "_enter", "_exit"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/selection/data.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\nimport {EnterNode} from \"./enter.js\";\nimport constant from \"../constant.js\";\n\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0,\n      node,\n      groupLength = group.length,\n      dataLength = data.length;\n\n  // Put any non-null nodes that fit into update.\n  // Put any null nodes into enter.\n  // Put any remaining data into enter.\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Put any non-null nodes that don’t fit into exit.\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i,\n      node,\n      nodeByKeyValue = new Map,\n      groupLength = group.length,\n      dataLength = data.length,\n      keyValues = new Array(groupLength),\n      keyValue;\n\n  // Compute the key for each node.\n  // If multiple nodes have the same key, the duplicates are added to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n\n  // Compute the key for each datum.\n  // If there a node associated with this key, join and add it to update.\n  // If there is not (or the key is a duplicate), add it to enter.\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n\n  // Add any remaining nodes that were not bound to data to exit.\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && (nodeByKeyValue.get(keyValues[i]) === node)) {\n      exit[i] = node;\n    }\n  }\n}\n\nfunction datum(node) {\n  return node.__data__;\n}\n\nexport default function(value, key) {\n  if (!arguments.length) return Array.from(this, datum);\n\n  var bind = key ? bindKey : bindIndex,\n      parents = this._parents,\n      groups = this._groups;\n\n  if (typeof value !== \"function\") value = constant(value);\n\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j],\n        group = groups[j],\n        groupLength = group.length,\n        data = arraylike(value.call(parent, parent && parent.__data__, j, parents)),\n        dataLength = data.length,\n        enterGroup = enter[j] = new Array(dataLength),\n        updateGroup = update[j] = new Array(dataLength),\n        exitGroup = exit[j] = new Array(groupLength);\n\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n\n    // Now connect the enter nodes to their following update node, such that\n    // appendChild can insert the materialized enter node before this node,\n    // rather than at the end of the parent node.\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1) i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength);\n        previous._next = next || null;\n      }\n    }\n  }\n\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\n\n// Given some data, this returns an array-like view of it: an object that\n// exposes a length property and allows numeric indexing. Note that unlike\n// selectAll, this isn’t worried about “live” collections because the resulting\n// array will only be used briefly while data is being bound. (It is possible to\n// cause the data to change while iterating by using a key function, but please\n// don’t; we’d rather avoid a gratuitous copy.)\nfunction arraylike(data) {\n  return typeof data === \"object\" && \"length\" in data\n    ? data // Array, TypedArray, NodeList, array-like\n    : Array.from(data); // Map, Set, iterable, string, or anything else\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,YAAY;AACpC,SAAQC,SAAS,QAAO,YAAY;AACpC,OAAOC,QAAQ,MAAM,gBAAgB;AAErC,SAASC,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC3D,IAAIC,CAAC,GAAG,CAAC;IACLC,IAAI;IACJC,WAAW,GAAGP,KAAK,CAACQ,MAAM;IAC1BC,UAAU,GAAGL,IAAI,CAACI,MAAM;;EAE5B;EACA;EACA;EACA,OAAOH,CAAC,GAAGI,UAAU,EAAE,EAAEJ,CAAC,EAAE;IAC1B,IAAIC,IAAI,GAAGN,KAAK,CAACK,CAAC,CAAC,EAAE;MACnBC,IAAI,CAACI,QAAQ,GAAGN,IAAI,CAACC,CAAC,CAAC;MACvBH,MAAM,CAACG,CAAC,CAAC,GAAGC,IAAI;IAClB,CAAC,MAAM;MACLL,KAAK,CAACI,CAAC,CAAC,GAAG,IAAIT,SAAS,CAACG,MAAM,EAAEK,IAAI,CAACC,CAAC,CAAC,CAAC;IAC3C;EACF;;EAEA;EACA,OAAOA,CAAC,GAAGE,WAAW,EAAE,EAAEF,CAAC,EAAE;IAC3B,IAAIC,IAAI,GAAGN,KAAK,CAACK,CAAC,CAAC,EAAE;MACnBF,IAAI,CAACE,CAAC,CAAC,GAAGC,IAAI;IAChB;EACF;AACF;AAEA,SAASK,OAAOA,CAACZ,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEQ,GAAG,EAAE;EAC9D,IAAIP,CAAC;IACDC,IAAI;IACJO,cAAc,GAAG,IAAIC,GAAG,CAAD,CAAC;IACxBP,WAAW,GAAGP,KAAK,CAACQ,MAAM;IAC1BC,UAAU,GAAGL,IAAI,CAACI,MAAM;IACxBO,SAAS,GAAG,IAAIC,KAAK,CAACT,WAAW,CAAC;IAClCU,QAAQ;;EAEZ;EACA;EACA,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,WAAW,EAAE,EAAEF,CAAC,EAAE;IAChC,IAAIC,IAAI,GAAGN,KAAK,CAACK,CAAC,CAAC,EAAE;MACnBU,SAAS,CAACV,CAAC,CAAC,GAAGY,QAAQ,GAAGL,GAAG,CAACM,IAAI,CAACZ,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAEL,CAAC,EAAEL,KAAK,CAAC,GAAG,EAAE;MACtE,IAAIa,cAAc,CAACM,GAAG,CAACF,QAAQ,CAAC,EAAE;QAChCd,IAAI,CAACE,CAAC,CAAC,GAAGC,IAAI;MAChB,CAAC,MAAM;QACLO,cAAc,CAACO,GAAG,CAACH,QAAQ,EAAEX,IAAI,CAAC;MACpC;IACF;EACF;;EAEA;EACA;EACA;EACA,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,UAAU,EAAE,EAAEJ,CAAC,EAAE;IAC/BY,QAAQ,GAAGL,GAAG,CAACM,IAAI,CAACnB,MAAM,EAAEK,IAAI,CAACC,CAAC,CAAC,EAAEA,CAAC,EAAED,IAAI,CAAC,GAAG,EAAE;IAClD,IAAIE,IAAI,GAAGO,cAAc,CAACQ,GAAG,CAACJ,QAAQ,CAAC,EAAE;MACvCf,MAAM,CAACG,CAAC,CAAC,GAAGC,IAAI;MAChBA,IAAI,CAACI,QAAQ,GAAGN,IAAI,CAACC,CAAC,CAAC;MACvBQ,cAAc,CAACS,MAAM,CAACL,QAAQ,CAAC;IACjC,CAAC,MAAM;MACLhB,KAAK,CAACI,CAAC,CAAC,GAAG,IAAIT,SAAS,CAACG,MAAM,EAAEK,IAAI,CAACC,CAAC,CAAC,CAAC;IAC3C;EACF;;EAEA;EACA,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,WAAW,EAAE,EAAEF,CAAC,EAAE;IAChC,IAAI,CAACC,IAAI,GAAGN,KAAK,CAACK,CAAC,CAAC,KAAMQ,cAAc,CAACQ,GAAG,CAACN,SAAS,CAACV,CAAC,CAAC,CAAC,KAAKC,IAAK,EAAE;MACpEH,IAAI,CAACE,CAAC,CAAC,GAAGC,IAAI;IAChB;EACF;AACF;AAEA,SAASiB,KAAKA,CAACjB,IAAI,EAAE;EACnB,OAAOA,IAAI,CAACI,QAAQ;AACtB;AAEA,eAAe,UAASc,KAAK,EAAEZ,GAAG,EAAE;EAClC,IAAI,CAACa,SAAS,CAACjB,MAAM,EAAE,OAAOQ,KAAK,CAACU,IAAI,CAAC,IAAI,EAAEH,KAAK,CAAC;EAErD,IAAII,IAAI,GAAGf,GAAG,GAAGD,OAAO,GAAGb,SAAS;IAChC8B,OAAO,GAAG,IAAI,CAACC,QAAQ;IACvBC,MAAM,GAAG,IAAI,CAACC,OAAO;EAEzB,IAAI,OAAOP,KAAK,KAAK,UAAU,EAAEA,KAAK,GAAG3B,QAAQ,CAAC2B,KAAK,CAAC;EAExD,KAAK,IAAIQ,CAAC,GAAGF,MAAM,CAACtB,MAAM,EAAEN,MAAM,GAAG,IAAIc,KAAK,CAACgB,CAAC,CAAC,EAAE/B,KAAK,GAAG,IAAIe,KAAK,CAACgB,CAAC,CAAC,EAAE7B,IAAI,GAAG,IAAIa,KAAK,CAACgB,CAAC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAE,EAAEC,CAAC,EAAE;IAC/G,IAAIlC,MAAM,GAAG6B,OAAO,CAACK,CAAC,CAAC;MACnBjC,KAAK,GAAG8B,MAAM,CAACG,CAAC,CAAC;MACjB1B,WAAW,GAAGP,KAAK,CAACQ,MAAM;MAC1BJ,IAAI,GAAG8B,SAAS,CAACV,KAAK,CAACN,IAAI,CAACnB,MAAM,EAAEA,MAAM,IAAIA,MAAM,CAACW,QAAQ,EAAEuB,CAAC,EAAEL,OAAO,CAAC,CAAC;MAC3EnB,UAAU,GAAGL,IAAI,CAACI,MAAM;MACxB2B,UAAU,GAAGlC,KAAK,CAACgC,CAAC,CAAC,GAAG,IAAIjB,KAAK,CAACP,UAAU,CAAC;MAC7C2B,WAAW,GAAGlC,MAAM,CAAC+B,CAAC,CAAC,GAAG,IAAIjB,KAAK,CAACP,UAAU,CAAC;MAC/C4B,SAAS,GAAGlC,IAAI,CAAC8B,CAAC,CAAC,GAAG,IAAIjB,KAAK,CAACT,WAAW,CAAC;IAEhDoB,IAAI,CAAC5B,MAAM,EAAEC,KAAK,EAAEmC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEjC,IAAI,EAAEQ,GAAG,CAAC;;IAElE;IACA;IACA;IACA,KAAK,IAAI0B,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEC,QAAQ,EAAEC,IAAI,EAAEH,EAAE,GAAG7B,UAAU,EAAE,EAAE6B,EAAE,EAAE;MAC9D,IAAIE,QAAQ,GAAGL,UAAU,CAACG,EAAE,CAAC,EAAE;QAC7B,IAAIA,EAAE,IAAIC,EAAE,EAAEA,EAAE,GAAGD,EAAE,GAAG,CAAC;QACzB,OAAO,EAAEG,IAAI,GAAGL,WAAW,CAACG,EAAE,CAAC,CAAC,IAAI,EAAEA,EAAE,GAAG9B,UAAU,CAAC;QACtD+B,QAAQ,CAACE,KAAK,GAAGD,IAAI,IAAI,IAAI;MAC/B;IACF;EACF;EAEAvC,MAAM,GAAG,IAAIP,SAAS,CAACO,MAAM,EAAE0B,OAAO,CAAC;EACvC1B,MAAM,CAACyC,MAAM,GAAG1C,KAAK;EACrBC,MAAM,CAAC0C,KAAK,GAAGzC,IAAI;EACnB,OAAOD,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgC,SAASA,CAAC9B,IAAI,EAAE;EACvB,OAAO,OAAOA,IAAI,KAAK,QAAQ,IAAI,QAAQ,IAAIA,IAAI,GAC/CA,IAAI,CAAC;EAAA,EACLY,KAAK,CAACU,IAAI,CAACtB,IAAI,CAAC,CAAC,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}