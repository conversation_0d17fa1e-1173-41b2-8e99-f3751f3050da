import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import SimpleWorkflowBuilder from './pages/SimpleWorkflowBuilder';
import ChatUI from './pages/ChatUI';
import Navigation from './components/layout/Navigation';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-background text-foreground">
        <Navigation />
        <main className="h-screen pt-16 overflow-hidden">
          <Routes>
            <Route path="/" element={<Navigate to="/workflow" replace />} />
            <Route path="/workflow" element={<SimpleWorkflowBuilder />} />
            <Route path="/chat" element={<ChatUI />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
