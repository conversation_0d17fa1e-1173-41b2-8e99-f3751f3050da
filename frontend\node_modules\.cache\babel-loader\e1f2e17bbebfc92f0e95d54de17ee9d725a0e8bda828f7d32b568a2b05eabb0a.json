{"ast": null, "code": "import { selection } from \"d3-selection\";\nimport transition_attr from \"./attr.js\";\nimport transition_attrTween from \"./attrTween.js\";\nimport transition_delay from \"./delay.js\";\nimport transition_duration from \"./duration.js\";\nimport transition_ease from \"./ease.js\";\nimport transition_easeVarying from \"./easeVarying.js\";\nimport transition_filter from \"./filter.js\";\nimport transition_merge from \"./merge.js\";\nimport transition_on from \"./on.js\";\nimport transition_remove from \"./remove.js\";\nimport transition_select from \"./select.js\";\nimport transition_selectAll from \"./selectAll.js\";\nimport transition_selection from \"./selection.js\";\nimport transition_style from \"./style.js\";\nimport transition_styleTween from \"./styleTween.js\";\nimport transition_text from \"./text.js\";\nimport transition_textTween from \"./textTween.js\";\nimport transition_transition from \"./transition.js\";\nimport transition_tween from \"./tween.js\";\nimport transition_end from \"./end.js\";\nvar id = 0;\nexport function Transition(groups, parents, name, id) {\n  this._groups = groups;\n  this._parents = parents;\n  this._name = name;\n  this._id = id;\n}\nexport default function transition(name) {\n  return selection().transition(name);\n}\nexport function newId() {\n  return ++id;\n}\nvar selection_prototype = selection.prototype;\nTransition.prototype = transition.prototype = {\n  constructor: Transition,\n  select: transition_select,\n  selectAll: transition_selectAll,\n  selectChild: selection_prototype.selectChild,\n  selectChildren: selection_prototype.selectChildren,\n  filter: transition_filter,\n  merge: transition_merge,\n  selection: transition_selection,\n  transition: transition_transition,\n  call: selection_prototype.call,\n  nodes: selection_prototype.nodes,\n  node: selection_prototype.node,\n  size: selection_prototype.size,\n  empty: selection_prototype.empty,\n  each: selection_prototype.each,\n  on: transition_on,\n  attr: transition_attr,\n  attrTween: transition_attrTween,\n  style: transition_style,\n  styleTween: transition_styleTween,\n  text: transition_text,\n  textTween: transition_textTween,\n  remove: transition_remove,\n  tween: transition_tween,\n  delay: transition_delay,\n  duration: transition_duration,\n  ease: transition_ease,\n  easeVarying: transition_easeVarying,\n  end: transition_end,\n  [Symbol.iterator]: selection_prototype[Symbol.iterator]\n};", "map": {"version": 3, "names": ["selection", "transition_attr", "transition_attrTween", "transition_delay", "transition_duration", "transition_ease", "transition_easeVarying", "transition_filter", "transition_merge", "transition_on", "transition_remove", "transition_select", "transition_selectAll", "transition_selection", "transition_style", "transition_styleTween", "transition_text", "transition_textTween", "transition_transition", "transition_tween", "transition_end", "id", "Transition", "groups", "parents", "name", "_groups", "_parents", "_name", "_id", "transition", "newId", "selection_prototype", "prototype", "constructor", "select", "selectAll", "<PERSON><PERSON><PERSON><PERSON>", "select<PERSON><PERSON><PERSON><PERSON>", "filter", "merge", "call", "nodes", "node", "size", "empty", "each", "on", "attr", "attrTween", "style", "styleTween", "text", "textTween", "remove", "tween", "delay", "duration", "ease", "easeVarying", "end", "Symbol", "iterator"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-transition/src/transition/index.js"], "sourcesContent": ["import {selection} from \"d3-selection\";\nimport transition_attr from \"./attr.js\";\nimport transition_attrTween from \"./attrTween.js\";\nimport transition_delay from \"./delay.js\";\nimport transition_duration from \"./duration.js\";\nimport transition_ease from \"./ease.js\";\nimport transition_easeVarying from \"./easeVarying.js\";\nimport transition_filter from \"./filter.js\";\nimport transition_merge from \"./merge.js\";\nimport transition_on from \"./on.js\";\nimport transition_remove from \"./remove.js\";\nimport transition_select from \"./select.js\";\nimport transition_selectAll from \"./selectAll.js\";\nimport transition_selection from \"./selection.js\";\nimport transition_style from \"./style.js\";\nimport transition_styleTween from \"./styleTween.js\";\nimport transition_text from \"./text.js\";\nimport transition_textTween from \"./textTween.js\";\nimport transition_transition from \"./transition.js\";\nimport transition_tween from \"./tween.js\";\nimport transition_end from \"./end.js\";\n\nvar id = 0;\n\nexport function Transition(groups, parents, name, id) {\n  this._groups = groups;\n  this._parents = parents;\n  this._name = name;\n  this._id = id;\n}\n\nexport default function transition(name) {\n  return selection().transition(name);\n}\n\nexport function newId() {\n  return ++id;\n}\n\nvar selection_prototype = selection.prototype;\n\nTransition.prototype = transition.prototype = {\n  constructor: Transition,\n  select: transition_select,\n  selectAll: transition_selectAll,\n  selectChild: selection_prototype.selectChild,\n  selectChildren: selection_prototype.selectChildren,\n  filter: transition_filter,\n  merge: transition_merge,\n  selection: transition_selection,\n  transition: transition_transition,\n  call: selection_prototype.call,\n  nodes: selection_prototype.nodes,\n  node: selection_prototype.node,\n  size: selection_prototype.size,\n  empty: selection_prototype.empty,\n  each: selection_prototype.each,\n  on: transition_on,\n  attr: transition_attr,\n  attrTween: transition_attrTween,\n  style: transition_style,\n  styleTween: transition_styleTween,\n  text: transition_text,\n  textTween: transition_textTween,\n  remove: transition_remove,\n  tween: transition_tween,\n  delay: transition_delay,\n  duration: transition_duration,\n  ease: transition_ease,\n  easeVarying: transition_easeVarying,\n  end: transition_end,\n  [Symbol.iterator]: selection_prototype[Symbol.iterator]\n};\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,cAAc;AACtC,OAAOC,eAAe,MAAM,WAAW;AACvC,OAAOC,oBAAoB,MAAM,gBAAgB;AACjD,OAAOC,gBAAgB,MAAM,YAAY;AACzC,OAAOC,mBAAmB,MAAM,eAAe;AAC/C,OAAOC,eAAe,MAAM,WAAW;AACvC,OAAOC,sBAAsB,MAAM,kBAAkB;AACrD,OAAOC,iBAAiB,MAAM,aAAa;AAC3C,OAAOC,gBAAgB,MAAM,YAAY;AACzC,OAAOC,aAAa,MAAM,SAAS;AACnC,OAAOC,iBAAiB,MAAM,aAAa;AAC3C,OAAOC,iBAAiB,MAAM,aAAa;AAC3C,OAAOC,oBAAoB,MAAM,gBAAgB;AACjD,OAAOC,oBAAoB,MAAM,gBAAgB;AACjD,OAAOC,gBAAgB,MAAM,YAAY;AACzC,OAAOC,qBAAqB,MAAM,iBAAiB;AACnD,OAAOC,eAAe,MAAM,WAAW;AACvC,OAAOC,oBAAoB,MAAM,gBAAgB;AACjD,OAAOC,qBAAqB,MAAM,iBAAiB;AACnD,OAAOC,gBAAgB,MAAM,YAAY;AACzC,OAAOC,cAAc,MAAM,UAAU;AAErC,IAAIC,EAAE,GAAG,CAAC;AAEV,OAAO,SAASC,UAAUA,CAACC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEJ,EAAE,EAAE;EACpD,IAAI,CAACK,OAAO,GAAGH,MAAM;EACrB,IAAI,CAACI,QAAQ,GAAGH,OAAO;EACvB,IAAI,CAACI,KAAK,GAAGH,IAAI;EACjB,IAAI,CAACI,GAAG,GAAGR,EAAE;AACf;AAEA,eAAe,SAASS,UAAUA,CAACL,IAAI,EAAE;EACvC,OAAOzB,SAAS,CAAC,CAAC,CAAC8B,UAAU,CAACL,IAAI,CAAC;AACrC;AAEA,OAAO,SAASM,KAAKA,CAAA,EAAG;EACtB,OAAO,EAAEV,EAAE;AACb;AAEA,IAAIW,mBAAmB,GAAGhC,SAAS,CAACiC,SAAS;AAE7CX,UAAU,CAACW,SAAS,GAAGH,UAAU,CAACG,SAAS,GAAG;EAC5CC,WAAW,EAAEZ,UAAU;EACvBa,MAAM,EAAExB,iBAAiB;EACzByB,SAAS,EAAExB,oBAAoB;EAC/ByB,WAAW,EAAEL,mBAAmB,CAACK,WAAW;EAC5CC,cAAc,EAAEN,mBAAmB,CAACM,cAAc;EAClDC,MAAM,EAAEhC,iBAAiB;EACzBiC,KAAK,EAAEhC,gBAAgB;EACvBR,SAAS,EAAEa,oBAAoB;EAC/BiB,UAAU,EAAEZ,qBAAqB;EACjCuB,IAAI,EAAET,mBAAmB,CAACS,IAAI;EAC9BC,KAAK,EAAEV,mBAAmB,CAACU,KAAK;EAChCC,IAAI,EAAEX,mBAAmB,CAACW,IAAI;EAC9BC,IAAI,EAAEZ,mBAAmB,CAACY,IAAI;EAC9BC,KAAK,EAAEb,mBAAmB,CAACa,KAAK;EAChCC,IAAI,EAAEd,mBAAmB,CAACc,IAAI;EAC9BC,EAAE,EAAEtC,aAAa;EACjBuC,IAAI,EAAE/C,eAAe;EACrBgD,SAAS,EAAE/C,oBAAoB;EAC/BgD,KAAK,EAAEpC,gBAAgB;EACvBqC,UAAU,EAAEpC,qBAAqB;EACjCqC,IAAI,EAAEpC,eAAe;EACrBqC,SAAS,EAAEpC,oBAAoB;EAC/BqC,MAAM,EAAE5C,iBAAiB;EACzB6C,KAAK,EAAEpC,gBAAgB;EACvBqC,KAAK,EAAErD,gBAAgB;EACvBsD,QAAQ,EAAErD,mBAAmB;EAC7BsD,IAAI,EAAErD,eAAe;EACrBsD,WAAW,EAAErD,sBAAsB;EACnCsD,GAAG,EAAExC,cAAc;EACnB,CAACyC,MAAM,CAACC,QAAQ,GAAG9B,mBAAmB,CAAC6B,MAAM,CAACC,QAAQ;AACxD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}