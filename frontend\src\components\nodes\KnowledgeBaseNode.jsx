import React from 'react';
import { Handle, Position } from 'reactflow';
import { Card, CardContent } from '../ui/card';
import { Database } from 'lucide-react';

const KnowledgeBaseNode = ({ data, selected }) => {
  return (
    <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-500 text-white rounded-lg">
            <Database className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-foreground text-sm">
              {data.label}
            </h3>
            <p className="text-xs text-muted-foreground mt-1">
              Search documents for context
            </p>
          </div>
        </div>
        
        <div className="mt-3 space-y-2">
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Document ID:</span>
            <span className="text-foreground">
              {data.config?.documentId || 'All documents'}
            </span>
          </div>
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Max Results:</span>
            <span className="text-foreground">
              {data.config?.maxResults || 5}
            </span>
          </div>
        </div>
      </CardContent>
      
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
      
      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
    </Card>
  );
};

export default KnowledgeBaseNode;
