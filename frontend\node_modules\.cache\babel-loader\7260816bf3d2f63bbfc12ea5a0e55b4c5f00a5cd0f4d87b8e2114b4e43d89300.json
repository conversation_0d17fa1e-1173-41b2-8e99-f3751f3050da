{"ast": null, "code": "import { set } from \"./schedule.js\";\nexport default function () {\n  var on0,\n    on1,\n    that = this,\n    id = that._id,\n    size = that.size();\n  return new Promise(function (resolve, reject) {\n    var cancel = {\n        value: reject\n      },\n      end = {\n        value: function () {\n          if (--size === 0) resolve();\n        }\n      };\n    that.each(function () {\n      var schedule = set(this, id),\n        on = schedule.on;\n\n      // If this node shared a dispatch with the previous node,\n      // just assign the updated shared dispatch and we’re done!\n      // Otherwise, copy-on-write.\n      if (on !== on0) {\n        on1 = (on0 = on).copy();\n        on1._.cancel.push(cancel);\n        on1._.interrupt.push(cancel);\n        on1._.end.push(end);\n      }\n      schedule.on = on1;\n    });\n\n    // The selection was empty, resolve end immediately\n    if (size === 0) resolve();\n  });\n}", "map": {"version": 3, "names": ["set", "on0", "on1", "that", "id", "_id", "size", "Promise", "resolve", "reject", "cancel", "value", "end", "each", "schedule", "on", "copy", "_", "push", "interrupt"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-transition/src/transition/end.js"], "sourcesContent": ["import {set} from \"./schedule.js\";\n\nexport default function() {\n  var on0, on1, that = this, id = that._id, size = that.size();\n  return new Promise(function(resolve, reject) {\n    var cancel = {value: reject},\n        end = {value: function() { if (--size === 0) resolve(); }};\n\n    that.each(function() {\n      var schedule = set(this, id),\n          on = schedule.on;\n\n      // If this node shared a dispatch with the previous node,\n      // just assign the updated shared dispatch and we’re done!\n      // Otherwise, copy-on-write.\n      if (on !== on0) {\n        on1 = (on0 = on).copy();\n        on1._.cancel.push(cancel);\n        on1._.interrupt.push(cancel);\n        on1._.end.push(end);\n      }\n\n      schedule.on = on1;\n    });\n\n    // The selection was empty, resolve end immediately\n    if (size === 0) resolve();\n  });\n}\n"], "mappings": "AAAA,SAAQA,GAAG,QAAO,eAAe;AAEjC,eAAe,YAAW;EACxB,IAAIC,GAAG;IAAEC,GAAG;IAAEC,IAAI,GAAG,IAAI;IAAEC,EAAE,GAAGD,IAAI,CAACE,GAAG;IAAEC,IAAI,GAAGH,IAAI,CAACG,IAAI,CAAC,CAAC;EAC5D,OAAO,IAAIC,OAAO,CAAC,UAASC,OAAO,EAAEC,MAAM,EAAE;IAC3C,IAAIC,MAAM,GAAG;QAACC,KAAK,EAAEF;MAAM,CAAC;MACxBG,GAAG,GAAG;QAACD,KAAK,EAAE,SAAAA,CAAA,EAAW;UAAE,IAAI,EAAEL,IAAI,KAAK,CAAC,EAAEE,OAAO,CAAC,CAAC;QAAE;MAAC,CAAC;IAE9DL,IAAI,CAACU,IAAI,CAAC,YAAW;MACnB,IAAIC,QAAQ,GAAGd,GAAG,CAAC,IAAI,EAAEI,EAAE,CAAC;QACxBW,EAAE,GAAGD,QAAQ,CAACC,EAAE;;MAEpB;MACA;MACA;MACA,IAAIA,EAAE,KAAKd,GAAG,EAAE;QACdC,GAAG,GAAG,CAACD,GAAG,GAAGc,EAAE,EAAEC,IAAI,CAAC,CAAC;QACvBd,GAAG,CAACe,CAAC,CAACP,MAAM,CAACQ,IAAI,CAACR,MAAM,CAAC;QACzBR,GAAG,CAACe,CAAC,CAACE,SAAS,CAACD,IAAI,CAACR,MAAM,CAAC;QAC5BR,GAAG,CAACe,CAAC,CAACL,GAAG,CAACM,IAAI,CAACN,GAAG,CAAC;MACrB;MAEAE,QAAQ,CAACC,EAAE,GAAGb,GAAG;IACnB,CAAC,CAAC;;IAEF;IACA,IAAII,IAAI,KAAK,CAAC,EAAEE,OAAO,CAAC,CAAC;EAC3B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}