{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-tabs": "^1.1.12", "autoprefixer": "10.4.16", "axios": "1.6.2", "class-variance-authority": "^0.7.0", "clsx": "2.0.0", "lucide-react": "0.294.0", "postcss": "8.4.32", "react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "6.8.1", "react-scripts": "5.0.1", "reactflow": "^11.11.4", "tailwind-merge": "2.2.0", "tailwindcss": "3.3.6", "tailwindcss-animate": "1.0.7", "web-vitals": "2.1.4", "zustand": "^5.0.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}