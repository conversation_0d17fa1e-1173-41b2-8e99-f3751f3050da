-- PostgreSQL initialization script for GenAI Workflow Builder
-- This script runs automatically when the PostgreSQL container starts

-- Create the database (if not exists)
SELECT 'CREATE DATABASE genai_workflow'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'genai_workflow')\gexec

-- Connect to the database
\c genai_workflow;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Set timezone
SET timezone = 'UTC';

-- Create a comment for the database
COMMENT ON DATABASE genai_workflow IS 'GenAI Workflow Builder Application Database';

-- Log successful initialization
SELECT 'PostgreSQL database initialized successfully for GenAI Workflow Builder' as status;
