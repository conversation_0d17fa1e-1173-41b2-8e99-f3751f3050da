{"ast": null, "code": "import namespaces from \"./namespaces.js\";\nexport default function (name) {\n  var prefix = name += \"\",\n    i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? {\n    space: namespaces[prefix],\n    local: name\n  } : name; // eslint-disable-line no-prototype-builtins\n}", "map": {"version": 3, "names": ["namespaces", "name", "prefix", "i", "indexOf", "slice", "hasOwnProperty", "space", "local"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/namespace.js"], "sourcesContent": ["import namespaces from \"./namespaces.js\";\n\nexport default function(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\") name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? {space: namespaces[prefix], local: name} : name; // eslint-disable-line no-prototype-builtins\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,iBAAiB;AAExC,eAAe,UAASC,IAAI,EAAE;EAC5B,IAAIC,MAAM,GAAGD,IAAI,IAAI,EAAE;IAAEE,CAAC,GAAGD,MAAM,CAACE,OAAO,CAAC,GAAG,CAAC;EAChD,IAAID,CAAC,IAAI,CAAC,IAAI,CAACD,MAAM,GAAGD,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEF,CAAC,CAAC,MAAM,OAAO,EAAEF,IAAI,GAAGA,IAAI,CAACI,KAAK,CAACF,CAAC,GAAG,CAAC,CAAC;EAC/E,OAAOH,UAAU,CAACM,cAAc,CAACJ,MAAM,CAAC,GAAG;IAACK,KAAK,EAAEP,UAAU,CAACE,MAAM,CAAC;IAAEM,KAAK,EAAEP;EAAI,CAAC,GAAGA,IAAI,CAAC,CAAC;AAC9F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}