version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: genai_postgres
    environment:
      POSTGRES_DB: genai_workflow
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - genai_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    container_name: genai_backend
    environment:
      DATABASE_URL: ********************************************/genai_workflow
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      CHROMA_PERSIST_DIRECTORY: /app/chroma_db
      UPLOAD_DIRECTORY: /app/uploads
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-in-production}
      DEBUG: ${DEBUG:-false}
    ports:
      - "8000:8000"
    volumes:
      - backend_uploads:/app/uploads
      - backend_chroma:/app/chroma_db
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - genai_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Frontend (React App)
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    container_name: genai_frontend
    environment:
      REACT_APP_API_URL: http://localhost:8000/api
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - genai_network
    restart: unless-stopped

  # Redis (for caching - optional)
  redis:
    image: redis:7-alpine
    container_name: genai_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - genai_network
    command: redis-server --appendonly yes
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  backend_uploads:
    driver: local
  backend_chroma:
    driver: local
  redis_data:
    driver: local

networks:
  genai_network:
    driver: bridge
