{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Text = createLucideIcon(\"Text\", [[\"path\", {\n  d: \"M17 6.1H3\",\n  key: \"wptmhv\"\n}], [\"path\", {\n  d: \"M21 12.1H3\",\n  key: \"1j38uz\"\n}], [\"path\", {\n  d: \"M15.1 18H3\",\n  key: \"1nb16a\"\n}]]);\nexport { Text as default };", "map": {"version": 3, "names": ["Text", "createLucideIcon", "d", "key"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Text\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgNi4xSDMiIC8+CiAgPHBhdGggZD0iTTIxIDEyLjFIMyIgLz4KICA8cGF0aCBkPSJNMTUuMSAxOEgzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Text = createLucideIcon('Text', [\n  ['path', { d: 'M17 6.1H3', key: 'wptmhv' }],\n  ['path', { d: 'M21 12.1H3', key: '1j38uz' }],\n  ['path', { d: 'M15.1 18H3', key: '1nb16a' }],\n]);\n\nexport default Text;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}