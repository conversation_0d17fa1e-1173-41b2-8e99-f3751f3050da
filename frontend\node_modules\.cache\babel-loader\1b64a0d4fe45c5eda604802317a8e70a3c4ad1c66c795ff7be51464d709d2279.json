{"ast": null, "code": "import { selection } from \"d3-selection\";\nvar Selection = selection.prototype.constructor;\nexport default function () {\n  return new Selection(this._groups, this._parents);\n}", "map": {"version": 3, "names": ["selection", "Selection", "prototype", "constructor", "_groups", "_parents"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-transition/src/transition/selection.js"], "sourcesContent": ["import {selection} from \"d3-selection\";\n\nvar Selection = selection.prototype.constructor;\n\nexport default function() {\n  return new Selection(this._groups, this._parents);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,cAAc;AAEtC,IAAIC,SAAS,GAAGD,SAAS,CAACE,SAAS,CAACC,WAAW;AAE/C,eAAe,YAAW;EACxB,OAAO,IAAIF,SAAS,CAAC,IAAI,CAACG,OAAO,EAAE,IAAI,CAACC,QAAQ,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}