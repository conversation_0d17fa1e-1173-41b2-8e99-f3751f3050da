{"ast": null, "code": "var b1 = 4 / 11,\n  b2 = 6 / 11,\n  b3 = 8 / 11,\n  b4 = 3 / 4,\n  b5 = 9 / 11,\n  b6 = 10 / 11,\n  b7 = 15 / 16,\n  b8 = 21 / 22,\n  b9 = 63 / 64,\n  b0 = 1 / b1 / b1;\nexport function bounceIn(t) {\n  return 1 - bounceOut(1 - t);\n}\nexport function bounceOut(t) {\n  return (t = +t) < b1 ? b0 * t * t : t < b3 ? b0 * (t -= b2) * t + b4 : t < b6 ? b0 * (t -= b5) * t + b7 : b0 * (t -= b8) * t + b9;\n}\nexport function bounceInOut(t) {\n  return ((t *= 2) <= 1 ? 1 - bounceOut(1 - t) : bounceOut(t - 1) + 1) / 2;\n}", "map": {"version": 3, "names": ["b1", "b2", "b3", "b4", "b5", "b6", "b7", "b8", "b9", "b0", "bounceIn", "t", "bounceOut", "bounceInOut"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-ease/src/bounce.js"], "sourcesContent": ["var b1 = 4 / 11,\n    b2 = 6 / 11,\n    b3 = 8 / 11,\n    b4 = 3 / 4,\n    b5 = 9 / 11,\n    b6 = 10 / 11,\n    b7 = 15 / 16,\n    b8 = 21 / 22,\n    b9 = 63 / 64,\n    b0 = 1 / b1 / b1;\n\nexport function bounceIn(t) {\n  return 1 - bounceOut(1 - t);\n}\n\nexport function bounceOut(t) {\n  return (t = +t) < b1 ? b0 * t * t : t < b3 ? b0 * (t -= b2) * t + b4 : t < b6 ? b0 * (t -= b5) * t + b7 : b0 * (t -= b8) * t + b9;\n}\n\nexport function bounceInOut(t) {\n  return ((t *= 2) <= 1 ? 1 - bounceOut(1 - t) : bounceOut(t - 1) + 1) / 2;\n}\n"], "mappings": "AAAA,IAAIA,EAAE,GAAG,CAAC,GAAG,EAAE;EACXC,EAAE,GAAG,CAAC,GAAG,EAAE;EACXC,EAAE,GAAG,CAAC,GAAG,EAAE;EACXC,EAAE,GAAG,CAAC,GAAG,CAAC;EACVC,EAAE,GAAG,CAAC,GAAG,EAAE;EACXC,EAAE,GAAG,EAAE,GAAG,EAAE;EACZC,EAAE,GAAG,EAAE,GAAG,EAAE;EACZC,EAAE,GAAG,EAAE,GAAG,EAAE;EACZC,EAAE,GAAG,EAAE,GAAG,EAAE;EACZC,EAAE,GAAG,CAAC,GAAGT,EAAE,GAAGA,EAAE;AAEpB,OAAO,SAASU,QAAQA,CAACC,CAAC,EAAE;EAC1B,OAAO,CAAC,GAAGC,SAAS,CAAC,CAAC,GAAGD,CAAC,CAAC;AAC7B;AAEA,OAAO,SAASC,SAASA,CAACD,CAAC,EAAE;EAC3B,OAAO,CAACA,CAAC,GAAG,CAACA,CAAC,IAAIX,EAAE,GAAGS,EAAE,GAAGE,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGT,EAAE,GAAGO,EAAE,IAAIE,CAAC,IAAIV,EAAE,CAAC,GAAGU,CAAC,GAAGR,EAAE,GAAGQ,CAAC,GAAGN,EAAE,GAAGI,EAAE,IAAIE,CAAC,IAAIP,EAAE,CAAC,GAAGO,CAAC,GAAGL,EAAE,GAAGG,EAAE,IAAIE,CAAC,IAAIJ,EAAE,CAAC,GAAGI,CAAC,GAAGH,EAAE;AACnI;AAEA,OAAO,SAASK,WAAWA,CAACF,CAAC,EAAE;EAC7B,OAAO,CAAC,CAACA,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGC,SAAS,CAAC,CAAC,GAAGD,CAAC,CAAC,GAAGC,SAAS,CAACD,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}