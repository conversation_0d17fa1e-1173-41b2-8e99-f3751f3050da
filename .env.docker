# =============================================================================
# DOCKER ENVIRONMENT CONFIGURATION
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Database Configuration (Docker)
DATABASE_URL=****************************************************/genai_workflow_db
POSTGRES_USER=genai_user
POSTGRES_PASSWORD=genai_password
POSTGRES_DB=genai_workflow_db

# Qdrant Configuration (Docker)
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=documents

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=["http://localhost:3000","http://localhost:3001","http://frontend:3000"]

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads
ALLOWED_EXTENSIONS=[".pdf",".txt",".docx"]

# Logging
LOG_LEVEL=INFO
