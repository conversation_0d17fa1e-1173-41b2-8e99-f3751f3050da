import { apiGet, apiPost, apiDelete, handleApiError } from './api';

/**
 * Embedding Service
 * Handles all embedding-related API operations including generation, search, and management
 */

/**
 * Generate embeddings for a document
 * @param {Object} params - Generation parameters
 * @param {number} params.documentId - ID of the document
 * @param {number} params.chunkSize - Size of text chunks (default: 1000)
 * @param {number} params.chunkOverlap - Overlap between chunks (default: 200)
 * @returns {Promise<Object>} Generation response
 */
export const generateEmbeddings = async (params) => {
  try {
    const {
      documentId,
      chunkSize = 1000,
      chunkOverlap = 200,
    } = params;
    
    if (!documentId) {
      throw new Error('Document ID is required');
    }
    
    // Validate parameters
    if (chunkSize < 100 || chunkSize > 5000) {
      throw new Error('Chunk size must be between 100 and 5000 characters');
    }
    
    if (chunkOverlap < 0 || chunkOverlap > 1000) {
      throw new Error('Chunk overlap must be between 0 and 1000 characters');
    }
    
    if (chunkOverlap >= chunkSize) {
      throw new Error('Chunk overlap must be less than chunk size');
    }
    
    const response = await apiPost('/embeddings/generate', {
      document_id: documentId,
      chunk_size: chunkSize,
      chunk_overlap: chunkOverlap,
    });
    
    return {
      success: true,
      data: response,
      message: 'Embeddings generated successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Search for relevant content using embeddings
 * @param {Object} params - Search parameters
 * @param {string} params.query - Search query
 * @param {number} params.documentId - Optional document ID to filter results
 * @param {number} params.nResults - Number of results to return (default: 5)
 * @returns {Promise<Object>} Search results
 */
export const searchEmbeddings = async (params) => {
  try {
    const {
      query,
      documentId = null,
      nResults = 5,
    } = params;
    
    if (!query || query.trim().length === 0) {
      throw new Error('Search query is required');
    }
    
    if (nResults < 1 || nResults > 20) {
      throw new Error('Number of results must be between 1 and 20');
    }
    
    const searchParams = {
      query: query.trim(),
      n_results: nResults,
    };
    
    if (documentId) {
      searchParams.document_id = documentId;
    }
    
    const response = await apiGet('/embeddings/search', searchParams);
    
    return {
      success: true,
      data: response,
      message: 'Search completed successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Delete embeddings for a specific document
 * @param {number} documentId - ID of the document
 * @returns {Promise<Object>} Deletion response
 */
export const deleteDocumentEmbeddings = async (documentId) => {
  try {
    if (!documentId) {
      throw new Error('Document ID is required');
    }
    
    const response = await apiDelete(`/embeddings/${documentId}`);
    
    return {
      success: true,
      data: response,
      message: 'Document embeddings deleted successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get embeddings statistics
 * @returns {Promise<Object>} Statistics about embeddings collection
 */
export const getEmbeddingsStats = async () => {
  try {
    const response = await apiGet('/embeddings/stats');
    
    return {
      success: true,
      data: response,
      message: 'Embeddings statistics retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Validate embedding generation parameters
 * @param {Object} params - Parameters to validate
 * @returns {Object} Validation result
 */
export const validateEmbeddingParams = (params) => {
  const errors = [];
  const { documentId, chunkSize, chunkOverlap } = params;
  
  if (!documentId) {
    errors.push('Document ID is required');
  }
  
  if (chunkSize !== undefined) {
    if (typeof chunkSize !== 'number' || chunkSize < 100 || chunkSize > 5000) {
      errors.push('Chunk size must be a number between 100 and 5000');
    }
  }
  
  if (chunkOverlap !== undefined) {
    if (typeof chunkOverlap !== 'number' || chunkOverlap < 0 || chunkOverlap > 1000) {
      errors.push('Chunk overlap must be a number between 0 and 1000');
    }
  }
  
  if (chunkSize && chunkOverlap && chunkOverlap >= chunkSize) {
    errors.push('Chunk overlap must be less than chunk size');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Format search results for display
 * @param {Array} results - Raw search results
 * @returns {Array} Formatted results
 */
export const formatSearchResults = (results) => {
  if (!Array.isArray(results)) {
    return [];
  }
  
  return results.map((result, index) => ({
    id: `result-${index}`,
    text: result.text || '',
    score: result.similarity_score || 0,
    rank: result.rank || index + 1,
    documentId: result.metadata?.document_id || null,
    chunkIndex: result.metadata?.chunk_index || null,
    timestamp: result.metadata?.timestamp || null,
    preview: result.text ? result.text.substring(0, 200) + '...' : '',
  }));
};

/**
 * Calculate embedding generation progress
 * @param {Object} response - Generation response
 * @returns {Object} Progress information
 */
export const calculateProgress = (response) => {
  if (!response || !response.chunks_processed) {
    return { progress: 0, status: 'pending' };
  }
  
  return {
    progress: response.embeddings_generated ? 100 : 50,
    status: response.embeddings_generated ? 'completed' : 'processing',
    chunksProcessed: response.chunks_processed,
    message: response.message || '',
  };
};

/**
 * Get optimal chunk parameters based on document size
 * @param {number} documentLength - Length of document text
 * @returns {Object} Recommended chunk parameters
 */
export const getOptimalChunkParams = (documentLength) => {
  if (documentLength < 2000) {
    return { chunkSize: 500, chunkOverlap: 100 };
  } else if (documentLength < 10000) {
    return { chunkSize: 1000, chunkOverlap: 200 };
  } else if (documentLength < 50000) {
    return { chunkSize: 1500, chunkOverlap: 300 };
  } else {
    return { chunkSize: 2000, chunkOverlap: 400 };
  }
};

export default {
  generateEmbeddings,
  searchEmbeddings,
  deleteDocumentEmbeddings,
  getEmbeddingsStats,
  validateEmbeddingParams,
  formatSearchResults,
  calculateProgress,
  getOptimalChunkParams,
};
