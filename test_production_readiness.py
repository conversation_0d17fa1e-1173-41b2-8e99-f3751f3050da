#!/usr/bin/env python3
"""
Comprehensive Production Readiness Test Suite
Tests all features and deployment requirements for Render hosting
"""

import requests
import json
import os
import subprocess
import time
from pathlib import Path

BASE_URL = "http://localhost:8000"

def test_environment_configuration():
    """Test environment configuration and secrets"""
    print("🔧 Testing Environment Configuration")
    print("=" * 60)
    
    # Check for required environment files
    env_files = ['.env.example', '.env.production', 'docker-compose.prod.yml']
    for env_file in env_files:
        if os.path.exists(env_file):
            print(f"✅ {env_file} exists")
        else:
            print(f"❌ {env_file} missing")
    
    # Check for sensitive data in code
    sensitive_patterns = ['sk-', 'OPENAI_API_KEY=sk-', 'password=', 'secret=']
    print("\n🔍 Checking for hardcoded secrets...")
    
    # Check backend files
    backend_files = list(Path('backend').rglob('*.py'))
    frontend_files = list(Path('frontend/src').rglob('*.js')) + list(Path('frontend/src').rglob('*.jsx'))
    
    secrets_found = False
    for file_path in backend_files + frontend_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                for pattern in sensitive_patterns:
                    if pattern in content and 'example' not in str(file_path).lower():
                        print(f"⚠️  Potential secret in {file_path}: {pattern}")
                        secrets_found = True
        except:
            continue
    
    if not secrets_found:
        print("✅ No hardcoded secrets found")
    
    return not secrets_found

def test_database_configuration():
    """Test database configuration for production"""
    print("\n🗄️  Testing Database Configuration")
    print("=" * 60)
    
    try:
        # Test database connection
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Database connection working")
            
            # Test document operations
            docs_response = requests.get(f"{BASE_URL}/api/documents/")
            if docs_response.status_code == 200:
                docs = docs_response.json()
                print(f"✅ Document API working ({len(docs)} documents)")
                return True
            else:
                print("❌ Document API failed")
                return False
        else:
            print("❌ Database connection failed")
            return False
    except Exception as e:
        print(f"❌ Database test error: {e}")
        return False

def test_api_endpoints():
    """Test all critical API endpoints"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 60)
    
    endpoints = [
        ("/health", "GET", "Health check"),
        ("/api/documents/", "GET", "Document list"),
        ("/api/workflow/validate", "POST", "Workflow validation"),
    ]
    
    all_passed = True
    for endpoint, method, description in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            else:
                # Simple POST test
                response = requests.post(f"{BASE_URL}{endpoint}", 
                                       json={"test": "data"}, timeout=10)
            
            if response.status_code in [200, 422]:  # 422 is expected for invalid data
                print(f"✅ {description}: {response.status_code}")
            else:
                print(f"❌ {description}: {response.status_code}")
                all_passed = False
        except Exception as e:
            print(f"❌ {description}: {e}")
            all_passed = False
    
    return all_passed

def test_file_upload_system():
    """Test file upload and processing"""
    print("\n📁 Testing File Upload System")
    print("=" * 60)
    
    try:
        # Check upload directory
        upload_dir = Path("backend/uploads")
        if upload_dir.exists():
            print("✅ Upload directory exists")
            
            # Check permissions
            test_file = upload_dir / "test.txt"
            try:
                test_file.write_text("test")
                test_file.unlink()
                print("✅ Upload directory writable")
                return True
            except Exception as e:
                print(f"❌ Upload directory not writable: {e}")
                return False
        else:
            print("❌ Upload directory missing")
            return False
    except Exception as e:
        print(f"❌ File upload test error: {e}")
        return False

def test_byok_implementation():
    """Test Bring Your Own Key implementation"""
    print("\n🔑 Testing BYOK Implementation")
    print("=" * 60)
    
    # Test workflow without API key
    workflow_no_key = {
        "nodes": [
            {
                "id": "llm_1",
                "type": "llm_engine",
                "position": {"x": 100, "y": 100},
                "data": {"config": {"model": "gpt-3.5-turbo"}}  # No API key
            }
        ],
        "connections": []
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/workflow/execute",
            json={
                "user_id": "test",
                "query": "test",
                "workflow": workflow_no_key
            },
            timeout=10
        )
        
        if response.status_code != 200:
            result = response.json()
            if "API key" in str(result):
                print("✅ BYOK validation working")
                return True
            else:
                print(f"⚠️  Unexpected error: {result}")
                return False
        else:
            print("❌ Should require API key")
            return False
    except Exception as e:
        print(f"❌ BYOK test error: {e}")
        return False

def test_frontend_build():
    """Test frontend build process"""
    print("\n🎨 Testing Frontend Build")
    print("=" * 60)
    
    try:
        # Check if build directory exists
        build_dir = Path("frontend/build")
        if build_dir.exists():
            print("✅ Frontend build directory exists")
            
            # Check for essential files
            essential_files = ["index.html", "static"]
            for file_name in essential_files:
                if (build_dir / file_name).exists():
                    print(f"✅ {file_name} exists in build")
                else:
                    print(f"❌ {file_name} missing in build")
                    return False
            return True
        else:
            print("❌ Frontend build directory missing")
            print("💡 Run 'npm run build' in frontend directory")
            return False
    except Exception as e:
        print(f"❌ Frontend build test error: {e}")
        return False

def test_docker_configuration():
    """Test Docker configuration"""
    print("\n🐳 Testing Docker Configuration")
    print("=" * 60)
    
    docker_files = [
        "backend/Dockerfile",
        "frontend/Dockerfile", 
        "docker-compose.yml",
        "docker-compose.prod.yml"
    ]
    
    all_exist = True
    for docker_file in docker_files:
        if os.path.exists(docker_file):
            print(f"✅ {docker_file} exists")
        else:
            print(f"❌ {docker_file} missing")
            all_exist = False
    
    return all_exist

def test_security_headers():
    """Test security headers and configurations"""
    print("\n🛡️  Testing Security Configuration")
    print("=" * 60)
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        headers = response.headers
        
        # Check CORS headers
        if 'Access-Control-Allow-Origin' in headers:
            print("✅ CORS headers configured")
        else:
            print("⚠️  CORS headers missing")
        
        # Check if running in debug mode (should be false for production)
        if 'Server' in headers and 'uvicorn' in headers['Server'].lower():
            print("✅ Using Uvicorn server")
        
        return True
    except Exception as e:
        print(f"❌ Security test error: {e}")
        return False

def test_performance():
    """Test basic performance metrics"""
    print("\n⚡ Testing Performance")
    print("=" * 60)
    
    try:
        # Test response times
        start_time = time.time()
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        response_time = time.time() - start_time
        
        if response_time < 1.0:
            print(f"✅ Health endpoint response time: {response_time:.3f}s")
        else:
            print(f"⚠️  Slow health endpoint: {response_time:.3f}s")
        
        # Test document list performance
        start_time = time.time()
        response = requests.get(f"{BASE_URL}/api/documents/", timeout=10)
        response_time = time.time() - start_time
        
        if response_time < 2.0:
            print(f"✅ Documents endpoint response time: {response_time:.3f}s")
            return True
        else:
            print(f"⚠️  Slow documents endpoint: {response_time:.3f}s")
            return False
    except Exception as e:
        print(f"❌ Performance test error: {e}")
        return False

def main():
    """Main production readiness test"""
    print("🚀 PRODUCTION READINESS TEST SUITE")
    print("=" * 60)
    print("Testing all features for Render deployment")
    print("=" * 60)
    
    tests = [
        ("Environment Configuration", test_environment_configuration),
        ("Database Configuration", test_database_configuration),
        ("API Endpoints", test_api_endpoints),
        ("File Upload System", test_file_upload_system),
        ("BYOK Implementation", test_byok_implementation),
        ("Frontend Build", test_frontend_build),
        ("Docker Configuration", test_docker_configuration),
        ("Security Configuration", test_security_headers),
        ("Performance", test_performance),
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    print("\n" + "=" * 60)
    print("📊 PRODUCTION READINESS SUMMARY:")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 PRODUCTION READY!")
        print("✅ All tests passed - Ready for Render deployment")
        print("\n🚀 Next Steps for Render Deployment:")
        print("   1. Create Render account")
        print("   2. Connect GitHub repository")
        print("   3. Configure environment variables")
        print("   4. Deploy backend and frontend services")
        print("   5. Set up PostgreSQL database")
    else:
        print("⚠️  PRODUCTION ISSUES FOUND")
        print("❌ Fix the failing tests before deployment")
        print("\n🔧 Common fixes:")
        print("   • Run 'npm run build' in frontend directory")
        print("   • Check environment configuration")
        print("   • Verify all Docker files exist")
        print("   • Test database connectivity")

if __name__ == "__main__":
    main()
