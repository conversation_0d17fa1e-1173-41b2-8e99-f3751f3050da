{"ast": null, "code": "import { dispatch } from \"d3-dispatch\";\nimport { dragDisable, dragEnable } from \"d3-drag\";\nimport { interpolateZoom } from \"d3-interpolate\";\nimport { select, pointer } from \"d3-selection\";\nimport { interrupt } from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport ZoomEvent from \"./event.js\";\nimport { Transform, identity } from \"./transform.js\";\nimport noevent, { nopropagation } from \"./noevent.js\";\n\n// Ignore right-click, since that should open the context menu.\n// except for pinch-to-zoom, which is sent as a wheel+ctrlKey event\nfunction defaultFilter(event) {\n  return (!event.ctrlKey || event.type === 'wheel') && !event.button;\n}\nfunction defaultExtent() {\n  var e = this;\n  if (e instanceof SVGElement) {\n    e = e.ownerSVGElement || e;\n    if (e.hasAttribute(\"viewBox\")) {\n      e = e.viewBox.baseVal;\n      return [[e.x, e.y], [e.x + e.width, e.y + e.height]];\n    }\n    return [[0, 0], [e.width.baseVal.value, e.height.baseVal.value]];\n  }\n  return [[0, 0], [e.clientWidth, e.clientHeight]];\n}\nfunction defaultTransform() {\n  return this.__zoom || identity;\n}\nfunction defaultWheelDelta(event) {\n  return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * (event.ctrlKey ? 10 : 1);\n}\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || \"ontouchstart\" in this;\n}\nfunction defaultConstrain(transform, extent, translateExtent) {\n  var dx0 = transform.invertX(extent[0][0]) - translateExtent[0][0],\n    dx1 = transform.invertX(extent[1][0]) - translateExtent[1][0],\n    dy0 = transform.invertY(extent[0][1]) - translateExtent[0][1],\n    dy1 = transform.invertY(extent[1][1]) - translateExtent[1][1];\n  return transform.translate(dx1 > dx0 ? (dx0 + dx1) / 2 : Math.min(0, dx0) || Math.max(0, dx1), dy1 > dy0 ? (dy0 + dy1) / 2 : Math.min(0, dy0) || Math.max(0, dy1));\n}\nexport default function () {\n  var filter = defaultFilter,\n    extent = defaultExtent,\n    constrain = defaultConstrain,\n    wheelDelta = defaultWheelDelta,\n    touchable = defaultTouchable,\n    scaleExtent = [0, Infinity],\n    translateExtent = [[-Infinity, -Infinity], [Infinity, Infinity]],\n    duration = 250,\n    interpolate = interpolateZoom,\n    listeners = dispatch(\"start\", \"zoom\", \"end\"),\n    touchstarting,\n    touchfirst,\n    touchending,\n    touchDelay = 500,\n    wheelDelay = 150,\n    clickDistance2 = 0,\n    tapDistance = 10;\n  function zoom(selection) {\n    selection.property(\"__zoom\", defaultTransform).on(\"wheel.zoom\", wheeled, {\n      passive: false\n    }).on(\"mousedown.zoom\", mousedowned).on(\"dblclick.zoom\", dblclicked).filter(touchable).on(\"touchstart.zoom\", touchstarted).on(\"touchmove.zoom\", touchmoved).on(\"touchend.zoom touchcancel.zoom\", touchended).style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n  zoom.transform = function (collection, transform, point, event) {\n    var selection = collection.selection ? collection.selection() : collection;\n    selection.property(\"__zoom\", defaultTransform);\n    if (collection !== selection) {\n      schedule(collection, transform, point, event);\n    } else {\n      selection.interrupt().each(function () {\n        gesture(this, arguments).event(event).start().zoom(null, typeof transform === \"function\" ? transform.apply(this, arguments) : transform).end();\n      });\n    }\n  };\n  zoom.scaleBy = function (selection, k, p, event) {\n    zoom.scaleTo(selection, function () {\n      var k0 = this.__zoom.k,\n        k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return k0 * k1;\n    }, p, event);\n  };\n  zoom.scaleTo = function (selection, k, p, event) {\n    zoom.transform(selection, function () {\n      var e = extent.apply(this, arguments),\n        t0 = this.__zoom,\n        p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p,\n        p1 = t0.invert(p0),\n        k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return constrain(translate(scale(t0, k1), p0, p1), e, translateExtent);\n    }, p, event);\n  };\n  zoom.translateBy = function (selection, x, y, event) {\n    zoom.transform(selection, function () {\n      return constrain(this.__zoom.translate(typeof x === \"function\" ? x.apply(this, arguments) : x, typeof y === \"function\" ? y.apply(this, arguments) : y), extent.apply(this, arguments), translateExtent);\n    }, null, event);\n  };\n  zoom.translateTo = function (selection, x, y, p, event) {\n    zoom.transform(selection, function () {\n      var e = extent.apply(this, arguments),\n        t = this.__zoom,\n        p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p;\n      return constrain(identity.translate(p0[0], p0[1]).scale(t.k).translate(typeof x === \"function\" ? -x.apply(this, arguments) : -x, typeof y === \"function\" ? -y.apply(this, arguments) : -y), e, translateExtent);\n    }, p, event);\n  };\n  function scale(transform, k) {\n    k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], k));\n    return k === transform.k ? transform : new Transform(k, transform.x, transform.y);\n  }\n  function translate(transform, p0, p1) {\n    var x = p0[0] - p1[0] * transform.k,\n      y = p0[1] - p1[1] * transform.k;\n    return x === transform.x && y === transform.y ? transform : new Transform(transform.k, x, y);\n  }\n  function centroid(extent) {\n    return [(+extent[0][0] + +extent[1][0]) / 2, (+extent[0][1] + +extent[1][1]) / 2];\n  }\n  function schedule(transition, transform, point, event) {\n    transition.on(\"start.zoom\", function () {\n      gesture(this, arguments).event(event).start();\n    }).on(\"interrupt.zoom end.zoom\", function () {\n      gesture(this, arguments).event(event).end();\n    }).tween(\"zoom\", function () {\n      var that = this,\n        args = arguments,\n        g = gesture(that, args).event(event),\n        e = extent.apply(that, args),\n        p = point == null ? centroid(e) : typeof point === \"function\" ? point.apply(that, args) : point,\n        w = Math.max(e[1][0] - e[0][0], e[1][1] - e[0][1]),\n        a = that.__zoom,\n        b = typeof transform === \"function\" ? transform.apply(that, args) : transform,\n        i = interpolate(a.invert(p).concat(w / a.k), b.invert(p).concat(w / b.k));\n      return function (t) {\n        if (t === 1) t = b; // Avoid rounding error on end.\n        else {\n          var l = i(t),\n            k = w / l[2];\n          t = new Transform(k, p[0] - l[0] * k, p[1] - l[1] * k);\n        }\n        g.zoom(null, t);\n      };\n    });\n  }\n  function gesture(that, args, clean) {\n    return !clean && that.__zooming || new Gesture(that, args);\n  }\n  function Gesture(that, args) {\n    this.that = that;\n    this.args = args;\n    this.active = 0;\n    this.sourceEvent = null;\n    this.extent = extent.apply(that, args);\n    this.taps = 0;\n  }\n  Gesture.prototype = {\n    event: function (event) {\n      if (event) this.sourceEvent = event;\n      return this;\n    },\n    start: function () {\n      if (++this.active === 1) {\n        this.that.__zooming = this;\n        this.emit(\"start\");\n      }\n      return this;\n    },\n    zoom: function (key, transform) {\n      if (this.mouse && key !== \"mouse\") this.mouse[1] = transform.invert(this.mouse[0]);\n      if (this.touch0 && key !== \"touch\") this.touch0[1] = transform.invert(this.touch0[0]);\n      if (this.touch1 && key !== \"touch\") this.touch1[1] = transform.invert(this.touch1[0]);\n      this.that.__zoom = transform;\n      this.emit(\"zoom\");\n      return this;\n    },\n    end: function () {\n      if (--this.active === 0) {\n        delete this.that.__zooming;\n        this.emit(\"end\");\n      }\n      return this;\n    },\n    emit: function (type) {\n      var d = select(this.that).datum();\n      listeners.call(type, this.that, new ZoomEvent(type, {\n        sourceEvent: this.sourceEvent,\n        target: zoom,\n        type,\n        transform: this.that.__zoom,\n        dispatch: listeners\n      }), d);\n    }\n  };\n  function wheeled(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var g = gesture(this, args).event(event),\n      t = this.__zoom,\n      k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], t.k * Math.pow(2, wheelDelta.apply(this, arguments)))),\n      p = pointer(event);\n\n    // If the mouse is in the same location as before, reuse it.\n    // If there were recent wheel events, reset the wheel idle timeout.\n    if (g.wheel) {\n      if (g.mouse[0][0] !== p[0] || g.mouse[0][1] !== p[1]) {\n        g.mouse[1] = t.invert(g.mouse[0] = p);\n      }\n      clearTimeout(g.wheel);\n    }\n\n    // If this wheel event won’t trigger a transform change, ignore it.\n    else if (t.k === k) return;\n\n    // Otherwise, capture the mouse point and location at the start.\n    else {\n      g.mouse = [p, t.invert(p)];\n      interrupt(this);\n      g.start();\n    }\n    noevent(event);\n    g.wheel = setTimeout(wheelidled, wheelDelay);\n    g.zoom(\"mouse\", constrain(translate(scale(t, k), g.mouse[0], g.mouse[1]), g.extent, translateExtent));\n    function wheelidled() {\n      g.wheel = null;\n      g.end();\n    }\n  }\n  function mousedowned(event, ...args) {\n    if (touchending || !filter.apply(this, arguments)) return;\n    var currentTarget = event.currentTarget,\n      g = gesture(this, args, true).event(event),\n      v = select(event.view).on(\"mousemove.zoom\", mousemoved, true).on(\"mouseup.zoom\", mouseupped, true),\n      p = pointer(event, currentTarget),\n      x0 = event.clientX,\n      y0 = event.clientY;\n    dragDisable(event.view);\n    nopropagation(event);\n    g.mouse = [p, this.__zoom.invert(p)];\n    interrupt(this);\n    g.start();\n    function mousemoved(event) {\n      noevent(event);\n      if (!g.moved) {\n        var dx = event.clientX - x0,\n          dy = event.clientY - y0;\n        g.moved = dx * dx + dy * dy > clickDistance2;\n      }\n      g.event(event).zoom(\"mouse\", constrain(translate(g.that.__zoom, g.mouse[0] = pointer(event, currentTarget), g.mouse[1]), g.extent, translateExtent));\n    }\n    function mouseupped(event) {\n      v.on(\"mousemove.zoom mouseup.zoom\", null);\n      dragEnable(event.view, g.moved);\n      noevent(event);\n      g.event(event).end();\n    }\n  }\n  function dblclicked(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var t0 = this.__zoom,\n      p0 = pointer(event.changedTouches ? event.changedTouches[0] : event, this),\n      p1 = t0.invert(p0),\n      k1 = t0.k * (event.shiftKey ? 0.5 : 2),\n      t1 = constrain(translate(scale(t0, k1), p0, p1), extent.apply(this, args), translateExtent);\n    noevent(event);\n    if (duration > 0) select(this).transition().duration(duration).call(schedule, t1, p0, event);else select(this).call(zoom.transform, t1, p0, event);\n  }\n  function touchstarted(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var touches = event.touches,\n      n = touches.length,\n      g = gesture(this, args, event.changedTouches.length === n).event(event),\n      started,\n      i,\n      t,\n      p;\n    nopropagation(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      p = [p, this.__zoom.invert(p), t.identifier];\n      if (!g.touch0) g.touch0 = p, started = true, g.taps = 1 + !!touchstarting;else if (!g.touch1 && g.touch0[2] !== p[2]) g.touch1 = p, g.taps = 0;\n    }\n    if (touchstarting) touchstarting = clearTimeout(touchstarting);\n    if (started) {\n      if (g.taps < 2) touchfirst = p[0], touchstarting = setTimeout(function () {\n        touchstarting = null;\n      }, touchDelay);\n      interrupt(this);\n      g.start();\n    }\n  }\n  function touchmoved(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n      touches = event.changedTouches,\n      n = touches.length,\n      i,\n      t,\n      p,\n      l;\n    noevent(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      if (g.touch0 && g.touch0[2] === t.identifier) g.touch0[0] = p;else if (g.touch1 && g.touch1[2] === t.identifier) g.touch1[0] = p;\n    }\n    t = g.that.__zoom;\n    if (g.touch1) {\n      var p0 = g.touch0[0],\n        l0 = g.touch0[1],\n        p1 = g.touch1[0],\n        l1 = g.touch1[1],\n        dp = (dp = p1[0] - p0[0]) * dp + (dp = p1[1] - p0[1]) * dp,\n        dl = (dl = l1[0] - l0[0]) * dl + (dl = l1[1] - l0[1]) * dl;\n      t = scale(t, Math.sqrt(dp / dl));\n      p = [(p0[0] + p1[0]) / 2, (p0[1] + p1[1]) / 2];\n      l = [(l0[0] + l1[0]) / 2, (l0[1] + l1[1]) / 2];\n    } else if (g.touch0) p = g.touch0[0], l = g.touch0[1];else return;\n    g.zoom(\"touch\", constrain(translate(t, p, l), g.extent, translateExtent));\n  }\n  function touchended(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n      touches = event.changedTouches,\n      n = touches.length,\n      i,\n      t;\n    nopropagation(event);\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function () {\n      touchending = null;\n    }, touchDelay);\n    for (i = 0; i < n; ++i) {\n      t = touches[i];\n      if (g.touch0 && g.touch0[2] === t.identifier) delete g.touch0;else if (g.touch1 && g.touch1[2] === t.identifier) delete g.touch1;\n    }\n    if (g.touch1 && !g.touch0) g.touch0 = g.touch1, delete g.touch1;\n    if (g.touch0) g.touch0[1] = this.__zoom.invert(g.touch0[0]);else {\n      g.end();\n      // If this was a dbltap, reroute to the (optional) dblclick.zoom handler.\n      if (g.taps === 2) {\n        t = pointer(t, this);\n        if (Math.hypot(touchfirst[0] - t[0], touchfirst[1] - t[1]) < tapDistance) {\n          var p = select(this).on(\"dblclick.zoom\");\n          if (p) p.apply(this, arguments);\n        }\n      }\n    }\n  }\n  zoom.wheelDelta = function (_) {\n    return arguments.length ? (wheelDelta = typeof _ === \"function\" ? _ : constant(+_), zoom) : wheelDelta;\n  };\n  zoom.filter = function (_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), zoom) : filter;\n  };\n  zoom.touchable = function (_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), zoom) : touchable;\n  };\n  zoom.extent = function (_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant([[+_[0][0], +_[0][1]], [+_[1][0], +_[1][1]]]), zoom) : extent;\n  };\n  zoom.scaleExtent = function (_) {\n    return arguments.length ? (scaleExtent[0] = +_[0], scaleExtent[1] = +_[1], zoom) : [scaleExtent[0], scaleExtent[1]];\n  };\n  zoom.translateExtent = function (_) {\n    return arguments.length ? (translateExtent[0][0] = +_[0][0], translateExtent[1][0] = +_[1][0], translateExtent[0][1] = +_[0][1], translateExtent[1][1] = +_[1][1], zoom) : [[translateExtent[0][0], translateExtent[0][1]], [translateExtent[1][0], translateExtent[1][1]]];\n  };\n  zoom.constrain = function (_) {\n    return arguments.length ? (constrain = _, zoom) : constrain;\n  };\n  zoom.duration = function (_) {\n    return arguments.length ? (duration = +_, zoom) : duration;\n  };\n  zoom.interpolate = function (_) {\n    return arguments.length ? (interpolate = _, zoom) : interpolate;\n  };\n  zoom.on = function () {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? zoom : value;\n  };\n  zoom.clickDistance = function (_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, zoom) : Math.sqrt(clickDistance2);\n  };\n  zoom.tapDistance = function (_) {\n    return arguments.length ? (tapDistance = +_, zoom) : tapDistance;\n  };\n  return zoom;\n}", "map": {"version": 3, "names": ["dispatch", "dragDisable", "dragEnable", "interpolateZoom", "select", "pointer", "interrupt", "constant", "ZoomEvent", "Transform", "identity", "noevent", "nopropagation", "defaultFilter", "event", "ctrl<PERSON>ey", "type", "button", "defaultExtent", "e", "SVGElement", "ownerSVGElement", "hasAttribute", "viewBox", "baseVal", "x", "y", "width", "height", "value", "clientWidth", "clientHeight", "defaultTransform", "__zoom", "defaultWheelDelta", "deltaY", "deltaMode", "defaultTouchable", "navigator", "maxTouchPoints", "defaultConstrain", "transform", "extent", "translateExtent", "dx0", "invertX", "dx1", "dy0", "invertY", "dy1", "translate", "Math", "min", "max", "filter", "constrain", "wheelDelta", "touchable", "scaleExtent", "Infinity", "duration", "interpolate", "listeners", "touchstarting", "touchfirst", "touchending", "touchDelay", "wheelDelay", "clickDistance2", "tapDistance", "zoom", "selection", "property", "on", "wheeled", "passive", "mousedowned", "dblclicked", "touchstarted", "touchmoved", "touchended", "style", "collection", "point", "schedule", "each", "gesture", "arguments", "start", "apply", "end", "scaleBy", "k", "p", "scaleTo", "k0", "k1", "t0", "p0", "centroid", "p1", "invert", "scale", "translateBy", "translateTo", "t", "transition", "tween", "that", "args", "g", "w", "a", "b", "i", "concat", "l", "clean", "__zooming", "Gesture", "active", "sourceEvent", "taps", "prototype", "emit", "key", "mouse", "touch0", "touch1", "d", "datum", "call", "target", "pow", "wheel", "clearTimeout", "setTimeout", "wheelidled", "currentTarget", "v", "view", "mousemoved", "mouseupped", "x0", "clientX", "y0", "clientY", "moved", "dx", "dy", "changedTouches", "shift<PERSON>ey", "t1", "touches", "n", "length", "started", "identifier", "l0", "l1", "dp", "dl", "sqrt", "hypot", "_", "clickDistance"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-zoom/src/zoom.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {dragDisable, dragEnable} from \"d3-drag\";\nimport {interpolateZoom} from \"d3-interpolate\";\nimport {select, pointer} from \"d3-selection\";\nimport {interrupt} from \"d3-transition\";\nimport constant from \"./constant.js\";\nimport ZoomEvent from \"./event.js\";\nimport {Transform, identity} from \"./transform.js\";\nimport noevent, {nopropagation} from \"./noevent.js\";\n\n// Ignore right-click, since that should open the context menu.\n// except for pinch-to-zoom, which is sent as a wheel+ctrlKey event\nfunction defaultFilter(event) {\n  return (!event.ctrlKey || event.type === 'wheel') && !event.button;\n}\n\nfunction defaultExtent() {\n  var e = this;\n  if (e instanceof SVGElement) {\n    e = e.ownerSVGElement || e;\n    if (e.hasAttribute(\"viewBox\")) {\n      e = e.viewBox.baseVal;\n      return [[e.x, e.y], [e.x + e.width, e.y + e.height]];\n    }\n    return [[0, 0], [e.width.baseVal.value, e.height.baseVal.value]];\n  }\n  return [[0, 0], [e.clientWidth, e.clientHeight]];\n}\n\nfunction defaultTransform() {\n  return this.__zoom || identity;\n}\n\nfunction defaultWheelDelta(event) {\n  return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * (event.ctrlKey ? 10 : 1);\n}\n\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || (\"ontouchstart\" in this);\n}\n\nfunction defaultConstrain(transform, extent, translateExtent) {\n  var dx0 = transform.invertX(extent[0][0]) - translateExtent[0][0],\n      dx1 = transform.invertX(extent[1][0]) - translateExtent[1][0],\n      dy0 = transform.invertY(extent[0][1]) - translateExtent[0][1],\n      dy1 = transform.invertY(extent[1][1]) - translateExtent[1][1];\n  return transform.translate(\n    dx1 > dx0 ? (dx0 + dx1) / 2 : Math.min(0, dx0) || Math.max(0, dx1),\n    dy1 > dy0 ? (dy0 + dy1) / 2 : Math.min(0, dy0) || Math.max(0, dy1)\n  );\n}\n\nexport default function() {\n  var filter = defaultFilter,\n      extent = defaultExtent,\n      constrain = defaultConstrain,\n      wheelDelta = defaultWheelDelta,\n      touchable = defaultTouchable,\n      scaleExtent = [0, Infinity],\n      translateExtent = [[-Infinity, -Infinity], [Infinity, Infinity]],\n      duration = 250,\n      interpolate = interpolateZoom,\n      listeners = dispatch(\"start\", \"zoom\", \"end\"),\n      touchstarting,\n      touchfirst,\n      touchending,\n      touchDelay = 500,\n      wheelDelay = 150,\n      clickDistance2 = 0,\n      tapDistance = 10;\n\n  function zoom(selection) {\n    selection\n        .property(\"__zoom\", defaultTransform)\n        .on(\"wheel.zoom\", wheeled, {passive: false})\n        .on(\"mousedown.zoom\", mousedowned)\n        .on(\"dblclick.zoom\", dblclicked)\n      .filter(touchable)\n        .on(\"touchstart.zoom\", touchstarted)\n        .on(\"touchmove.zoom\", touchmoved)\n        .on(\"touchend.zoom touchcancel.zoom\", touchended)\n        .style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n\n  zoom.transform = function(collection, transform, point, event) {\n    var selection = collection.selection ? collection.selection() : collection;\n    selection.property(\"__zoom\", defaultTransform);\n    if (collection !== selection) {\n      schedule(collection, transform, point, event);\n    } else {\n      selection.interrupt().each(function() {\n        gesture(this, arguments)\n          .event(event)\n          .start()\n          .zoom(null, typeof transform === \"function\" ? transform.apply(this, arguments) : transform)\n          .end();\n      });\n    }\n  };\n\n  zoom.scaleBy = function(selection, k, p, event) {\n    zoom.scaleTo(selection, function() {\n      var k0 = this.__zoom.k,\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return k0 * k1;\n    }, p, event);\n  };\n\n  zoom.scaleTo = function(selection, k, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t0 = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p,\n          p1 = t0.invert(p0),\n          k1 = typeof k === \"function\" ? k.apply(this, arguments) : k;\n      return constrain(translate(scale(t0, k1), p0, p1), e, translateExtent);\n    }, p, event);\n  };\n\n  zoom.translateBy = function(selection, x, y, event) {\n    zoom.transform(selection, function() {\n      return constrain(this.__zoom.translate(\n        typeof x === \"function\" ? x.apply(this, arguments) : x,\n        typeof y === \"function\" ? y.apply(this, arguments) : y\n      ), extent.apply(this, arguments), translateExtent);\n    }, null, event);\n  };\n\n  zoom.translateTo = function(selection, x, y, p, event) {\n    zoom.transform(selection, function() {\n      var e = extent.apply(this, arguments),\n          t = this.__zoom,\n          p0 = p == null ? centroid(e) : typeof p === \"function\" ? p.apply(this, arguments) : p;\n      return constrain(identity.translate(p0[0], p0[1]).scale(t.k).translate(\n        typeof x === \"function\" ? -x.apply(this, arguments) : -x,\n        typeof y === \"function\" ? -y.apply(this, arguments) : -y\n      ), e, translateExtent);\n    }, p, event);\n  };\n\n  function scale(transform, k) {\n    k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], k));\n    return k === transform.k ? transform : new Transform(k, transform.x, transform.y);\n  }\n\n  function translate(transform, p0, p1) {\n    var x = p0[0] - p1[0] * transform.k, y = p0[1] - p1[1] * transform.k;\n    return x === transform.x && y === transform.y ? transform : new Transform(transform.k, x, y);\n  }\n\n  function centroid(extent) {\n    return [(+extent[0][0] + +extent[1][0]) / 2, (+extent[0][1] + +extent[1][1]) / 2];\n  }\n\n  function schedule(transition, transform, point, event) {\n    transition\n        .on(\"start.zoom\", function() { gesture(this, arguments).event(event).start(); })\n        .on(\"interrupt.zoom end.zoom\", function() { gesture(this, arguments).event(event).end(); })\n        .tween(\"zoom\", function() {\n          var that = this,\n              args = arguments,\n              g = gesture(that, args).event(event),\n              e = extent.apply(that, args),\n              p = point == null ? centroid(e) : typeof point === \"function\" ? point.apply(that, args) : point,\n              w = Math.max(e[1][0] - e[0][0], e[1][1] - e[0][1]),\n              a = that.__zoom,\n              b = typeof transform === \"function\" ? transform.apply(that, args) : transform,\n              i = interpolate(a.invert(p).concat(w / a.k), b.invert(p).concat(w / b.k));\n          return function(t) {\n            if (t === 1) t = b; // Avoid rounding error on end.\n            else { var l = i(t), k = w / l[2]; t = new Transform(k, p[0] - l[0] * k, p[1] - l[1] * k); }\n            g.zoom(null, t);\n          };\n        });\n  }\n\n  function gesture(that, args, clean) {\n    return (!clean && that.__zooming) || new Gesture(that, args);\n  }\n\n  function Gesture(that, args) {\n    this.that = that;\n    this.args = args;\n    this.active = 0;\n    this.sourceEvent = null;\n    this.extent = extent.apply(that, args);\n    this.taps = 0;\n  }\n\n  Gesture.prototype = {\n    event: function(event) {\n      if (event) this.sourceEvent = event;\n      return this;\n    },\n    start: function() {\n      if (++this.active === 1) {\n        this.that.__zooming = this;\n        this.emit(\"start\");\n      }\n      return this;\n    },\n    zoom: function(key, transform) {\n      if (this.mouse && key !== \"mouse\") this.mouse[1] = transform.invert(this.mouse[0]);\n      if (this.touch0 && key !== \"touch\") this.touch0[1] = transform.invert(this.touch0[0]);\n      if (this.touch1 && key !== \"touch\") this.touch1[1] = transform.invert(this.touch1[0]);\n      this.that.__zoom = transform;\n      this.emit(\"zoom\");\n      return this;\n    },\n    end: function() {\n      if (--this.active === 0) {\n        delete this.that.__zooming;\n        this.emit(\"end\");\n      }\n      return this;\n    },\n    emit: function(type) {\n      var d = select(this.that).datum();\n      listeners.call(\n        type,\n        this.that,\n        new ZoomEvent(type, {\n          sourceEvent: this.sourceEvent,\n          target: zoom,\n          type,\n          transform: this.that.__zoom,\n          dispatch: listeners\n        }),\n        d\n      );\n    }\n  };\n\n  function wheeled(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var g = gesture(this, args).event(event),\n        t = this.__zoom,\n        k = Math.max(scaleExtent[0], Math.min(scaleExtent[1], t.k * Math.pow(2, wheelDelta.apply(this, arguments)))),\n        p = pointer(event);\n\n    // If the mouse is in the same location as before, reuse it.\n    // If there were recent wheel events, reset the wheel idle timeout.\n    if (g.wheel) {\n      if (g.mouse[0][0] !== p[0] || g.mouse[0][1] !== p[1]) {\n        g.mouse[1] = t.invert(g.mouse[0] = p);\n      }\n      clearTimeout(g.wheel);\n    }\n\n    // If this wheel event won’t trigger a transform change, ignore it.\n    else if (t.k === k) return;\n\n    // Otherwise, capture the mouse point and location at the start.\n    else {\n      g.mouse = [p, t.invert(p)];\n      interrupt(this);\n      g.start();\n    }\n\n    noevent(event);\n    g.wheel = setTimeout(wheelidled, wheelDelay);\n    g.zoom(\"mouse\", constrain(translate(scale(t, k), g.mouse[0], g.mouse[1]), g.extent, translateExtent));\n\n    function wheelidled() {\n      g.wheel = null;\n      g.end();\n    }\n  }\n\n  function mousedowned(event, ...args) {\n    if (touchending || !filter.apply(this, arguments)) return;\n    var currentTarget = event.currentTarget,\n        g = gesture(this, args, true).event(event),\n        v = select(event.view).on(\"mousemove.zoom\", mousemoved, true).on(\"mouseup.zoom\", mouseupped, true),\n        p = pointer(event, currentTarget),\n        x0 = event.clientX,\n        y0 = event.clientY;\n\n    dragDisable(event.view);\n    nopropagation(event);\n    g.mouse = [p, this.__zoom.invert(p)];\n    interrupt(this);\n    g.start();\n\n    function mousemoved(event) {\n      noevent(event);\n      if (!g.moved) {\n        var dx = event.clientX - x0, dy = event.clientY - y0;\n        g.moved = dx * dx + dy * dy > clickDistance2;\n      }\n      g.event(event)\n       .zoom(\"mouse\", constrain(translate(g.that.__zoom, g.mouse[0] = pointer(event, currentTarget), g.mouse[1]), g.extent, translateExtent));\n    }\n\n    function mouseupped(event) {\n      v.on(\"mousemove.zoom mouseup.zoom\", null);\n      dragEnable(event.view, g.moved);\n      noevent(event);\n      g.event(event).end();\n    }\n  }\n\n  function dblclicked(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var t0 = this.__zoom,\n        p0 = pointer(event.changedTouches ? event.changedTouches[0] : event, this),\n        p1 = t0.invert(p0),\n        k1 = t0.k * (event.shiftKey ? 0.5 : 2),\n        t1 = constrain(translate(scale(t0, k1), p0, p1), extent.apply(this, args), translateExtent);\n\n    noevent(event);\n    if (duration > 0) select(this).transition().duration(duration).call(schedule, t1, p0, event);\n    else select(this).call(zoom.transform, t1, p0, event);\n  }\n\n  function touchstarted(event, ...args) {\n    if (!filter.apply(this, arguments)) return;\n    var touches = event.touches,\n        n = touches.length,\n        g = gesture(this, args, event.changedTouches.length === n).event(event),\n        started, i, t, p;\n\n    nopropagation(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      p = [p, this.__zoom.invert(p), t.identifier];\n      if (!g.touch0) g.touch0 = p, started = true, g.taps = 1 + !!touchstarting;\n      else if (!g.touch1 && g.touch0[2] !== p[2]) g.touch1 = p, g.taps = 0;\n    }\n\n    if (touchstarting) touchstarting = clearTimeout(touchstarting);\n\n    if (started) {\n      if (g.taps < 2) touchfirst = p[0], touchstarting = setTimeout(function() { touchstarting = null; }, touchDelay);\n      interrupt(this);\n      g.start();\n    }\n  }\n\n  function touchmoved(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t, p, l;\n\n    noevent(event);\n    for (i = 0; i < n; ++i) {\n      t = touches[i], p = pointer(t, this);\n      if (g.touch0 && g.touch0[2] === t.identifier) g.touch0[0] = p;\n      else if (g.touch1 && g.touch1[2] === t.identifier) g.touch1[0] = p;\n    }\n    t = g.that.__zoom;\n    if (g.touch1) {\n      var p0 = g.touch0[0], l0 = g.touch0[1],\n          p1 = g.touch1[0], l1 = g.touch1[1],\n          dp = (dp = p1[0] - p0[0]) * dp + (dp = p1[1] - p0[1]) * dp,\n          dl = (dl = l1[0] - l0[0]) * dl + (dl = l1[1] - l0[1]) * dl;\n      t = scale(t, Math.sqrt(dp / dl));\n      p = [(p0[0] + p1[0]) / 2, (p0[1] + p1[1]) / 2];\n      l = [(l0[0] + l1[0]) / 2, (l0[1] + l1[1]) / 2];\n    }\n    else if (g.touch0) p = g.touch0[0], l = g.touch0[1];\n    else return;\n\n    g.zoom(\"touch\", constrain(translate(t, p, l), g.extent, translateExtent));\n  }\n\n  function touchended(event, ...args) {\n    if (!this.__zooming) return;\n    var g = gesture(this, args).event(event),\n        touches = event.changedTouches,\n        n = touches.length, i, t;\n\n    nopropagation(event);\n    if (touchending) clearTimeout(touchending);\n    touchending = setTimeout(function() { touchending = null; }, touchDelay);\n    for (i = 0; i < n; ++i) {\n      t = touches[i];\n      if (g.touch0 && g.touch0[2] === t.identifier) delete g.touch0;\n      else if (g.touch1 && g.touch1[2] === t.identifier) delete g.touch1;\n    }\n    if (g.touch1 && !g.touch0) g.touch0 = g.touch1, delete g.touch1;\n    if (g.touch0) g.touch0[1] = this.__zoom.invert(g.touch0[0]);\n    else {\n      g.end();\n      // If this was a dbltap, reroute to the (optional) dblclick.zoom handler.\n      if (g.taps === 2) {\n        t = pointer(t, this);\n        if (Math.hypot(touchfirst[0] - t[0], touchfirst[1] - t[1]) < tapDistance) {\n          var p = select(this).on(\"dblclick.zoom\");\n          if (p) p.apply(this, arguments);\n        }\n      }\n    }\n  }\n\n  zoom.wheelDelta = function(_) {\n    return arguments.length ? (wheelDelta = typeof _ === \"function\" ? _ : constant(+_), zoom) : wheelDelta;\n  };\n\n  zoom.filter = function(_) {\n    return arguments.length ? (filter = typeof _ === \"function\" ? _ : constant(!!_), zoom) : filter;\n  };\n\n  zoom.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), zoom) : touchable;\n  };\n\n  zoom.extent = function(_) {\n    return arguments.length ? (extent = typeof _ === \"function\" ? _ : constant([[+_[0][0], +_[0][1]], [+_[1][0], +_[1][1]]]), zoom) : extent;\n  };\n\n  zoom.scaleExtent = function(_) {\n    return arguments.length ? (scaleExtent[0] = +_[0], scaleExtent[1] = +_[1], zoom) : [scaleExtent[0], scaleExtent[1]];\n  };\n\n  zoom.translateExtent = function(_) {\n    return arguments.length ? (translateExtent[0][0] = +_[0][0], translateExtent[1][0] = +_[1][0], translateExtent[0][1] = +_[0][1], translateExtent[1][1] = +_[1][1], zoom) : [[translateExtent[0][0], translateExtent[0][1]], [translateExtent[1][0], translateExtent[1][1]]];\n  };\n\n  zoom.constrain = function(_) {\n    return arguments.length ? (constrain = _, zoom) : constrain;\n  };\n\n  zoom.duration = function(_) {\n    return arguments.length ? (duration = +_, zoom) : duration;\n  };\n\n  zoom.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, zoom) : interpolate;\n  };\n\n  zoom.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? zoom : value;\n  };\n\n  zoom.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, zoom) : Math.sqrt(clickDistance2);\n  };\n\n  zoom.tapDistance = function(_) {\n    return arguments.length ? (tapDistance = +_, zoom) : tapDistance;\n  };\n\n  return zoom;\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,aAAa;AACpC,SAAQC,WAAW,EAAEC,UAAU,QAAO,SAAS;AAC/C,SAAQC,eAAe,QAAO,gBAAgB;AAC9C,SAAQC,MAAM,EAAEC,OAAO,QAAO,cAAc;AAC5C,SAAQC,SAAS,QAAO,eAAe;AACvC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAAQC,SAAS,EAAEC,QAAQ,QAAO,gBAAgB;AAClD,OAAOC,OAAO,IAAGC,aAAa,QAAO,cAAc;;AAEnD;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,OAAO,CAAC,CAACA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,IAAI,KAAK,OAAO,KAAK,CAACF,KAAK,CAACG,MAAM;AACpE;AAEA,SAASC,aAAaA,CAAA,EAAG;EACvB,IAAIC,CAAC,GAAG,IAAI;EACZ,IAAIA,CAAC,YAAYC,UAAU,EAAE;IAC3BD,CAAC,GAAGA,CAAC,CAACE,eAAe,IAAIF,CAAC;IAC1B,IAAIA,CAAC,CAACG,YAAY,CAAC,SAAS,CAAC,EAAE;MAC7BH,CAAC,GAAGA,CAAC,CAACI,OAAO,CAACC,OAAO;MACrB,OAAO,CAAC,CAACL,CAAC,CAACM,CAAC,EAAEN,CAAC,CAACO,CAAC,CAAC,EAAE,CAACP,CAAC,CAACM,CAAC,GAAGN,CAAC,CAACQ,KAAK,EAAER,CAAC,CAACO,CAAC,GAAGP,CAAC,CAACS,MAAM,CAAC,CAAC;IACtD;IACA,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAACT,CAAC,CAACQ,KAAK,CAACH,OAAO,CAACK,KAAK,EAAEV,CAAC,CAACS,MAAM,CAACJ,OAAO,CAACK,KAAK,CAAC,CAAC;EAClE;EACA,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAACV,CAAC,CAACW,WAAW,EAAEX,CAAC,CAACY,YAAY,CAAC,CAAC;AAClD;AAEA,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,IAAI,CAACC,MAAM,IAAIvB,QAAQ;AAChC;AAEA,SAASwB,iBAAiBA,CAACpB,KAAK,EAAE;EAChC,OAAO,CAACA,KAAK,CAACqB,MAAM,IAAIrB,KAAK,CAACsB,SAAS,KAAK,CAAC,GAAG,IAAI,GAAGtB,KAAK,CAACsB,SAAS,GAAG,CAAC,GAAG,KAAK,CAAC,IAAItB,KAAK,CAACC,OAAO,GAAG,EAAE,GAAG,CAAC,CAAC;AAChH;AAEA,SAASsB,gBAAgBA,CAAA,EAAG;EAC1B,OAAOC,SAAS,CAACC,cAAc,IAAK,cAAc,IAAI,IAAK;AAC7D;AAEA,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,MAAM,EAAEC,eAAe,EAAE;EAC5D,IAAIC,GAAG,GAAGH,SAAS,CAACI,OAAO,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7DG,GAAG,GAAGL,SAAS,CAACI,OAAO,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7DI,GAAG,GAAGN,SAAS,CAACO,OAAO,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7DM,GAAG,GAAGR,SAAS,CAACO,OAAO,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACjE,OAAOF,SAAS,CAACS,SAAS,CACxBJ,GAAG,GAAGF,GAAG,GAAG,CAACA,GAAG,GAAGE,GAAG,IAAI,CAAC,GAAGK,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,GAAG,CAAC,IAAIO,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEP,GAAG,CAAC,EAClEG,GAAG,GAAGF,GAAG,GAAG,CAACA,GAAG,GAAGE,GAAG,IAAI,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,GAAG,CAAC,IAAII,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEJ,GAAG,CACnE,CAAC;AACH;AAEA,eAAe,YAAW;EACxB,IAAIK,MAAM,GAAGzC,aAAa;IACtB6B,MAAM,GAAGxB,aAAa;IACtBqC,SAAS,GAAGf,gBAAgB;IAC5BgB,UAAU,GAAGtB,iBAAiB;IAC9BuB,SAAS,GAAGpB,gBAAgB;IAC5BqB,WAAW,GAAG,CAAC,CAAC,EAAEC,QAAQ,CAAC;IAC3BhB,eAAe,GAAG,CAAC,CAAC,CAACgB,QAAQ,EAAE,CAACA,QAAQ,CAAC,EAAE,CAACA,QAAQ,EAAEA,QAAQ,CAAC,CAAC;IAChEC,QAAQ,GAAG,GAAG;IACdC,WAAW,GAAG1D,eAAe;IAC7B2D,SAAS,GAAG9D,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC;IAC5C+D,aAAa;IACbC,UAAU;IACVC,WAAW;IACXC,UAAU,GAAG,GAAG;IAChBC,UAAU,GAAG,GAAG;IAChBC,cAAc,GAAG,CAAC;IAClBC,WAAW,GAAG,EAAE;EAEpB,SAASC,IAAIA,CAACC,SAAS,EAAE;IACvBA,SAAS,CACJC,QAAQ,CAAC,QAAQ,EAAExC,gBAAgB,CAAC,CACpCyC,EAAE,CAAC,YAAY,EAAEC,OAAO,EAAE;MAACC,OAAO,EAAE;IAAK,CAAC,CAAC,CAC3CF,EAAE,CAAC,gBAAgB,EAAEG,WAAW,CAAC,CACjCH,EAAE,CAAC,eAAe,EAAEI,UAAU,CAAC,CACjCvB,MAAM,CAACG,SAAS,CAAC,CACfgB,EAAE,CAAC,iBAAiB,EAAEK,YAAY,CAAC,CACnCL,EAAE,CAAC,gBAAgB,EAAEM,UAAU,CAAC,CAChCN,EAAE,CAAC,gCAAgC,EAAEO,UAAU,CAAC,CAChDC,KAAK,CAAC,6BAA6B,EAAE,eAAe,CAAC;EAC5D;EAEAX,IAAI,CAAC7B,SAAS,GAAG,UAASyC,UAAU,EAAEzC,SAAS,EAAE0C,KAAK,EAAErE,KAAK,EAAE;IAC7D,IAAIyD,SAAS,GAAGW,UAAU,CAACX,SAAS,GAAGW,UAAU,CAACX,SAAS,CAAC,CAAC,GAAGW,UAAU;IAC1EX,SAAS,CAACC,QAAQ,CAAC,QAAQ,EAAExC,gBAAgB,CAAC;IAC9C,IAAIkD,UAAU,KAAKX,SAAS,EAAE;MAC5Ba,QAAQ,CAACF,UAAU,EAAEzC,SAAS,EAAE0C,KAAK,EAAErE,KAAK,CAAC;IAC/C,CAAC,MAAM;MACLyD,SAAS,CAACjE,SAAS,CAAC,CAAC,CAAC+E,IAAI,CAAC,YAAW;QACpCC,OAAO,CAAC,IAAI,EAAEC,SAAS,CAAC,CACrBzE,KAAK,CAACA,KAAK,CAAC,CACZ0E,KAAK,CAAC,CAAC,CACPlB,IAAI,CAAC,IAAI,EAAE,OAAO7B,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACgD,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,GAAG9C,SAAS,CAAC,CAC1FiD,GAAG,CAAC,CAAC;MACV,CAAC,CAAC;IACJ;EACF,CAAC;EAEDpB,IAAI,CAACqB,OAAO,GAAG,UAASpB,SAAS,EAAEqB,CAAC,EAAEC,CAAC,EAAE/E,KAAK,EAAE;IAC9CwD,IAAI,CAACwB,OAAO,CAACvB,SAAS,EAAE,YAAW;MACjC,IAAIwB,EAAE,GAAG,IAAI,CAAC9D,MAAM,CAAC2D,CAAC;QAClBI,EAAE,GAAG,OAAOJ,CAAC,KAAK,UAAU,GAAGA,CAAC,CAACH,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,GAAGK,CAAC;MAC/D,OAAOG,EAAE,GAAGC,EAAE;IAChB,CAAC,EAAEH,CAAC,EAAE/E,KAAK,CAAC;EACd,CAAC;EAEDwD,IAAI,CAACwB,OAAO,GAAG,UAASvB,SAAS,EAAEqB,CAAC,EAAEC,CAAC,EAAE/E,KAAK,EAAE;IAC9CwD,IAAI,CAAC7B,SAAS,CAAC8B,SAAS,EAAE,YAAW;MACnC,IAAIpD,CAAC,GAAGuB,MAAM,CAAC+C,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;QACjCU,EAAE,GAAG,IAAI,CAAChE,MAAM;QAChBiE,EAAE,GAAGL,CAAC,IAAI,IAAI,GAAGM,QAAQ,CAAChF,CAAC,CAAC,GAAG,OAAO0E,CAAC,KAAK,UAAU,GAAGA,CAAC,CAACJ,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,GAAGM,CAAC;QACrFO,EAAE,GAAGH,EAAE,CAACI,MAAM,CAACH,EAAE,CAAC;QAClBF,EAAE,GAAG,OAAOJ,CAAC,KAAK,UAAU,GAAGA,CAAC,CAACH,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,GAAGK,CAAC;MAC/D,OAAOrC,SAAS,CAACL,SAAS,CAACoD,KAAK,CAACL,EAAE,EAAED,EAAE,CAAC,EAAEE,EAAE,EAAEE,EAAE,CAAC,EAAEjF,CAAC,EAAEwB,eAAe,CAAC;IACxE,CAAC,EAAEkD,CAAC,EAAE/E,KAAK,CAAC;EACd,CAAC;EAEDwD,IAAI,CAACiC,WAAW,GAAG,UAAShC,SAAS,EAAE9C,CAAC,EAAEC,CAAC,EAAEZ,KAAK,EAAE;IAClDwD,IAAI,CAAC7B,SAAS,CAAC8B,SAAS,EAAE,YAAW;MACnC,OAAOhB,SAAS,CAAC,IAAI,CAACtB,MAAM,CAACiB,SAAS,CACpC,OAAOzB,CAAC,KAAK,UAAU,GAAGA,CAAC,CAACgE,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,GAAG9D,CAAC,EACtD,OAAOC,CAAC,KAAK,UAAU,GAAGA,CAAC,CAAC+D,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,GAAG7D,CACvD,CAAC,EAAEgB,MAAM,CAAC+C,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,EAAE5C,eAAe,CAAC;IACpD,CAAC,EAAE,IAAI,EAAE7B,KAAK,CAAC;EACjB,CAAC;EAEDwD,IAAI,CAACkC,WAAW,GAAG,UAASjC,SAAS,EAAE9C,CAAC,EAAEC,CAAC,EAAEmE,CAAC,EAAE/E,KAAK,EAAE;IACrDwD,IAAI,CAAC7B,SAAS,CAAC8B,SAAS,EAAE,YAAW;MACnC,IAAIpD,CAAC,GAAGuB,MAAM,CAAC+C,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;QACjCkB,CAAC,GAAG,IAAI,CAACxE,MAAM;QACfiE,EAAE,GAAGL,CAAC,IAAI,IAAI,GAAGM,QAAQ,CAAChF,CAAC,CAAC,GAAG,OAAO0E,CAAC,KAAK,UAAU,GAAGA,CAAC,CAACJ,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,GAAGM,CAAC;MACzF,OAAOtC,SAAS,CAAC7C,QAAQ,CAACwC,SAAS,CAACgD,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CAACI,KAAK,CAACG,CAAC,CAACb,CAAC,CAAC,CAAC1C,SAAS,CACpE,OAAOzB,CAAC,KAAK,UAAU,GAAG,CAACA,CAAC,CAACgE,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,GAAG,CAAC9D,CAAC,EACxD,OAAOC,CAAC,KAAK,UAAU,GAAG,CAACA,CAAC,CAAC+D,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,GAAG,CAAC7D,CACzD,CAAC,EAAEP,CAAC,EAAEwB,eAAe,CAAC;IACxB,CAAC,EAAEkD,CAAC,EAAE/E,KAAK,CAAC;EACd,CAAC;EAED,SAASwF,KAAKA,CAAC7D,SAAS,EAAEmD,CAAC,EAAE;IAC3BA,CAAC,GAAGzC,IAAI,CAACE,GAAG,CAACK,WAAW,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACC,GAAG,CAACM,WAAW,CAAC,CAAC,CAAC,EAAEkC,CAAC,CAAC,CAAC;IACzD,OAAOA,CAAC,KAAKnD,SAAS,CAACmD,CAAC,GAAGnD,SAAS,GAAG,IAAIhC,SAAS,CAACmF,CAAC,EAAEnD,SAAS,CAAChB,CAAC,EAAEgB,SAAS,CAACf,CAAC,CAAC;EACnF;EAEA,SAASwB,SAASA,CAACT,SAAS,EAAEyD,EAAE,EAAEE,EAAE,EAAE;IACpC,IAAI3E,CAAC,GAAGyE,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,GAAG3D,SAAS,CAACmD,CAAC;MAAElE,CAAC,GAAGwE,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,GAAG3D,SAAS,CAACmD,CAAC;IACpE,OAAOnE,CAAC,KAAKgB,SAAS,CAAChB,CAAC,IAAIC,CAAC,KAAKe,SAAS,CAACf,CAAC,GAAGe,SAAS,GAAG,IAAIhC,SAAS,CAACgC,SAAS,CAACmD,CAAC,EAAEnE,CAAC,EAAEC,CAAC,CAAC;EAC9F;EAEA,SAASyE,QAAQA,CAACzD,MAAM,EAAE;IACxB,OAAO,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACnF;EAEA,SAAS0C,QAAQA,CAACsB,UAAU,EAAEjE,SAAS,EAAE0C,KAAK,EAAErE,KAAK,EAAE;IACrD4F,UAAU,CACLjC,EAAE,CAAC,YAAY,EAAE,YAAW;MAAEa,OAAO,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACzE,KAAK,CAACA,KAAK,CAAC,CAAC0E,KAAK,CAAC,CAAC;IAAE,CAAC,CAAC,CAC/Ef,EAAE,CAAC,yBAAyB,EAAE,YAAW;MAAEa,OAAO,CAAC,IAAI,EAAEC,SAAS,CAAC,CAACzE,KAAK,CAACA,KAAK,CAAC,CAAC4E,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC,CAC1FiB,KAAK,CAAC,MAAM,EAAE,YAAW;MACxB,IAAIC,IAAI,GAAG,IAAI;QACXC,IAAI,GAAGtB,SAAS;QAChBuB,CAAC,GAAGxB,OAAO,CAACsB,IAAI,EAAEC,IAAI,CAAC,CAAC/F,KAAK,CAACA,KAAK,CAAC;QACpCK,CAAC,GAAGuB,MAAM,CAAC+C,KAAK,CAACmB,IAAI,EAAEC,IAAI,CAAC;QAC5BhB,CAAC,GAAGV,KAAK,IAAI,IAAI,GAAGgB,QAAQ,CAAChF,CAAC,CAAC,GAAG,OAAOgE,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACM,KAAK,CAACmB,IAAI,EAAEC,IAAI,CAAC,GAAG1B,KAAK;QAC/F4B,CAAC,GAAG5D,IAAI,CAACE,GAAG,CAAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD6F,CAAC,GAAGJ,IAAI,CAAC3E,MAAM;QACfgF,CAAC,GAAG,OAAOxE,SAAS,KAAK,UAAU,GAAGA,SAAS,CAACgD,KAAK,CAACmB,IAAI,EAAEC,IAAI,CAAC,GAAGpE,SAAS;QAC7EyE,CAAC,GAAGrD,WAAW,CAACmD,CAAC,CAACX,MAAM,CAACR,CAAC,CAAC,CAACsB,MAAM,CAACJ,CAAC,GAAGC,CAAC,CAACpB,CAAC,CAAC,EAAEqB,CAAC,CAACZ,MAAM,CAACR,CAAC,CAAC,CAACsB,MAAM,CAACJ,CAAC,GAAGE,CAAC,CAACrB,CAAC,CAAC,CAAC;MAC7E,OAAO,UAASa,CAAC,EAAE;QACjB,IAAIA,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGQ,CAAC,CAAC,CAAC;QAAA,KACf;UAAE,IAAIG,CAAC,GAAGF,CAAC,CAACT,CAAC,CAAC;YAAEb,CAAC,GAAGmB,CAAC,GAAGK,CAAC,CAAC,CAAC,CAAC;UAAEX,CAAC,GAAG,IAAIhG,SAAS,CAACmF,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC,GAAGxB,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,GAAGuB,CAAC,CAAC,CAAC,CAAC,GAAGxB,CAAC,CAAC;QAAE;QAC3FkB,CAAC,CAACxC,IAAI,CAAC,IAAI,EAAEmC,CAAC,CAAC;MACjB,CAAC;IACH,CAAC,CAAC;EACR;EAEA,SAASnB,OAAOA,CAACsB,IAAI,EAAEC,IAAI,EAAEQ,KAAK,EAAE;IAClC,OAAQ,CAACA,KAAK,IAAIT,IAAI,CAACU,SAAS,IAAK,IAAIC,OAAO,CAACX,IAAI,EAAEC,IAAI,CAAC;EAC9D;EAEA,SAASU,OAAOA,CAACX,IAAI,EAAEC,IAAI,EAAE;IAC3B,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACW,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAAC/E,MAAM,GAAGA,MAAM,CAAC+C,KAAK,CAACmB,IAAI,EAAEC,IAAI,CAAC;IACtC,IAAI,CAACa,IAAI,GAAG,CAAC;EACf;EAEAH,OAAO,CAACI,SAAS,GAAG;IAClB7G,KAAK,EAAE,SAAAA,CAASA,KAAK,EAAE;MACrB,IAAIA,KAAK,EAAE,IAAI,CAAC2G,WAAW,GAAG3G,KAAK;MACnC,OAAO,IAAI;IACb,CAAC;IACD0E,KAAK,EAAE,SAAAA,CAAA,EAAW;MAChB,IAAI,EAAE,IAAI,CAACgC,MAAM,KAAK,CAAC,EAAE;QACvB,IAAI,CAACZ,IAAI,CAACU,SAAS,GAAG,IAAI;QAC1B,IAAI,CAACM,IAAI,CAAC,OAAO,CAAC;MACpB;MACA,OAAO,IAAI;IACb,CAAC;IACDtD,IAAI,EAAE,SAAAA,CAASuD,GAAG,EAAEpF,SAAS,EAAE;MAC7B,IAAI,IAAI,CAACqF,KAAK,IAAID,GAAG,KAAK,OAAO,EAAE,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGrF,SAAS,CAAC4D,MAAM,CAAC,IAAI,CAACyB,KAAK,CAAC,CAAC,CAAC,CAAC;MAClF,IAAI,IAAI,CAACC,MAAM,IAAIF,GAAG,KAAK,OAAO,EAAE,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,GAAGtF,SAAS,CAAC4D,MAAM,CAAC,IAAI,CAAC0B,MAAM,CAAC,CAAC,CAAC,CAAC;MACrF,IAAI,IAAI,CAACC,MAAM,IAAIH,GAAG,KAAK,OAAO,EAAE,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,GAAGvF,SAAS,CAAC4D,MAAM,CAAC,IAAI,CAAC2B,MAAM,CAAC,CAAC,CAAC,CAAC;MACrF,IAAI,CAACpB,IAAI,CAAC3E,MAAM,GAAGQ,SAAS;MAC5B,IAAI,CAACmF,IAAI,CAAC,MAAM,CAAC;MACjB,OAAO,IAAI;IACb,CAAC;IACDlC,GAAG,EAAE,SAAAA,CAAA,EAAW;MACd,IAAI,EAAE,IAAI,CAAC8B,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,IAAI,CAACZ,IAAI,CAACU,SAAS;QAC1B,IAAI,CAACM,IAAI,CAAC,KAAK,CAAC;MAClB;MACA,OAAO,IAAI;IACb,CAAC;IACDA,IAAI,EAAE,SAAAA,CAAS5G,IAAI,EAAE;MACnB,IAAIiH,CAAC,GAAG7H,MAAM,CAAC,IAAI,CAACwG,IAAI,CAAC,CAACsB,KAAK,CAAC,CAAC;MACjCpE,SAAS,CAACqE,IAAI,CACZnH,IAAI,EACJ,IAAI,CAAC4F,IAAI,EACT,IAAIpG,SAAS,CAACQ,IAAI,EAAE;QAClByG,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BW,MAAM,EAAE9D,IAAI;QACZtD,IAAI;QACJyB,SAAS,EAAE,IAAI,CAACmE,IAAI,CAAC3E,MAAM;QAC3BjC,QAAQ,EAAE8D;MACZ,CAAC,CAAC,EACFmE,CACF,CAAC;IACH;EACF,CAAC;EAED,SAASvD,OAAOA,CAAC5D,KAAK,EAAE,GAAG+F,IAAI,EAAE;IAC/B,IAAI,CAACvD,MAAM,CAACmC,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,EAAE;IACpC,IAAIuB,CAAC,GAAGxB,OAAO,CAAC,IAAI,EAAEuB,IAAI,CAAC,CAAC/F,KAAK,CAACA,KAAK,CAAC;MACpC2F,CAAC,GAAG,IAAI,CAACxE,MAAM;MACf2D,CAAC,GAAGzC,IAAI,CAACE,GAAG,CAACK,WAAW,CAAC,CAAC,CAAC,EAAEP,IAAI,CAACC,GAAG,CAACM,WAAW,CAAC,CAAC,CAAC,EAAE+C,CAAC,CAACb,CAAC,GAAGzC,IAAI,CAACkF,GAAG,CAAC,CAAC,EAAE7E,UAAU,CAACiC,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,CAAC,CAAC,CAAC;MAC5GM,CAAC,GAAGxF,OAAO,CAACS,KAAK,CAAC;;IAEtB;IACA;IACA,IAAIgG,CAAC,CAACwB,KAAK,EAAE;MACX,IAAIxB,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKjC,CAAC,CAAC,CAAC,CAAC,IAAIiB,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKjC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpDiB,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,GAAGrB,CAAC,CAACJ,MAAM,CAACS,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,GAAGjC,CAAC,CAAC;MACvC;MACA0C,YAAY,CAACzB,CAAC,CAACwB,KAAK,CAAC;IACvB;;IAEA;IAAA,KACK,IAAI7B,CAAC,CAACb,CAAC,KAAKA,CAAC,EAAE;;IAEpB;IAAA,KACK;MACHkB,CAAC,CAACgB,KAAK,GAAG,CAACjC,CAAC,EAAEY,CAAC,CAACJ,MAAM,CAACR,CAAC,CAAC,CAAC;MAC1BvF,SAAS,CAAC,IAAI,CAAC;MACfwG,CAAC,CAACtB,KAAK,CAAC,CAAC;IACX;IAEA7E,OAAO,CAACG,KAAK,CAAC;IACdgG,CAAC,CAACwB,KAAK,GAAGE,UAAU,CAACC,UAAU,EAAEtE,UAAU,CAAC;IAC5C2C,CAAC,CAACxC,IAAI,CAAC,OAAO,EAAEf,SAAS,CAACL,SAAS,CAACoD,KAAK,CAACG,CAAC,EAAEb,CAAC,CAAC,EAAEkB,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,EAAEhB,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEhB,CAAC,CAACpE,MAAM,EAAEC,eAAe,CAAC,CAAC;IAErG,SAAS8F,UAAUA,CAAA,EAAG;MACpB3B,CAAC,CAACwB,KAAK,GAAG,IAAI;MACdxB,CAAC,CAACpB,GAAG,CAAC,CAAC;IACT;EACF;EAEA,SAASd,WAAWA,CAAC9D,KAAK,EAAE,GAAG+F,IAAI,EAAE;IACnC,IAAI5C,WAAW,IAAI,CAACX,MAAM,CAACmC,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,EAAE;IACnD,IAAImD,aAAa,GAAG5H,KAAK,CAAC4H,aAAa;MACnC5B,CAAC,GAAGxB,OAAO,CAAC,IAAI,EAAEuB,IAAI,EAAE,IAAI,CAAC,CAAC/F,KAAK,CAACA,KAAK,CAAC;MAC1C6H,CAAC,GAAGvI,MAAM,CAACU,KAAK,CAAC8H,IAAI,CAAC,CAACnE,EAAE,CAAC,gBAAgB,EAAEoE,UAAU,EAAE,IAAI,CAAC,CAACpE,EAAE,CAAC,cAAc,EAAEqE,UAAU,EAAE,IAAI,CAAC;MAClGjD,CAAC,GAAGxF,OAAO,CAACS,KAAK,EAAE4H,aAAa,CAAC;MACjCK,EAAE,GAAGjI,KAAK,CAACkI,OAAO;MAClBC,EAAE,GAAGnI,KAAK,CAACoI,OAAO;IAEtBjJ,WAAW,CAACa,KAAK,CAAC8H,IAAI,CAAC;IACvBhI,aAAa,CAACE,KAAK,CAAC;IACpBgG,CAAC,CAACgB,KAAK,GAAG,CAACjC,CAAC,EAAE,IAAI,CAAC5D,MAAM,CAACoE,MAAM,CAACR,CAAC,CAAC,CAAC;IACpCvF,SAAS,CAAC,IAAI,CAAC;IACfwG,CAAC,CAACtB,KAAK,CAAC,CAAC;IAET,SAASqD,UAAUA,CAAC/H,KAAK,EAAE;MACzBH,OAAO,CAACG,KAAK,CAAC;MACd,IAAI,CAACgG,CAAC,CAACqC,KAAK,EAAE;QACZ,IAAIC,EAAE,GAAGtI,KAAK,CAACkI,OAAO,GAAGD,EAAE;UAAEM,EAAE,GAAGvI,KAAK,CAACoI,OAAO,GAAGD,EAAE;QACpDnC,CAAC,CAACqC,KAAK,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGjF,cAAc;MAC9C;MACA0C,CAAC,CAAChG,KAAK,CAACA,KAAK,CAAC,CACZwD,IAAI,CAAC,OAAO,EAAEf,SAAS,CAACL,SAAS,CAAC4D,CAAC,CAACF,IAAI,CAAC3E,MAAM,EAAE6E,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,GAAGzH,OAAO,CAACS,KAAK,EAAE4H,aAAa,CAAC,EAAE5B,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEhB,CAAC,CAACpE,MAAM,EAAEC,eAAe,CAAC,CAAC;IACzI;IAEA,SAASmG,UAAUA,CAAChI,KAAK,EAAE;MACzB6H,CAAC,CAAClE,EAAE,CAAC,6BAA6B,EAAE,IAAI,CAAC;MACzCvE,UAAU,CAACY,KAAK,CAAC8H,IAAI,EAAE9B,CAAC,CAACqC,KAAK,CAAC;MAC/BxI,OAAO,CAACG,KAAK,CAAC;MACdgG,CAAC,CAAChG,KAAK,CAACA,KAAK,CAAC,CAAC4E,GAAG,CAAC,CAAC;IACtB;EACF;EAEA,SAASb,UAAUA,CAAC/D,KAAK,EAAE,GAAG+F,IAAI,EAAE;IAClC,IAAI,CAACvD,MAAM,CAACmC,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,EAAE;IACpC,IAAIU,EAAE,GAAG,IAAI,CAAChE,MAAM;MAChBiE,EAAE,GAAG7F,OAAO,CAACS,KAAK,CAACwI,cAAc,GAAGxI,KAAK,CAACwI,cAAc,CAAC,CAAC,CAAC,GAAGxI,KAAK,EAAE,IAAI,CAAC;MAC1EsF,EAAE,GAAGH,EAAE,CAACI,MAAM,CAACH,EAAE,CAAC;MAClBF,EAAE,GAAGC,EAAE,CAACL,CAAC,IAAI9E,KAAK,CAACyI,QAAQ,GAAG,GAAG,GAAG,CAAC,CAAC;MACtCC,EAAE,GAAGjG,SAAS,CAACL,SAAS,CAACoD,KAAK,CAACL,EAAE,EAAED,EAAE,CAAC,EAAEE,EAAE,EAAEE,EAAE,CAAC,EAAE1D,MAAM,CAAC+C,KAAK,CAAC,IAAI,EAAEoB,IAAI,CAAC,EAAElE,eAAe,CAAC;IAE/FhC,OAAO,CAACG,KAAK,CAAC;IACd,IAAI8C,QAAQ,GAAG,CAAC,EAAExD,MAAM,CAAC,IAAI,CAAC,CAACsG,UAAU,CAAC,CAAC,CAAC9C,QAAQ,CAACA,QAAQ,CAAC,CAACuE,IAAI,CAAC/C,QAAQ,EAAEoE,EAAE,EAAEtD,EAAE,EAAEpF,KAAK,CAAC,CAAC,KACxFV,MAAM,CAAC,IAAI,CAAC,CAAC+H,IAAI,CAAC7D,IAAI,CAAC7B,SAAS,EAAE+G,EAAE,EAAEtD,EAAE,EAAEpF,KAAK,CAAC;EACvD;EAEA,SAASgE,YAAYA,CAAChE,KAAK,EAAE,GAAG+F,IAAI,EAAE;IACpC,IAAI,CAACvD,MAAM,CAACmC,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC,EAAE;IACpC,IAAIkE,OAAO,GAAG3I,KAAK,CAAC2I,OAAO;MACvBC,CAAC,GAAGD,OAAO,CAACE,MAAM;MAClB7C,CAAC,GAAGxB,OAAO,CAAC,IAAI,EAAEuB,IAAI,EAAE/F,KAAK,CAACwI,cAAc,CAACK,MAAM,KAAKD,CAAC,CAAC,CAAC5I,KAAK,CAACA,KAAK,CAAC;MACvE8I,OAAO;MAAE1C,CAAC;MAAET,CAAC;MAAEZ,CAAC;IAEpBjF,aAAa,CAACE,KAAK,CAAC;IACpB,KAAKoG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,CAAC,EAAE,EAAExC,CAAC,EAAE;MACtBT,CAAC,GAAGgD,OAAO,CAACvC,CAAC,CAAC,EAAErB,CAAC,GAAGxF,OAAO,CAACoG,CAAC,EAAE,IAAI,CAAC;MACpCZ,CAAC,GAAG,CAACA,CAAC,EAAE,IAAI,CAAC5D,MAAM,CAACoE,MAAM,CAACR,CAAC,CAAC,EAAEY,CAAC,CAACoD,UAAU,CAAC;MAC5C,IAAI,CAAC/C,CAAC,CAACiB,MAAM,EAAEjB,CAAC,CAACiB,MAAM,GAAGlC,CAAC,EAAE+D,OAAO,GAAG,IAAI,EAAE9C,CAAC,CAACY,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC3D,aAAa,CAAC,KACrE,IAAI,CAAC+C,CAAC,CAACkB,MAAM,IAAIlB,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,KAAKlC,CAAC,CAAC,CAAC,CAAC,EAAEiB,CAAC,CAACkB,MAAM,GAAGnC,CAAC,EAAEiB,CAAC,CAACY,IAAI,GAAG,CAAC;IACtE;IAEA,IAAI3D,aAAa,EAAEA,aAAa,GAAGwE,YAAY,CAACxE,aAAa,CAAC;IAE9D,IAAI6F,OAAO,EAAE;MACX,IAAI9C,CAAC,CAACY,IAAI,GAAG,CAAC,EAAE1D,UAAU,GAAG6B,CAAC,CAAC,CAAC,CAAC,EAAE9B,aAAa,GAAGyE,UAAU,CAAC,YAAW;QAAEzE,aAAa,GAAG,IAAI;MAAE,CAAC,EAAEG,UAAU,CAAC;MAC/G5D,SAAS,CAAC,IAAI,CAAC;MACfwG,CAAC,CAACtB,KAAK,CAAC,CAAC;IACX;EACF;EAEA,SAAST,UAAUA,CAACjE,KAAK,EAAE,GAAG+F,IAAI,EAAE;IAClC,IAAI,CAAC,IAAI,CAACS,SAAS,EAAE;IACrB,IAAIR,CAAC,GAAGxB,OAAO,CAAC,IAAI,EAAEuB,IAAI,CAAC,CAAC/F,KAAK,CAACA,KAAK,CAAC;MACpC2I,OAAO,GAAG3I,KAAK,CAACwI,cAAc;MAC9BI,CAAC,GAAGD,OAAO,CAACE,MAAM;MAAEzC,CAAC;MAAET,CAAC;MAAEZ,CAAC;MAAEuB,CAAC;IAElCzG,OAAO,CAACG,KAAK,CAAC;IACd,KAAKoG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,CAAC,EAAE,EAAExC,CAAC,EAAE;MACtBT,CAAC,GAAGgD,OAAO,CAACvC,CAAC,CAAC,EAAErB,CAAC,GAAGxF,OAAO,CAACoG,CAAC,EAAE,IAAI,CAAC;MACpC,IAAIK,CAAC,CAACiB,MAAM,IAAIjB,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,KAAKtB,CAAC,CAACoD,UAAU,EAAE/C,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,GAAGlC,CAAC,CAAC,KACzD,IAAIiB,CAAC,CAACkB,MAAM,IAAIlB,CAAC,CAACkB,MAAM,CAAC,CAAC,CAAC,KAAKvB,CAAC,CAACoD,UAAU,EAAE/C,CAAC,CAACkB,MAAM,CAAC,CAAC,CAAC,GAAGnC,CAAC;IACpE;IACAY,CAAC,GAAGK,CAAC,CAACF,IAAI,CAAC3E,MAAM;IACjB,IAAI6E,CAAC,CAACkB,MAAM,EAAE;MACZ,IAAI9B,EAAE,GAAGY,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC;QAAE+B,EAAE,GAAGhD,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC;QAClC3B,EAAE,GAAGU,CAAC,CAACkB,MAAM,CAAC,CAAC,CAAC;QAAE+B,EAAE,GAAGjD,CAAC,CAACkB,MAAM,CAAC,CAAC,CAAC;QAClCgC,EAAE,GAAG,CAACA,EAAE,GAAG5D,EAAE,CAAC,CAAC,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,IAAI8D,EAAE,GAAG,CAACA,EAAE,GAAG5D,EAAE,CAAC,CAAC,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,IAAI8D,EAAE;QAC1DC,EAAE,GAAG,CAACA,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,IAAIG,EAAE,GAAG,CAACA,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,IAAIG,EAAE;MAC9DxD,CAAC,GAAGH,KAAK,CAACG,CAAC,EAAEtD,IAAI,CAAC+G,IAAI,CAACF,EAAE,GAAGC,EAAE,CAAC,CAAC;MAChCpE,CAAC,GAAG,CAAC,CAACK,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAACF,EAAE,CAAC,CAAC,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;MAC9CgB,CAAC,GAAG,CAAC,CAAC0C,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAACD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC,MACI,IAAIjD,CAAC,CAACiB,MAAM,EAAElC,CAAC,GAAGiB,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,EAAEX,CAAC,GAAGN,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,CAAC,KAC/C;IAELjB,CAAC,CAACxC,IAAI,CAAC,OAAO,EAAEf,SAAS,CAACL,SAAS,CAACuD,CAAC,EAAEZ,CAAC,EAAEuB,CAAC,CAAC,EAAEN,CAAC,CAACpE,MAAM,EAAEC,eAAe,CAAC,CAAC;EAC3E;EAEA,SAASqC,UAAUA,CAAClE,KAAK,EAAE,GAAG+F,IAAI,EAAE;IAClC,IAAI,CAAC,IAAI,CAACS,SAAS,EAAE;IACrB,IAAIR,CAAC,GAAGxB,OAAO,CAAC,IAAI,EAAEuB,IAAI,CAAC,CAAC/F,KAAK,CAACA,KAAK,CAAC;MACpC2I,OAAO,GAAG3I,KAAK,CAACwI,cAAc;MAC9BI,CAAC,GAAGD,OAAO,CAACE,MAAM;MAAEzC,CAAC;MAAET,CAAC;IAE5B7F,aAAa,CAACE,KAAK,CAAC;IACpB,IAAImD,WAAW,EAAEsE,YAAY,CAACtE,WAAW,CAAC;IAC1CA,WAAW,GAAGuE,UAAU,CAAC,YAAW;MAAEvE,WAAW,GAAG,IAAI;IAAE,CAAC,EAAEC,UAAU,CAAC;IACxE,KAAKgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwC,CAAC,EAAE,EAAExC,CAAC,EAAE;MACtBT,CAAC,GAAGgD,OAAO,CAACvC,CAAC,CAAC;MACd,IAAIJ,CAAC,CAACiB,MAAM,IAAIjB,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,KAAKtB,CAAC,CAACoD,UAAU,EAAE,OAAO/C,CAAC,CAACiB,MAAM,CAAC,KACzD,IAAIjB,CAAC,CAACkB,MAAM,IAAIlB,CAAC,CAACkB,MAAM,CAAC,CAAC,CAAC,KAAKvB,CAAC,CAACoD,UAAU,EAAE,OAAO/C,CAAC,CAACkB,MAAM;IACpE;IACA,IAAIlB,CAAC,CAACkB,MAAM,IAAI,CAAClB,CAAC,CAACiB,MAAM,EAAEjB,CAAC,CAACiB,MAAM,GAAGjB,CAAC,CAACkB,MAAM,EAAE,OAAOlB,CAAC,CAACkB,MAAM;IAC/D,IAAIlB,CAAC,CAACiB,MAAM,EAAEjB,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC9F,MAAM,CAACoE,MAAM,CAACS,CAAC,CAACiB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KACvD;MACHjB,CAAC,CAACpB,GAAG,CAAC,CAAC;MACP;MACA,IAAIoB,CAAC,CAACY,IAAI,KAAK,CAAC,EAAE;QAChBjB,CAAC,GAAGpG,OAAO,CAACoG,CAAC,EAAE,IAAI,CAAC;QACpB,IAAItD,IAAI,CAACgH,KAAK,CAACnG,UAAU,CAAC,CAAC,CAAC,GAAGyC,CAAC,CAAC,CAAC,CAAC,EAAEzC,UAAU,CAAC,CAAC,CAAC,GAAGyC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGpC,WAAW,EAAE;UACxE,IAAIwB,CAAC,GAAGzF,MAAM,CAAC,IAAI,CAAC,CAACqE,EAAE,CAAC,eAAe,CAAC;UACxC,IAAIoB,CAAC,EAAEA,CAAC,CAACJ,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;QACjC;MACF;IACF;EACF;EAEAjB,IAAI,CAACd,UAAU,GAAG,UAAS4G,CAAC,EAAE;IAC5B,OAAO7E,SAAS,CAACoE,MAAM,IAAInG,UAAU,GAAG,OAAO4G,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG7J,QAAQ,CAAC,CAAC6J,CAAC,CAAC,EAAE9F,IAAI,IAAId,UAAU;EACxG,CAAC;EAEDc,IAAI,CAAChB,MAAM,GAAG,UAAS8G,CAAC,EAAE;IACxB,OAAO7E,SAAS,CAACoE,MAAM,IAAIrG,MAAM,GAAG,OAAO8G,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG7J,QAAQ,CAAC,CAAC,CAAC6J,CAAC,CAAC,EAAE9F,IAAI,IAAIhB,MAAM;EACjG,CAAC;EAEDgB,IAAI,CAACb,SAAS,GAAG,UAAS2G,CAAC,EAAE;IAC3B,OAAO7E,SAAS,CAACoE,MAAM,IAAIlG,SAAS,GAAG,OAAO2G,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG7J,QAAQ,CAAC,CAAC,CAAC6J,CAAC,CAAC,EAAE9F,IAAI,IAAIb,SAAS;EACvG,CAAC;EAEDa,IAAI,CAAC5B,MAAM,GAAG,UAAS0H,CAAC,EAAE;IACxB,OAAO7E,SAAS,CAACoE,MAAM,IAAIjH,MAAM,GAAG,OAAO0H,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG7J,QAAQ,CAAC,CAAC,CAAC,CAAC6J,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE9F,IAAI,IAAI5B,MAAM;EAC1I,CAAC;EAED4B,IAAI,CAACZ,WAAW,GAAG,UAAS0G,CAAC,EAAE;IAC7B,OAAO7E,SAAS,CAACoE,MAAM,IAAIjG,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC0G,CAAC,CAAC,CAAC,CAAC,EAAE1G,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC0G,CAAC,CAAC,CAAC,CAAC,EAAE9F,IAAI,IAAI,CAACZ,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC;EACrH,CAAC;EAEDY,IAAI,CAAC3B,eAAe,GAAG,UAASyH,CAAC,EAAE;IACjC,OAAO7E,SAAS,CAACoE,MAAM,IAAIhH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEzH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEzH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEzH,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACyH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE9F,IAAI,IAAI,CAAC,CAAC3B,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7Q,CAAC;EAED2B,IAAI,CAACf,SAAS,GAAG,UAAS6G,CAAC,EAAE;IAC3B,OAAO7E,SAAS,CAACoE,MAAM,IAAIpG,SAAS,GAAG6G,CAAC,EAAE9F,IAAI,IAAIf,SAAS;EAC7D,CAAC;EAEDe,IAAI,CAACV,QAAQ,GAAG,UAASwG,CAAC,EAAE;IAC1B,OAAO7E,SAAS,CAACoE,MAAM,IAAI/F,QAAQ,GAAG,CAACwG,CAAC,EAAE9F,IAAI,IAAIV,QAAQ;EAC5D,CAAC;EAEDU,IAAI,CAACT,WAAW,GAAG,UAASuG,CAAC,EAAE;IAC7B,OAAO7E,SAAS,CAACoE,MAAM,IAAI9F,WAAW,GAAGuG,CAAC,EAAE9F,IAAI,IAAIT,WAAW;EACjE,CAAC;EAEDS,IAAI,CAACG,EAAE,GAAG,YAAW;IACnB,IAAI5C,KAAK,GAAGiC,SAAS,CAACW,EAAE,CAACgB,KAAK,CAAC3B,SAAS,EAAEyB,SAAS,CAAC;IACpD,OAAO1D,KAAK,KAAKiC,SAAS,GAAGQ,IAAI,GAAGzC,KAAK;EAC3C,CAAC;EAEDyC,IAAI,CAAC+F,aAAa,GAAG,UAASD,CAAC,EAAE;IAC/B,OAAO7E,SAAS,CAACoE,MAAM,IAAIvF,cAAc,GAAG,CAACgG,CAAC,GAAG,CAACA,CAAC,IAAIA,CAAC,EAAE9F,IAAI,IAAInB,IAAI,CAAC+G,IAAI,CAAC9F,cAAc,CAAC;EAC7F,CAAC;EAEDE,IAAI,CAACD,WAAW,GAAG,UAAS+F,CAAC,EAAE;IAC7B,OAAO7E,SAAS,CAACoE,MAAM,IAAItF,WAAW,GAAG,CAAC+F,CAAC,EAAE9F,IAAI,IAAID,WAAW;EAClE,CAAC;EAED,OAAOC,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}