{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Goal = createLucideIcon(\"Goal\", [[\"path\", {\n  d: \"M12 13V2l8 4-8 4\",\n  key: \"5wlwwj\"\n}], [\"path\", {\n  d: \"M20.55 10.23A9 9 0 1 1 8 4.94\",\n  key: \"5988i3\"\n}], [\"path\", {\n  d: \"M8 10a5 5 0 1 0 8.9 2.02\",\n  key: \"1hq7ot\"\n}]]);\nexport { Goal as default };", "map": {"version": 3, "names": ["Goal", "createLucideIcon", "d", "key"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\goal.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Goal\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTNWMmw4IDQtOCA0IiAvPgogIDxwYXRoIGQ9Ik0yMC41NSAxMC4yM0E5IDkgMCAxIDEgOCA0Ljk0IiAvPgogIDxwYXRoIGQ9Ik04IDEwYTUgNSAwIDEgMCA4LjkgMi4wMiIgLz4KPC9zdmc+) - https://lucide.dev/icons/goal\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Goal = createLucideIcon('Goal', [\n  ['path', { d: 'M12 13V2l8 4-8 4', key: '5wlwwj' }],\n  ['path', { d: 'M20.55 10.23A9 9 0 1 1 8 4.94', key: '5988i3' }],\n  ['path', { d: 'M8 10a5 5 0 1 0 8.9 2.02', key: '1hq7ot' }],\n]);\n\nexport default Goal;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,+BAAiC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}