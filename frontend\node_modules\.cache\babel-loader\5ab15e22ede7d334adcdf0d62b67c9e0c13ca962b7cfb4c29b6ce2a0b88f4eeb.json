{"ast": null, "code": "import sourceEvent from \"./sourceEvent.js\";\nexport default function (event, node) {\n  event = sourceEvent(event);\n  if (node === undefined) node = event.currentTarget;\n  if (node) {\n    var svg = node.ownerSVGElement || node;\n    if (svg.createSVGPoint) {\n      var point = svg.createSVGPoint();\n      point.x = event.clientX, point.y = event.clientY;\n      point = point.matrixTransform(node.getScreenCTM().inverse());\n      return [point.x, point.y];\n    }\n    if (node.getBoundingClientRect) {\n      var rect = node.getBoundingClientRect();\n      return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n    }\n  }\n  return [event.pageX, event.pageY];\n}", "map": {"version": 3, "names": ["sourceEvent", "event", "node", "undefined", "currentTarget", "svg", "ownerSVGElement", "createSVGPoint", "point", "x", "clientX", "y", "clientY", "matrixTransform", "getScreenCTM", "inverse", "getBoundingClientRect", "rect", "left", "clientLeft", "top", "clientTop", "pageX", "pageY"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/pointer.js"], "sourcesContent": ["import sourceEvent from \"./sourceEvent.js\";\n\nexport default function(event, node) {\n  event = sourceEvent(event);\n  if (node === undefined) node = event.currentTarget;\n  if (node) {\n    var svg = node.ownerSVGElement || node;\n    if (svg.createSVGPoint) {\n      var point = svg.createSVGPoint();\n      point.x = event.clientX, point.y = event.clientY;\n      point = point.matrixTransform(node.getScreenCTM().inverse());\n      return [point.x, point.y];\n    }\n    if (node.getBoundingClientRect) {\n      var rect = node.getBoundingClientRect();\n      return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n    }\n  }\n  return [event.pageX, event.pageY];\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,kBAAkB;AAE1C,eAAe,UAASC,KAAK,EAAEC,IAAI,EAAE;EACnCD,KAAK,GAAGD,WAAW,CAACC,KAAK,CAAC;EAC1B,IAAIC,IAAI,KAAKC,SAAS,EAAED,IAAI,GAAGD,KAAK,CAACG,aAAa;EAClD,IAAIF,IAAI,EAAE;IACR,IAAIG,GAAG,GAAGH,IAAI,CAACI,eAAe,IAAIJ,IAAI;IACtC,IAAIG,GAAG,CAACE,cAAc,EAAE;MACtB,IAAIC,KAAK,GAAGH,GAAG,CAACE,cAAc,CAAC,CAAC;MAChCC,KAAK,CAACC,CAAC,GAAGR,KAAK,CAACS,OAAO,EAAEF,KAAK,CAACG,CAAC,GAAGV,KAAK,CAACW,OAAO;MAChDJ,KAAK,GAAGA,KAAK,CAACK,eAAe,CAACX,IAAI,CAACY,YAAY,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;MAC5D,OAAO,CAACP,KAAK,CAACC,CAAC,EAAED,KAAK,CAACG,CAAC,CAAC;IAC3B;IACA,IAAIT,IAAI,CAACc,qBAAqB,EAAE;MAC9B,IAAIC,IAAI,GAAGf,IAAI,CAACc,qBAAqB,CAAC,CAAC;MACvC,OAAO,CAACf,KAAK,CAACS,OAAO,GAAGO,IAAI,CAACC,IAAI,GAAGhB,IAAI,CAACiB,UAAU,EAAElB,KAAK,CAACW,OAAO,GAAGK,IAAI,CAACG,GAAG,GAAGlB,IAAI,CAACmB,SAAS,CAAC;IACjG;EACF;EACA,OAAO,CAACpB,KAAK,CAACqB,KAAK,EAAErB,KAAK,CAACsB,KAAK,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}