import React, { useState, useEffect } from 'react'
import { X, Upload, <PERSON><PERSON><PERSON>, <PERSON>, FileOutput } from 'lucide-react'
import { Button } from '../ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card'
import useWorkflowStore from '../../store/workflowStore'

const NodeConfigPanel = ({ node, onClose }) => {
  const { updateWorkflowNodes } = useWorkflowStore()
  const [config, setConfig] = useState(node.data.config || {})

  useEffect(() => {
    setConfig(node.data.config || {})
  }, [node])

  const handleConfigChange = (key, value) => {
    const newConfig = { ...config, [key]: value }
    setConfig(newConfig)
    
    // Update the node in the store
    const { currentWorkflow } = useWorkflowStore.getState()
    const updatedNodes = currentWorkflow.nodes.map(n => 
      n.id === node.id 
        ? { ...n, data: { ...n.data, config: newConfig } }
        : n
    )
    updateWorkflowNodes(updatedNodes)
  }

  const getNodeIcon = () => {
    switch (node.type) {
      case 'user_query': return <Settings className="w-5 h-5" />
      case 'knowledge_base': return <Upload className="w-5 h-5" />
      case 'llm_engine': return <Brain className="w-5 h-5" />
      case 'output': return <FileOutput className="w-5 h-5" />
      default: return <Settings className="w-5 h-5" />
    }
  }

  const getNodeColor = () => {
    switch (node.type) {
      case 'user_query': return 'text-blue-600'
      case 'knowledge_base': return 'text-green-600'
      case 'llm_engine': return 'text-purple-600'
      case 'output': return 'text-gray-600'
      default: return 'text-gray-600'
    }
  }

  const renderUserQueryConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Query Input
        </label>
        <textarea
          value={config.query || ''}
          onChange={(e) => handleConfigChange('query', e.target.value)}
          placeholder="Enter your question or leave empty for dynamic input..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          rows={3}
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Placeholder Text
        </label>
        <input
          type="text"
          value={config.placeholder || ''}
          onChange={(e) => handleConfigChange('placeholder', e.target.value)}
          placeholder="Enter your question..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
    </div>
  )

  const renderKnowledgeBaseConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Document Upload
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-600 mb-2">
            Drag and drop PDF files here, or click to browse
          </p>
          <Button variant="outline" size="sm">
            Choose Files
          </Button>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Max Results
        </label>
        <input
          type="number"
          value={config.maxResults || 5}
          onChange={(e) => handleConfigChange('maxResults', parseInt(e.target.value))}
          min="1"
          max="20"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Similarity Threshold
        </label>
        <input
          type="range"
          value={config.similarityThreshold || 0.7}
          onChange={(e) => handleConfigChange('similarityThreshold', parseFloat(e.target.value))}
          min="0"
          max="1"
          step="0.1"
          className="w-full"
        />
        <div className="text-xs text-gray-500 mt-1">
          Current: {config.similarityThreshold || 0.7}
        </div>
      </div>
    </div>
  )

  const renderLLMEngineConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Model
        </label>
        <select
          value={config.model || 'gpt-3.5-turbo'}
          onChange={(e) => handleConfigChange('model', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
          <option value="gpt-4">GPT-4</option>
          <option value="gpt-4-turbo">GPT-4 Turbo</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          System Prompt
        </label>
        <textarea
          value={config.systemPrompt || ''}
          onChange={(e) => handleConfigChange('systemPrompt', e.target.value)}
          placeholder="You are a helpful AI assistant..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          rows={4}
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Temperature: {config.temperature || 0.7}
        </label>
        <input
          type="range"
          value={config.temperature || 0.7}
          onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
          min="0"
          max="2"
          step="0.1"
          className="w-full"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Max Tokens
        </label>
        <input
          type="number"
          value={config.maxTokens || 1000}
          onChange={(e) => handleConfigChange('maxTokens', parseInt(e.target.value))}
          min="1"
          max="4000"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        />
      </div>
    </div>
  )

  const renderOutputConfig = () => (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Output Format
        </label>
        <select
          value={config.format || 'text'}
          onChange={(e) => handleConfigChange('format', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-gray-500"
        >
          <option value="text">Plain Text</option>
          <option value="markdown">Markdown</option>
          <option value="json">JSON</option>
        </select>
      </div>
      
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="showSources"
          checked={config.showSources || false}
          onChange={(e) => handleConfigChange('showSources', e.target.checked)}
          className="rounded border-gray-300 text-gray-600 focus:ring-gray-500"
        />
        <label htmlFor="showSources" className="text-sm text-gray-700">
          Show source references
        </label>
      </div>
      
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="enableCopy"
          checked={config.enableCopy || true}
          onChange={(e) => handleConfigChange('enableCopy', e.target.checked)}
          className="rounded border-gray-300 text-gray-600 focus:ring-gray-500"
        />
        <label htmlFor="enableCopy" className="text-sm text-gray-700">
          Enable copy to clipboard
        </label>
      </div>
    </div>
  )

  const renderConfigContent = () => {
    switch (node.type) {
      case 'user_query': return renderUserQueryConfig()
      case 'knowledge_base': return renderKnowledgeBaseConfig()
      case 'llm_engine': return renderLLMEngineConfig()
      case 'output': return renderOutputConfig()
      default: return <div>No configuration available</div>
    }
  }

  return (
    <div className="w-80 bg-white border-l border-gray-200 h-full overflow-y-auto">
      <Card className="h-full rounded-none border-0">
        <CardHeader className="border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className={`${getNodeColor()}`}>
                {getNodeIcon()}
              </div>
              <CardTitle className="text-lg">
                {node.data.label || node.type.replace('_', ' ')}
              </CardTitle>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="p-6">
          {renderConfigContent()}
        </CardContent>
      </Card>
    </div>
  )
}

export default NodeConfigPanel
