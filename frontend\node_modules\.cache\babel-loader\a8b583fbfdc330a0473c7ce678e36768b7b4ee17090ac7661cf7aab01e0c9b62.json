{"ast": null, "code": "import { interpolateTransformCss as interpolateTransform } from \"d3-interpolate\";\nimport { style } from \"d3-selection\";\nimport { set } from \"./schedule.js\";\nimport { tweenValue } from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\nfunction styleNull(name, interpolate) {\n  var string00, string10, interpolate0;\n  return function () {\n    var string0 = style(this, name),\n      string1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null : string0 === string00 && string1 === string10 ? interpolate0 : interpolate0 = interpolate(string00 = string0, string10 = string1);\n  };\n}\nfunction styleRemove(name) {\n  return function () {\n    this.style.removeProperty(name);\n  };\n}\nfunction styleConstant(name, interpolate, value1) {\n  var string00,\n    string1 = value1 + \"\",\n    interpolate0;\n  return function () {\n    var string0 = style(this, name);\n    return string0 === string1 ? null : string0 === string00 ? interpolate0 : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\nfunction styleFunction(name, interpolate, value) {\n  var string00, string10, interpolate0;\n  return function () {\n    var string0 = style(this, name),\n      value1 = value(this),\n      string1 = value1 + \"\";\n    if (value1 == null) string1 = value1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null : string0 === string00 && string1 === string10 ? interpolate0 : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\nfunction styleMaybeRemove(id, name) {\n  var on0,\n    on1,\n    listener0,\n    key = \"style.\" + name,\n    event = \"end.\" + key,\n    remove;\n  return function () {\n    var schedule = set(this, id),\n      on = schedule.on,\n      listener = schedule.value[key] == null ? remove || (remove = styleRemove(name)) : undefined;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0 || listener0 !== listener) (on1 = (on0 = on).copy()).on(event, listener0 = listener);\n    schedule.on = on1;\n  };\n}\nexport default function (name, value, priority) {\n  var i = (name += \"\") === \"transform\" ? interpolateTransform : interpolate;\n  return value == null ? this.styleTween(name, styleNull(name, i)).on(\"end.style.\" + name, styleRemove(name)) : typeof value === \"function\" ? this.styleTween(name, styleFunction(name, i, tweenValue(this, \"style.\" + name, value))).each(styleMaybeRemove(this._id, name)) : this.styleTween(name, styleConstant(name, i, value), priority).on(\"end.style.\" + name, null);\n}", "map": {"version": 3, "names": ["interpolateTransformCss", "interpolateTransform", "style", "set", "tweenValue", "interpolate", "styleNull", "name", "string00", "string10", "interpolate0", "string0", "string1", "removeProperty", "styleRemove", "styleConstant", "value1", "styleFunction", "value", "styleMaybeRemove", "id", "on0", "on1", "listener0", "key", "event", "remove", "schedule", "on", "listener", "undefined", "copy", "priority", "i", "styleTween", "each", "_id"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-transition/src/transition/style.js"], "sourcesContent": ["import {interpolateTransformCss as interpolateTransform} from \"d3-interpolate\";\nimport {style} from \"d3-selection\";\nimport {set} from \"./schedule.js\";\nimport {tweenValue} from \"./tween.js\";\nimport interpolate from \"./interpolate.js\";\n\nfunction styleNull(name, interpolate) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        string1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, string10 = string1);\n  };\n}\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, interpolate, value1) {\n  var string00,\n      string1 = value1 + \"\",\n      interpolate0;\n  return function() {\n    var string0 = style(this, name);\n    return string0 === string1 ? null\n        : string0 === string00 ? interpolate0\n        : interpolate0 = interpolate(string00 = string0, value1);\n  };\n}\n\nfunction styleFunction(name, interpolate, value) {\n  var string00,\n      string10,\n      interpolate0;\n  return function() {\n    var string0 = style(this, name),\n        value1 = value(this),\n        string1 = value1 + \"\";\n    if (value1 == null) string1 = value1 = (this.style.removeProperty(name), style(this, name));\n    return string0 === string1 ? null\n        : string0 === string00 && string1 === string10 ? interpolate0\n        : (string10 = string1, interpolate0 = interpolate(string00 = string0, value1));\n  };\n}\n\nfunction styleMaybeRemove(id, name) {\n  var on0, on1, listener0, key = \"style.\" + name, event = \"end.\" + key, remove;\n  return function() {\n    var schedule = set(this, id),\n        on = schedule.on,\n        listener = schedule.value[key] == null ? remove || (remove = styleRemove(name)) : undefined;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0 || listener0 !== listener) (on1 = (on0 = on).copy()).on(event, listener0 = listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, value, priority) {\n  var i = (name += \"\") === \"transform\" ? interpolateTransform : interpolate;\n  return value == null ? this\n      .styleTween(name, styleNull(name, i))\n      .on(\"end.style.\" + name, styleRemove(name))\n    : typeof value === \"function\" ? this\n      .styleTween(name, styleFunction(name, i, tweenValue(this, \"style.\" + name, value)))\n      .each(styleMaybeRemove(this._id, name))\n    : this\n      .styleTween(name, styleConstant(name, i, value), priority)\n      .on(\"end.style.\" + name, null);\n}\n"], "mappings": "AAAA,SAAQA,uBAAuB,IAAIC,oBAAoB,QAAO,gBAAgB;AAC9E,SAAQC,KAAK,QAAO,cAAc;AAClC,SAAQC,GAAG,QAAO,eAAe;AACjC,SAAQC,UAAU,QAAO,YAAY;AACrC,OAAOC,WAAW,MAAM,kBAAkB;AAE1C,SAASC,SAASA,CAACC,IAAI,EAAEF,WAAW,EAAE;EACpC,IAAIG,QAAQ,EACRC,QAAQ,EACRC,YAAY;EAChB,OAAO,YAAW;IAChB,IAAIC,OAAO,GAAGT,KAAK,CAAC,IAAI,EAAEK,IAAI,CAAC;MAC3BK,OAAO,IAAI,IAAI,CAACV,KAAK,CAACW,cAAc,CAACN,IAAI,CAAC,EAAEL,KAAK,CAAC,IAAI,EAAEK,IAAI,CAAC,CAAC;IAClE,OAAOI,OAAO,KAAKC,OAAO,GAAG,IAAI,GAC3BD,OAAO,KAAKH,QAAQ,IAAII,OAAO,KAAKH,QAAQ,GAAGC,YAAY,GAC3DA,YAAY,GAAGL,WAAW,CAACG,QAAQ,GAAGG,OAAO,EAAEF,QAAQ,GAAGG,OAAO,CAAC;EAC1E,CAAC;AACH;AAEA,SAASE,WAAWA,CAACP,IAAI,EAAE;EACzB,OAAO,YAAW;IAChB,IAAI,CAACL,KAAK,CAACW,cAAc,CAACN,IAAI,CAAC;EACjC,CAAC;AACH;AAEA,SAASQ,aAAaA,CAACR,IAAI,EAAEF,WAAW,EAAEW,MAAM,EAAE;EAChD,IAAIR,QAAQ;IACRI,OAAO,GAAGI,MAAM,GAAG,EAAE;IACrBN,YAAY;EAChB,OAAO,YAAW;IAChB,IAAIC,OAAO,GAAGT,KAAK,CAAC,IAAI,EAAEK,IAAI,CAAC;IAC/B,OAAOI,OAAO,KAAKC,OAAO,GAAG,IAAI,GAC3BD,OAAO,KAAKH,QAAQ,GAAGE,YAAY,GACnCA,YAAY,GAAGL,WAAW,CAACG,QAAQ,GAAGG,OAAO,EAAEK,MAAM,CAAC;EAC9D,CAAC;AACH;AAEA,SAASC,aAAaA,CAACV,IAAI,EAAEF,WAAW,EAAEa,KAAK,EAAE;EAC/C,IAAIV,QAAQ,EACRC,QAAQ,EACRC,YAAY;EAChB,OAAO,YAAW;IAChB,IAAIC,OAAO,GAAGT,KAAK,CAAC,IAAI,EAAEK,IAAI,CAAC;MAC3BS,MAAM,GAAGE,KAAK,CAAC,IAAI,CAAC;MACpBN,OAAO,GAAGI,MAAM,GAAG,EAAE;IACzB,IAAIA,MAAM,IAAI,IAAI,EAAEJ,OAAO,GAAGI,MAAM,IAAI,IAAI,CAACd,KAAK,CAACW,cAAc,CAACN,IAAI,CAAC,EAAEL,KAAK,CAAC,IAAI,EAAEK,IAAI,CAAC,CAAC;IAC3F,OAAOI,OAAO,KAAKC,OAAO,GAAG,IAAI,GAC3BD,OAAO,KAAKH,QAAQ,IAAII,OAAO,KAAKH,QAAQ,GAAGC,YAAY,IAC1DD,QAAQ,GAAGG,OAAO,EAAEF,YAAY,GAAGL,WAAW,CAACG,QAAQ,GAAGG,OAAO,EAAEK,MAAM,CAAC,CAAC;EACpF,CAAC;AACH;AAEA,SAASG,gBAAgBA,CAACC,EAAE,EAAEb,IAAI,EAAE;EAClC,IAAIc,GAAG;IAAEC,GAAG;IAAEC,SAAS;IAAEC,GAAG,GAAG,QAAQ,GAAGjB,IAAI;IAAEkB,KAAK,GAAG,MAAM,GAAGD,GAAG;IAAEE,MAAM;EAC5E,OAAO,YAAW;IAChB,IAAIC,QAAQ,GAAGxB,GAAG,CAAC,IAAI,EAAEiB,EAAE,CAAC;MACxBQ,EAAE,GAAGD,QAAQ,CAACC,EAAE;MAChBC,QAAQ,GAAGF,QAAQ,CAACT,KAAK,CAACM,GAAG,CAAC,IAAI,IAAI,GAAGE,MAAM,KAAKA,MAAM,GAAGZ,WAAW,CAACP,IAAI,CAAC,CAAC,GAAGuB,SAAS;;IAE/F;IACA;IACA;IACA,IAAIF,EAAE,KAAKP,GAAG,IAAIE,SAAS,KAAKM,QAAQ,EAAE,CAACP,GAAG,GAAG,CAACD,GAAG,GAAGO,EAAE,EAAEG,IAAI,CAAC,CAAC,EAAEH,EAAE,CAACH,KAAK,EAAEF,SAAS,GAAGM,QAAQ,CAAC;IAEnGF,QAAQ,CAACC,EAAE,GAAGN,GAAG;EACnB,CAAC;AACH;AAEA,eAAe,UAASf,IAAI,EAAEW,KAAK,EAAEc,QAAQ,EAAE;EAC7C,IAAIC,CAAC,GAAG,CAAC1B,IAAI,IAAI,EAAE,MAAM,WAAW,GAAGN,oBAAoB,GAAGI,WAAW;EACzE,OAAOa,KAAK,IAAI,IAAI,GAAG,IAAI,CACtBgB,UAAU,CAAC3B,IAAI,EAAED,SAAS,CAACC,IAAI,EAAE0B,CAAC,CAAC,CAAC,CACpCL,EAAE,CAAC,YAAY,GAAGrB,IAAI,EAAEO,WAAW,CAACP,IAAI,CAAC,CAAC,GAC3C,OAAOW,KAAK,KAAK,UAAU,GAAG,IAAI,CACjCgB,UAAU,CAAC3B,IAAI,EAAEU,aAAa,CAACV,IAAI,EAAE0B,CAAC,EAAE7B,UAAU,CAAC,IAAI,EAAE,QAAQ,GAAGG,IAAI,EAAEW,KAAK,CAAC,CAAC,CAAC,CAClFiB,IAAI,CAAChB,gBAAgB,CAAC,IAAI,CAACiB,GAAG,EAAE7B,IAAI,CAAC,CAAC,GACvC,IAAI,CACH2B,UAAU,CAAC3B,IAAI,EAAEQ,aAAa,CAACR,IAAI,EAAE0B,CAAC,EAAEf,KAAK,CAAC,EAAEc,QAAQ,CAAC,CACzDJ,EAAE,CAAC,YAAY,GAAGrB,IAAI,EAAE,IAAI,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}