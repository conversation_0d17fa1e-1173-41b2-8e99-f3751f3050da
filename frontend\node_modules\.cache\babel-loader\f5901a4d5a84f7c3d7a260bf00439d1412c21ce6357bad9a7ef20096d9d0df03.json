{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\aiplanet\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport SimpleWorkflowBuilder from './pages/SimpleWorkflowBuilder';\nimport Chat<PERSON> from './pages/ChatUI';\nimport Navigation from './components/layout/Navigation';\nimport { ToastProvider } from './components/ui/toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ToastProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-background text-foreground\",\n        children: [/*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"h-screen pt-16 overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/workflow\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 16,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/workflow\",\n              element: /*#__PURE__*/_jsxDEV(SimpleWorkflowBuilder, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 17,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/chat\",\n              element: /*#__PURE__*/_jsxDEV(ChatUI, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 18,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "SimpleWorkflowBuilder", "ChatUI", "Navigation", "ToastProvider", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/aiplanet/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport SimpleWorkflowBuilder from './pages/SimpleWorkflowBuilder';\nimport ChatUI from './pages/ChatUI';\nimport Navigation from './components/layout/Navigation';\nimport { ToastProvider } from './components/ui/toast';\n\nfunction App() {\n  return (\n    <ToastProvider>\n      <Router>\n        <div className=\"min-h-screen bg-background text-foreground\">\n          <Navigation />\n          <main className=\"h-screen pt-16 overflow-hidden\">\n            <Routes>\n              <Route path=\"/\" element={<Navigate to=\"/workflow\" replace />} />\n              <Route path=\"/workflow\" element={<SimpleWorkflowBuilder />} />\n              <Route path=\"/chat\" element={<ChatUI />} />\n            </Routes>\n          </main>\n        </div>\n      </Router>\n    </ToastProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,UAAU,MAAM,gCAAgC;AACvD,SAASC,aAAa,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACF,aAAa;IAAAI,QAAA,eACZF,OAAA,CAACT,MAAM;MAAAW,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,4CAA4C;QAAAD,QAAA,gBACzDF,OAAA,CAACH,UAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACdP,OAAA;UAAMG,SAAS,EAAC,gCAAgC;UAAAD,QAAA,eAC9CF,OAAA,CAACR,MAAM;YAAAU,QAAA,gBACLF,OAAA,CAACP,KAAK;cAACe,IAAI,EAAC,GAAG;cAACC,OAAO,eAAET,OAAA,CAACN,QAAQ;gBAACgB,EAAE,EAAC,WAAW;gBAACC,OAAO;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEP,OAAA,CAACP,KAAK;cAACe,IAAI,EAAC,WAAW;cAACC,OAAO,eAAET,OAAA,CAACL,qBAAqB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DP,OAAA,CAACP,KAAK;cAACe,IAAI,EAAC,OAAO;cAACC,OAAO,eAAET,OAAA,CAACJ,MAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACK,EAAA,GAjBQX,GAAG;AAmBZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}