version: '3.8'

services:
  # Qdrant Vector Database Service
  qdrant:
    image: qdrant/qdrant:v1.7.0
    container_name: genai_qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"  # REST API
      - "6334:6334"  # gRPC API
    volumes:
      - qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    networks:
      - genai_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database Service
  postgres:
    image: postgres:15-alpine
    container_name: genai_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: genai_workflow
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - genai_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d genai_workflow"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: genai_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - genai_network
    depends_on:
      postgres:
        condition: service_healthy

  # FastAPI Backend Service (optional - for full containerization)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: genai_backend
    restart: unless-stopped
    environment:
      DATABASE_URL: ***********************************************/genai_workflow
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      QDRANT_HOST: qdrant
      QDRANT_PORT: 6333
      QDRANT_COLLECTION_NAME: document_embeddings
      UPLOAD_DIRECTORY: /app/uploads
    ports:
      - "8000:8000"
    volumes:
      - ./backend/uploads:/app/uploads
    networks:
      - genai_network
    depends_on:
      postgres:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    profiles:
      - full-stack  # Only start with --profile full-stack

  # React Frontend Service (optional - for full containerization)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: genai_frontend
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:8000
    ports:
      - "3000:3000"
    networks:
      - genai_network
    depends_on:
      - backend
    profiles:
      - full-stack  # Only start with --profile full-stack

volumes:
  qdrant_storage:
    driver: local
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  genai_network:
    driver: bridge
