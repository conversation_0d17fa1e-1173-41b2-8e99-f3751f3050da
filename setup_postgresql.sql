-- PostgreSQL Setup Script for GenAI Workflow Builder
-- Run this script to set up the database

-- Create database (run this as postgres superuser)
CREATE DATABASE genai_workflow;

-- Create user (optional - you can use existing postgres user)
-- CREATE USER genai_user WITH PASSWORD 'your_password';
-- GRANT ALL PRIVILEGES ON DATABASE genai_workflow TO genai_user;

-- Connect to the database
\c genai_workflow;

-- The FastAPI application will automatically create tables using SQLAlchemy
-- No need to manually create tables here

-- Verify connection
SELECT 'PostgreSQL setup completed successfully!' as status;
