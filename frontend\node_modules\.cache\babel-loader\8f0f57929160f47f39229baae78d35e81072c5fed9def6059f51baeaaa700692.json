{"ast": null, "code": "export default function cc(names) {\n  if (typeof names === \"string\" || typeof names === \"number\") return \"\" + names;\n  let out = \"\";\n  if (Array.isArray(names)) {\n    for (let i = 0, tmp; i < names.length; i++) {\n      if ((tmp = cc(names[i])) !== \"\") {\n        out += (out && \" \") + tmp;\n      }\n    }\n  } else {\n    for (let k in names) {\n      if (names[k]) out += (out && \" \") + k;\n    }\n  }\n  return out;\n}", "map": {"version": 3, "names": ["cc", "names", "out", "Array", "isArray", "i", "tmp", "length", "k"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/classcat/index.js"], "sourcesContent": ["export default function cc(names) {\n  if (typeof names === \"string\" || typeof names === \"number\") return \"\" + names\n\n  let out = \"\"\n\n  if (Array.isArray(names)) {\n    for (let i = 0, tmp; i < names.length; i++) {\n      if ((tmp = cc(names[i])) !== \"\") {\n        out += (out && \" \") + tmp\n      }\n    }\n  } else {\n    for (let k in names) {\n      if (names[k]) out += (out && \" \") + k\n    }\n  }\n\n  return out\n}\n"], "mappings": "AAAA,eAAe,SAASA,EAAEA,CAACC,KAAK,EAAE;EAChC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,EAAE,GAAGA,KAAK;EAE7E,IAAIC,GAAG,GAAG,EAAE;EAEZ,IAAIC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IACxB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEC,GAAG,EAAED,CAAC,GAAGJ,KAAK,CAACM,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC1C,IAAI,CAACC,GAAG,GAAGN,EAAE,CAACC,KAAK,CAACI,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE;QAC/BH,GAAG,IAAI,CAACA,GAAG,IAAI,GAAG,IAAII,GAAG;MAC3B;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAIE,CAAC,IAAIP,KAAK,EAAE;MACnB,IAAIA,KAAK,CAACO,CAAC,CAAC,EAAEN,GAAG,IAAI,CAACA,GAAG,IAAI,GAAG,IAAIM,CAAC;IACvC;EACF;EAEA,OAAON,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}