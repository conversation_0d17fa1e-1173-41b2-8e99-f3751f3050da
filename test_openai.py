#!/usr/bin/env python3
"""
Simple test script to verify OpenAI API connectivity.
"""

import os
import sys
from openai import OpenAI

def test_openai_connection():
    """Test OpenAI API connection with the provided API key."""
    
    # Get API key from environment
    api_key = "********************************************************************************************************************************************************************"
    
    if not api_key:
        print("❌ No OpenAI API key found")
        return False
    
    print(f"🔑 Testing OpenAI API key: {api_key[:20]}...")
    
    try:
        # Initialize OpenAI client
        client = OpenAI(api_key=api_key)
        
        # Test simple completion
        print("🧪 Testing simple completion...")
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say hello"}],
            max_tokens=10,
            timeout=30
        )
        
        print(f"✅ Completion successful: {response.choices[0].message.content}")
        
        # Test embeddings
        print("🧪 Testing embeddings...")
        embedding_response = client.embeddings.create(
            model="text-embedding-ada-002",
            input=["test text"],
            timeout=30
        )
        
        print(f"✅ Embeddings successful: {len(embedding_response.data[0].embedding)} dimensions")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_openai_connection()
    sys.exit(0 if success else 1)
