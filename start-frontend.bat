@echo off
echo 🎨 Starting GenAI Workflow Builder Frontend...
echo ================================================

cd /d "%~dp0frontend"

echo 📋 Checking Node.js installation...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js not found! Please install Node.js 14+
    pause
    exit /b 1
)

echo 📦 Checking dependencies...
if not exist "node_modules" (
    echo 📥 Installing dependencies...
    npm install
)

echo 🌟 Starting React development server...
echo 📡 Frontend will be available at: http://localhost:3000
echo 🔄 Hot reload enabled - changes will update automatically
echo.
echo Press Ctrl+C to stop the server
echo ================================================

npm start

pause
