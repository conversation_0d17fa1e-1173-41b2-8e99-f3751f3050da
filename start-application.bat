@echo off
echo 🚀 GenAI Workflow Builder - Complete Application Startup
echo =========================================================

echo 🔍 Checking prerequisites...

:: Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

:: Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found! Please install Node.js 14+
    pause
    exit /b 1
)

:: Check PostgreSQL
echo 🐘 Testing PostgreSQL connection...
python -c "import psycopg2; conn = psycopg2.connect('postgresql://postgres:postgres123@localhost:5432/genai_workflow'); print('✅ PostgreSQL connected'); conn.close()" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️  PostgreSQL connection failed!
    echo Please ensure PostgreSQL is running and database exists.
    echo.
    set /p continue="Continue anyway? (y/n): "
    if /i not "%continue%"=="y" exit /b 1
)

echo.
echo 🚀 Starting both Backend and Frontend...
echo =========================================================

:: Start Backend in new window
echo 📡 Starting Backend Server...
start "GenAI Backend" cmd /k "cd /d "%~dp0" && start-backend.bat"

:: Wait a moment for backend to start
timeout /t 5 /nobreak >nul

:: Start Frontend in new window
echo 🎨 Starting Frontend Server...
start "GenAI Frontend" cmd /k "cd /d "%~dp0" && start-frontend.bat"

echo.
echo ✅ Application startup initiated!
echo.
echo 📋 Access Points:
echo    🎨 Frontend: http://localhost:3000
echo    📡 Backend:  http://localhost:8000
echo    📚 API Docs: http://localhost:8000/docs
echo.
echo 💡 Both servers will open in separate windows
echo 💡 Close those windows to stop the servers
echo.

pause
