#!/bin/bash

# GenAI Workflow Builder - Production Deployment Script
# Usage: ./deploy.sh [environment] [action]
# Example: ./deploy.sh production deploy

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="genai-workflow"
ENVIRONMENT=${1:-production}
ACTION=${2:-deploy}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Check if required files exist
check_requirements() {
    local required_files=(".env" "docker-compose.prod.yml")
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "Required file $file not found"
            if [[ "$file" == ".env" ]]; then
                log_info "Copy .env.production to .env and update with your values"
            fi
            exit 1
        fi
    done
    log_success "All required files found"
}

# Load environment variables
load_env() {
    if [[ -f ".env" ]]; then
        export $(cat .env | grep -v '^#' | xargs)
        log_success "Environment variables loaded"
    else
        log_error ".env file not found"
        exit 1
    fi
}

# Validate required environment variables
validate_env() {
    local required_vars=("OPENAI_API_KEY" "POSTGRES_PASSWORD" "SECRET_KEY")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        exit 1
    fi
    log_success "Environment variables validated"
}

# Build Docker images
build_images() {
    log_info "Building Docker images..."
    
    # Build backend
    log_info "Building backend image..."
    docker build -t ${PROJECT_NAME}-backend:latest ./backend
    
    # Build frontend
    log_info "Building frontend image..."
    docker build -t ${PROJECT_NAME}-frontend:latest ./frontend
    
    log_success "Docker images built successfully"
}

# Deploy application
deploy() {
    log_info "Deploying ${PROJECT_NAME} in ${ENVIRONMENT} mode..."
    
    # Stop existing containers
    log_info "Stopping existing containers..."
    docker-compose -f docker-compose.prod.yml down --remove-orphans
    
    # Pull latest images for external services
    log_info "Pulling latest images..."
    docker-compose -f docker-compose.prod.yml pull postgres qdrant redis
    
    # Start services
    log_info "Starting services..."
    docker-compose -f docker-compose.prod.yml up -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 30
    
    # Check service health
    check_health
    
    log_success "Deployment completed successfully!"
    log_info "Application is available at:"
    log_info "  Frontend: http://localhost:${FRONTEND_PORT:-3000}"
    log_info "  Backend API: http://localhost:${BACKEND_PORT:-8000}"
    log_info "  API Docs: http://localhost:${BACKEND_PORT:-8000}/docs"
}

# Check service health
check_health() {
    local services=("postgres" "qdrant" "backend" "frontend")
    
    for service in "${services[@]}"; do
        log_info "Checking health of $service..."
        if docker-compose -f docker-compose.prod.yml ps $service | grep -q "healthy\|Up"; then
            log_success "$service is healthy"
        else
            log_warning "$service may not be fully ready yet"
        fi
    done
}

# Show logs
show_logs() {
    local service=${1:-}
    if [[ -n "$service" ]]; then
        docker-compose -f docker-compose.prod.yml logs -f "$service"
    else
        docker-compose -f docker-compose.prod.yml logs -f
    fi
}

# Stop application
stop() {
    log_info "Stopping ${PROJECT_NAME}..."
    docker-compose -f docker-compose.prod.yml down
    log_success "Application stopped"
}

# Restart application
restart() {
    stop
    deploy
}

# Backup data
backup() {
    local backup_dir="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    log_info "Creating backup in $backup_dir..."
    
    # Backup PostgreSQL
    docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U postgres genai_workflow > "$backup_dir/postgres.sql"
    
    # Backup Qdrant
    docker cp genai_qdrant_prod:/qdrant/storage "$backup_dir/qdrant_storage"
    
    # Backup uploads
    docker cp genai_backend_prod:/app/uploads "$backup_dir/uploads"
    
    log_success "Backup created in $backup_dir"
}

# Show usage
usage() {
    echo "Usage: $0 [environment] [action]"
    echo ""
    echo "Environments:"
    echo "  production    Production deployment (default)"
    echo ""
    echo "Actions:"
    echo "  deploy        Deploy the application (default)"
    echo "  build         Build Docker images only"
    echo "  stop          Stop the application"
    echo "  restart       Restart the application"
    echo "  logs [service] Show logs for all services or specific service"
    echo "  backup        Create a backup of data"
    echo "  health        Check service health"
    echo ""
    echo "Examples:"
    echo "  $0 production deploy"
    echo "  $0 production logs backend"
    echo "  $0 production backup"
}

# Main execution
main() {
    case "$ACTION" in
        "deploy")
            check_docker
            check_requirements
            load_env
            validate_env
            build_images
            deploy
            ;;
        "build")
            check_docker
            build_images
            ;;
        "stop")
            check_docker
            stop
            ;;
        "restart")
            check_docker
            check_requirements
            load_env
            validate_env
            restart
            ;;
        "logs")
            check_docker
            show_logs "$3"
            ;;
        "backup")
            check_docker
            backup
            ;;
        "health")
            check_docker
            check_health
            ;;
        "help"|"--help"|"-h")
            usage
            ;;
        *)
            log_error "Unknown action: $ACTION"
            usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
