import React, { memo, useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, useReactFlow } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Input } from '../../ui/input';
import { MessageCircle } from 'lucide-react';

const UserQueryNode = ({ id, data, selected }) => {
  const { setNodes } = useReactFlow();
  const [query, setQuery] = useState(data?.config?.query || '');

  // Update node data when query changes
  useEffect(() => {
    const updateNodeData = () => {
      setNodes((nodes) =>
        nodes.map((node) =>
          node.id === id
            ? {
                ...node,
                data: {
                  ...node.data,
                  config: {
                    ...node.data.config,
                    query: query,
                  },
                },
              }
            : node
        )
      );
    };

    const debounceTimer = setTimeout(updateNodeData, 300);
    return () => clearTimeout(debounceTimer);
  }, [query, id, setNodes]);

  const handleQueryChange = (e) => {
    setQuery(e.target.value);
  };

  return (
    <Card className={`min-w-[280px] max-w-[320px] ${selected ? 'ring-2 ring-blue-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center space-x-2">
          <div className="p-1.5 rounded-lg bg-blue-500 text-white">
            <MessageCircle className="w-4 h-4" />
          </div>
          <CardTitle className="text-sm">User Query</CardTitle>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* Query Input */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-700">
            Enter User Query
          </label>
          <Input
            type="text"
            placeholder="What would you like to know?"
            value={query}
            onChange={handleQueryChange}
            className="text-sm"
          />
        </div>

        {/* Status */}
        <div className="bg-blue-50 p-2 rounded text-xs">
          <div className="font-medium text-blue-800 mb-1">Status:</div>
          <div className="text-blue-700">
            {query ? `Query: "${query.substring(0, 30)}${query.length > 30 ? '...' : ''}"` : 'No query entered'}
          </div>
        </div>
      </CardContent>

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(UserQueryNode);
