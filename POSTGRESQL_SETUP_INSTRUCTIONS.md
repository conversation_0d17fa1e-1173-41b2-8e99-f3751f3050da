# 🐘 PostgreSQL Setup Instructions for GenAI Workflow Builder

## ⚠️ **IMPORTANT: PostgreSQL is REQUIRED for this project**

This project is configured to use PostgreSQL exclusively. Follow these steps to set it up properly.

---

## 🔧 **Method 1: Install PostgreSQL (Recommended)**

### **Step 1: Download and Install PostgreSQL**

1. **Go to**: https://www.postgresql.org/download/windows/
2. **Download**: PostgreSQL 15 or later for Windows
3. **Run the installer** and follow these settings:
   - **Port**: `5432` (default)
   - **Superuser password**: Set to `postgres123`
   - **Locale**: Default (English, United States)

### **Step 2: Verify Installation**

Open Command Prompt or PowerShell and test:

```bash
# Test PostgreSQL installation
psql --version

# Test connection (will prompt for password: postgres123)
psql -U postgres -h localhost
```

### **Step 3: Create Database**

```sql
-- In psql prompt, create the database
CREATE DATABASE genai_workflow;

-- Verify database creation
\l

-- Exit psql
\q
```

### **Step 4: Update Project Configuration**

The project is already configured with:
```env
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/genai_workflow
```

---

## 🐳 **Method 2: Use Docker (Alternative)**

### **Step 1: Install Docker Desktop**

1. Download from: https://www.docker.com/products/docker-desktop/
2. Install and start Docker Desktop

### **Step 2: Start PostgreSQL Container**

```bash
# Navigate to project directory
cd "D:\assignment for AI planet\aiplanet"

# Start PostgreSQL with Docker Compose
docker-compose up -d postgres

# Check if container is running
docker-compose ps

# Test connection
docker-compose exec postgres psql -U postgres -d genai_workflow -c "SELECT 'Connected!' as status;"
```

### **Step 3: Access Database**

- **PostgreSQL**: `localhost:5432`
- **pgAdmin** (web interface): http://localhost:5050
  - Email: `<EMAIL>`
  - Password: `admin123`

---

## 🚀 **After PostgreSQL Setup**

### **Step 1: Install Backend Dependencies**

```bash
cd backend
pip install -r requirements.txt
```

### **Step 2: Start Backend Server**

```bash
# From backend directory
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Expected Output:**
```
✅ Database connection successful
🐘 Using PostgreSQL database
📋 Creating database tables...
✅ Database tables created successfully
INFO:     Uvicorn running on http://0.0.0.0:8000
```

### **Step 3: Start Frontend**

```bash
cd frontend
npm start
```

---

## 🔍 **Troubleshooting**

### **Issue: "connection to server failed"**

**Solution:**
1. Ensure PostgreSQL service is running:
   ```bash
   # Windows (as Administrator)
   net start postgresql-x64-15
   
   # Or check services.msc for "postgresql" service
   ```

2. Check if port 5432 is available:
   ```bash
   netstat -an | findstr 5432
   ```

### **Issue: "password authentication failed"**

**Solution:**
1. Reset PostgreSQL password:
   ```bash
   # Connect as postgres user
   psql -U postgres
   
   # Change password
   ALTER USER postgres PASSWORD 'postgres123';
   ```

2. Or update `.env` file with correct password:
   ```env
   DATABASE_URL=postgresql://postgres:YOUR_PASSWORD@localhost:5432/genai_workflow
   ```

### **Issue: "database does not exist"**

**Solution:**
```sql
-- Connect to PostgreSQL
psql -U postgres

-- Create database
CREATE DATABASE genai_workflow;

-- Grant permissions (if needed)
GRANT ALL PRIVILEGES ON DATABASE genai_workflow TO postgres;
```

---

## ✅ **Verification Steps**

### **1. Test Database Connection**

```bash
# From project root
python -c "
import psycopg2
try:
    conn = psycopg2.connect('postgresql://postgres:postgres123@localhost:5432/genai_workflow')
    print('✅ PostgreSQL connection successful!')
    conn.close()
except Exception as e:
    print(f'❌ Connection failed: {e}')
"
```

### **2. Test Backend API**

```bash
# Test health endpoint
curl http://localhost:8000/health

# Expected response: {"status": "healthy", "database": "connected"}
```

### **3. Test Frontend**

- Open: http://localhost:3000 (or the port shown in terminal)
- Navigate to "Documents" tab
- Try uploading a PDF file
- Navigate to "Workflow Builder"
- Create a workflow and click "Build Stack"

---

## 📋 **Final Checklist**

- [ ] PostgreSQL installed and running
- [ ] Database `genai_workflow` created
- [ ] Backend starts without database errors
- [ ] Frontend loads successfully
- [ ] Document upload works
- [ ] Workflow validation works
- [ ] Chat interface responds

---

## 🆘 **Need Help?**

If you encounter any issues:

1. **Check PostgreSQL service status**
2. **Verify connection parameters in `.env`**
3. **Check firewall settings for port 5432**
4. **Try restarting PostgreSQL service**

The project is fully configured for PostgreSQL - once the database is set up, everything will work seamlessly!
