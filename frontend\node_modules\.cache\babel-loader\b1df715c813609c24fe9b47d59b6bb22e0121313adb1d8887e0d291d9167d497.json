{"ast": null, "code": "function none() {}\nexport default function (selector) {\n  return selector == null ? none : function () {\n    return this.querySelector(selector);\n  };\n}", "map": {"version": 3, "names": ["none", "selector", "querySelector"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/selector.js"], "sourcesContent": ["function none() {}\n\nexport default function(selector) {\n  return selector == null ? none : function() {\n    return this.querySelector(selector);\n  };\n}\n"], "mappings": "AAAA,SAASA,IAAIA,CAAA,EAAG,CAAC;AAEjB,eAAe,UAASC,QAAQ,EAAE;EAChC,OAAOA,QAAQ,IAAI,IAAI,GAAGD,IAAI,GAAG,YAAW;IAC1C,OAAO,IAAI,CAACE,aAAa,CAACD,QAAQ,CAAC;EACrC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}