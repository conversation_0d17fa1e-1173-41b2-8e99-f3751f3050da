{"ast": null, "code": "function htmlRemove() {\n  this.innerHTML = \"\";\n}\nfunction htmlConstant(value) {\n  return function () {\n    this.innerHTML = value;\n  };\n}\nfunction htmlFunction(value) {\n  return function () {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\nexport default function (value) {\n  return arguments.length ? this.each(value == null ? htmlRemove : (typeof value === \"function\" ? htmlFunction : htmlConstant)(value)) : this.node().innerHTML;\n}", "map": {"version": 3, "names": ["htmlRemove", "innerHTML", "htmlConstant", "value", "htmlFunction", "v", "apply", "arguments", "length", "each", "node"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/selection/html.js"], "sourcesContent": ["function htmlRemove() {\n  this.innerHTML = \"\";\n}\n\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\n\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\n\nexport default function(value) {\n  return arguments.length\n      ? this.each(value == null\n          ? htmlRemove : (typeof value === \"function\"\n          ? htmlFunction\n          : htmlConstant)(value))\n      : this.node().innerHTML;\n}\n"], "mappings": "AAAA,SAASA,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACC,SAAS,GAAG,EAAE;AACrB;AAEA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,OAAO,YAAW;IAChB,IAAI,CAACF,SAAS,GAAGE,KAAK;EACxB,CAAC;AACH;AAEA,SAASC,YAAYA,CAACD,KAAK,EAAE;EAC3B,OAAO,YAAW;IAChB,IAAIE,CAAC,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAI,CAACN,SAAS,GAAGI,CAAC,IAAI,IAAI,GAAG,EAAE,GAAGA,CAAC;EACrC,CAAC;AACH;AAEA,eAAe,UAASF,KAAK,EAAE;EAC7B,OAAOI,SAAS,CAACC,MAAM,GACjB,IAAI,CAACC,IAAI,CAACN,KAAK,IAAI,IAAI,GACnBH,UAAU,GAAG,CAAC,OAAOG,KAAK,KAAK,UAAU,GACzCC,YAAY,GACZF,YAAY,EAAEC,KAAK,CAAC,CAAC,GACzB,IAAI,CAACO,IAAI,CAAC,CAAC,CAACT,SAAS;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}