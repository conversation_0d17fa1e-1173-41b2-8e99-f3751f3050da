{"ast": null, "code": "var exponent = 3;\nexport var polyIn = function custom(e) {\n  e = +e;\n  function polyIn(t) {\n    return Math.pow(t, e);\n  }\n  polyIn.exponent = custom;\n  return polyIn;\n}(exponent);\nexport var polyOut = function custom(e) {\n  e = +e;\n  function polyOut(t) {\n    return 1 - Math.pow(1 - t, e);\n  }\n  polyOut.exponent = custom;\n  return polyOut;\n}(exponent);\nexport var polyInOut = function custom(e) {\n  e = +e;\n  function polyInOut(t) {\n    return ((t *= 2) <= 1 ? Math.pow(t, e) : 2 - Math.pow(2 - t, e)) / 2;\n  }\n  polyInOut.exponent = custom;\n  return polyInOut;\n}(exponent);", "map": {"version": 3, "names": ["exponent", "polyIn", "custom", "e", "t", "Math", "pow", "polyOut", "polyInOut"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-ease/src/poly.js"], "sourcesContent": ["var exponent = 3;\n\nexport var polyIn = (function custom(e) {\n  e = +e;\n\n  function polyIn(t) {\n    return Math.pow(t, e);\n  }\n\n  polyIn.exponent = custom;\n\n  return polyIn;\n})(exponent);\n\nexport var polyOut = (function custom(e) {\n  e = +e;\n\n  function polyOut(t) {\n    return 1 - Math.pow(1 - t, e);\n  }\n\n  polyOut.exponent = custom;\n\n  return polyOut;\n})(exponent);\n\nexport var polyInOut = (function custom(e) {\n  e = +e;\n\n  function polyInOut(t) {\n    return ((t *= 2) <= 1 ? Math.pow(t, e) : 2 - Math.pow(2 - t, e)) / 2;\n  }\n\n  polyInOut.exponent = custom;\n\n  return polyInOut;\n})(exponent);\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,CAAC;AAEhB,OAAO,IAAIC,MAAM,GAAI,SAASC,MAAMA,CAACC,CAAC,EAAE;EACtCA,CAAC,GAAG,CAACA,CAAC;EAEN,SAASF,MAAMA,CAACG,CAAC,EAAE;IACjB,OAAOC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAED,CAAC,CAAC;EACvB;EAEAF,MAAM,CAACD,QAAQ,GAAGE,MAAM;EAExB,OAAOD,MAAM;AACf,CAAC,CAAED,QAAQ,CAAC;AAEZ,OAAO,IAAIO,OAAO,GAAI,SAASL,MAAMA,CAACC,CAAC,EAAE;EACvCA,CAAC,GAAG,CAACA,CAAC;EAEN,SAASI,OAAOA,CAACH,CAAC,EAAE;IAClB,OAAO,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC,CAAC;EAC/B;EAEAI,OAAO,CAACP,QAAQ,GAAGE,MAAM;EAEzB,OAAOK,OAAO;AAChB,CAAC,CAAEP,QAAQ,CAAC;AAEZ,OAAO,IAAIQ,SAAS,GAAI,SAASN,MAAMA,CAACC,CAAC,EAAE;EACzCA,CAAC,GAAG,CAACA,CAAC;EAEN,SAASK,SAASA,CAACJ,CAAC,EAAE;IACpB,OAAO,CAAC,CAACA,CAAC,IAAI,CAAC,KAAK,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,EAAED,CAAC,CAAC,GAAG,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,CAAC,EAAED,CAAC,CAAC,IAAI,CAAC;EACtE;EAEAK,SAAS,CAACR,QAAQ,GAAGE,MAAM;EAE3B,OAAOM,SAAS;AAClB,CAAC,CAAER,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}