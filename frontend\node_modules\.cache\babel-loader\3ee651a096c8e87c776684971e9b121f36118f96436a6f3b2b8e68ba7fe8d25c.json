{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst GitBranch = createLucideIcon(\"GitBranch\", [[\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"3\",\n  y2: \"15\",\n  key: \"17qcm7\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"6\",\n  r: \"3\",\n  key: \"1h7g24\"\n}], [\"circle\", {\n  cx: \"6\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"fqmcym\"\n}], [\"path\", {\n  d: \"M18 9a9 9 0 0 1-9 9\",\n  key: \"n2h4wq\"\n}]]);\nexport { GitBranch as default };", "map": {"version": 3, "names": ["GitBranch", "createLucideIcon", "x1", "x2", "y1", "y2", "key", "cx", "cy", "r", "d"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\git-branch.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name GitBranch\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iNiIgeDI9IjYiIHkxPSIzIiB5Mj0iMTUiIC8+CiAgPGNpcmNsZSBjeD0iMTgiIGN5PSI2IiByPSIzIiAvPgogIDxjaXJjbGUgY3g9IjYiIGN5PSIxOCIgcj0iMyIgLz4KICA8cGF0aCBkPSJNMTggOWE5IDkgMCAwIDEtOSA5IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/git-branch\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst GitBranch = createLucideIcon('GitBranch', [\n  ['line', { x1: '6', x2: '6', y1: '3', y2: '15', key: '17qcm7' }],\n  ['circle', { cx: '18', cy: '6', r: '3', key: '1h7g24' }],\n  ['circle', { cx: '6', cy: '18', r: '3', key: 'fqmcym' }],\n  ['path', { d: 'M18 9a9 9 0 0 1-9 9', key: 'n2h4wq' }],\n]);\n\nexport default GitBranch;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEI,CAAA,EAAG,qBAAuB;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}