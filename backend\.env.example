# Database Configuration - PostgreSQL (Required)
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/genai_workflow

# OpenAI Configuration (Required)
OPENAI_API_KEY=your_openai_api_key_here

# ChromaDB Configuration
CHROMA_PERSIST_DIRECTORY=./chroma_db

# Security Configuration
SECRET_KEY=your-secret-key-change-in-production

# File Upload Configuration
UPLOAD_DIRECTORY=./uploads
MAX_FILE_SIZE=10485760

# Application Configuration
DEBUG=true

# Web Search Configuration (Optional)
SERPAPI_API_KEY=your_serpapi_key_here
ENABLE_WEB_SEARCH=false
