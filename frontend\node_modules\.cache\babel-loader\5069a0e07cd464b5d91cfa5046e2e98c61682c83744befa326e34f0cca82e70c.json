{"ast": null, "code": "export default function () {\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n  return this;\n}", "map": {"version": 3, "names": ["groups", "_groups", "j", "m", "length", "group", "i", "next", "node", "compareDocumentPosition", "parentNode", "insertBefore"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/selection/order.js"], "sourcesContent": ["export default function() {\n\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m;) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0;) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4) next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n\n  return this;\n}\n"], "mappings": "AAAA,eAAe,YAAW;EAExB,KAAK,IAAIA,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAE,EAAEF,CAAC,GAAGC,CAAC,GAAG;IACnE,KAAK,IAAIE,KAAK,GAAGL,MAAM,CAACE,CAAC,CAAC,EAAEI,CAAC,GAAGD,KAAK,CAACD,MAAM,GAAG,CAAC,EAAEG,IAAI,GAAGF,KAAK,CAACC,CAAC,CAAC,EAAEE,IAAI,EAAE,EAAEF,CAAC,IAAI,CAAC,GAAG;MAClF,IAAIE,IAAI,GAAGH,KAAK,CAACC,CAAC,CAAC,EAAE;QACnB,IAAIC,IAAI,IAAIC,IAAI,CAACC,uBAAuB,CAACF,IAAI,CAAC,GAAG,CAAC,EAAEA,IAAI,CAACG,UAAU,CAACC,YAAY,CAACH,IAAI,EAAED,IAAI,CAAC;QAC5FA,IAAI,GAAGC,IAAI;MACb;IACF;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}