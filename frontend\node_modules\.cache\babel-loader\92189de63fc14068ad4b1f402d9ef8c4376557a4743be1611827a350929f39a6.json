{"ast": null, "code": "import creator from \"./creator.js\";\nimport select from \"./select.js\";\nexport default function (name) {\n  return select(creator(name).call(document.documentElement));\n}", "map": {"version": 3, "names": ["creator", "select", "name", "call", "document", "documentElement"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/create.js"], "sourcesContent": ["import creator from \"./creator.js\";\nimport select from \"./select.js\";\n\nexport default function(name) {\n  return select(creator(name).call(document.documentElement));\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,MAAM,MAAM,aAAa;AAEhC,eAAe,UAASC,IAAI,EAAE;EAC5B,OAAOD,MAAM,CAACD,OAAO,CAACE,IAAI,CAAC,CAACC,IAAI,CAACC,QAAQ,CAACC,eAAe,CAAC,CAAC;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}