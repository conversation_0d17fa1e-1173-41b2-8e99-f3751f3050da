import React, { useState, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import ReactFlow, {
  ReactFlowProvider,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { Button } from '../components/ui/button';
import ComponentLibraryPanel from '../components/workflow/panels/ComponentLibraryPanel';
import ConfigurationPanel from '../components/workflow/panels/ConfigurationPanel';
import UserQueryNode from '../components/workflow/nodes/UserQueryNode';
import KnowledgeBaseNode from '../components/workflow/nodes/KnowledgeBaseNode';
import LLMEngineNode from '../components/workflow/nodes/LLMEngineNode';
import OutputNode from '../components/workflow/nodes/OutputNode';

import {
  Play,
  Save,
  Trash2,
  CheckCircle,
  AlertCircle,
  Loader2,
  MessageSquare
} from 'lucide-react';

// Node types mapping
const nodeTypes = {
  userQuery: UserQueryNode,
  knowledgeBase: KnowledgeBaseNode,
  llmEngine: LLMEngineNode,
  output: OutputNode,
};

// Initial nodes and edges
const initialNodes = [];
const initialEdges = [];

const WorkflowBuilder = () => {
  const navigate = useNavigate();
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState(null);
  const [isValidWorkflow, setIsValidWorkflow] = useState(false);
  const [isBuilding, setIsBuilding] = useState(false);
  const reactFlowWrapper = useRef(null);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);

  // React Flow event handlers
  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (typeof type === 'undefined' || !type) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      addNode(type, position);
    },
    [reactFlowInstance]
  );

  const addNode = useCallback((type, position = null) => {
    const id = `${type}-${Date.now()}`;
    const newNode = {
      id,
      type,
      position: position || { 
        x: Math.random() * 400 + 100, 
        y: Math.random() * 400 + 100 
      },
      data: {
        label: getNodeLabel(type),
        config: getDefaultConfig(type),
      },
    };
    setNodes((nds) => nds.concat(newNode));
  }, [setNodes]);

  const getNodeLabel = (type) => {
    const labels = {
      userQuery: 'User Query',
      knowledgeBase: 'Knowledge Base',
      llmEngine: 'LLM Engine',
      output: 'Output',
    };
    return labels[type] || 'Unknown';
  };

  const getDefaultConfig = (type) => {
    const configs = {
      userQuery: { placeholder: 'Enter your question...' },
      knowledgeBase: { maxResults: 5, similarityThreshold: 0.7 },
      llmEngine: { 
        model: 'gpt-3.5-turbo', 
        temperature: 0.7, 
        maxTokens: 1000,
        enableWebSearch: false,
        systemPrompt: ''
      },
      output: { format: 'text', showSources: true, enableFollowUp: true },
    };
    return configs[type] || {};
  };

  const updateNodeConfig = useCallback((nodeId, newData) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: newData,
          };
        }
        return node;
      })
    );
  }, [setNodes]);

  const onNodeClick = useCallback((event, node) => {
    setSelectedNode(node);
  }, []);

  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
  }, []);

  const validateWorkflow = useCallback(() => {
    // Check if workflow has required components
    const hasUserQuery = nodes.some(node => node.type === 'userQuery');
    const hasOutput = nodes.some(node => node.type === 'output');
    const hasConnections = edges.length > 0;
    
    const isValid = hasUserQuery && hasOutput && hasConnections;
    setIsValidWorkflow(isValid);
    return isValid;
  }, [nodes, edges]);

  const clearWorkflow = useCallback(() => {
    setNodes([]);
    setEdges([]);
    setSelectedNode(null);
    setIsValidWorkflow(false);
  }, [setNodes, setEdges]);

  const saveWorkflow = useCallback(() => {
    const workflow = { nodes, edges };
    localStorage.setItem('workflow', JSON.stringify(workflow));
    alert('Workflow saved successfully!');
  }, [nodes, edges]);

  const executeWorkflow = useCallback(async () => {
    if (!validateWorkflow()) {
      alert('Please build a valid workflow first. Ensure you have User Query and Output components connected.');
      return;
    }

    setIsBuilding(true);

    try {
      // Convert workflow to backend format
      const workflowData = {
        nodes: nodes.map(node => ({
          id: node.id,
          type: node.type,
          position: node.position,
          data: node.data
        })),
        connections: edges.map(edge => ({
          source: edge.source,
          target: edge.target,
          sourceHandle: edge.sourceHandle,
          targetHandle: edge.targetHandle
        }))
      };

      // Validate workflow with backend
      const response = await fetch('http://localhost:8000/api/workflow/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workflowData)
      });

      const result = await response.json();

      if (result.valid) {
        // Save workflow to localStorage for chat interface
        localStorage.setItem('currentWorkflow', JSON.stringify(workflowData));

        alert(`✅ Workflow built successfully!\n\n` +
              `• ${result.node_count} components configured\n` +
              `• ${result.connection_count} connections established\n` +
              `• Execution order: ${result.execution_order.join(' → ')}\n\n` +
              `Ready to chat with your AI workflow!`);

        // Navigate to chat interface
        navigate('/chat');
      } else {
        alert(`❌ Workflow validation failed:\n\n${result.error}\n\nPlease fix the issues and try again.`);
      }
    } catch (error) {
      console.error('Error building workflow:', error);
      alert('❌ Failed to build workflow. Please check your connection and try again.');
    } finally {
      setIsBuilding(false);
    }
  }, [nodes, edges, validateWorkflow, navigate]);

  return (
    <ReactFlowProvider>
      <div className="flex h-full bg-background">
        {/* Component Library Panel */}
        <ComponentLibraryPanel onAddNode={addNode} />

        {/* Main Workflow Canvas */}
        <div className="flex-1 flex flex-col">
          {/* Toolbar */}
          <div className="p-4 border-b border-border bg-card">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h2 className="text-lg font-semibold text-foreground">Workflow Canvas</h2>
                <div className="flex items-center space-x-2">
                  {isValidWorkflow ? (
                    <div className="flex items-center space-x-1 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">Valid Workflow</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1 text-yellow-600">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">Incomplete Workflow</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Button onClick={validateWorkflow} variant="outline" size="sm">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Validate
                </Button>
                
                <Button onClick={executeWorkflow} size="sm" disabled={!isValidWorkflow || isBuilding}>
                  {isBuilding ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Play className="w-4 h-4 mr-2" />
                  )}
                  {isBuilding ? 'Building...' : 'Build Stack'}
                </Button>

                <Button
                  onClick={() => navigate('/chat')}
                  variant="outline"
                  size="sm"
                  disabled={!isValidWorkflow}
                >
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Chat with Stack
                </Button>
                
                <Button onClick={saveWorkflow} variant="outline" size="sm">
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
                
                <Button onClick={clearWorkflow} variant="outline" size="sm">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Clear
                </Button>
              </div>
            </div>
          </div>

          {/* React Flow Canvas */}
          <div className="flex-1" ref={reactFlowWrapper}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onInit={setReactFlowInstance}
              onDrop={onDrop}
              onDragOver={onDragOver}
              onNodeClick={onNodeClick}
              onPaneClick={onPaneClick}
              nodeTypes={nodeTypes}
              fitView
              snapToGrid={true}
              snapGrid={[15, 15]}
              defaultViewport={{ x: 0, y: 0, zoom: 1 }}
            >
              <Controls />
              <MiniMap 
                nodeColor={(node) => {
                  switch (node.type) {
                    case 'userQuery': return '#3b82f6';
                    case 'knowledgeBase': return '#10b981';
                    case 'llmEngine': return '#f59e0b';
                    case 'output': return '#ef4444';
                    default: return '#6b7280';
                  }
                }}
                className="bg-background border border-border"
              />
              <Background 
                variant={BackgroundVariant.Dots} 
                gap={20} 
                size={1}
                className="bg-muted/20"
              />
            </ReactFlow>
          </div>
        </div>

        {/* Configuration Panel */}
        <ConfigurationPanel 
          selectedNode={selectedNode}
          onUpdateNode={updateNodeConfig}
          onClose={() => setSelectedNode(null)}
        />
      </div>
    </ReactFlowProvider>
  );
};

export default WorkflowBuilder;
