{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Stamp = createLucideIcon(\"Stamp\", [[\"path\", {\n  d: \"M5 22h14\",\n  key: \"ehvnwv\"\n}], [\"path\", {\n  d: \"M19.27 13.73A2.5 2.5 0 0 0 17.5 13h-11A2.5 2.5 0 0 0 4 15.5V17a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-1.5c0-.66-.26-1.3-.73-1.77Z\",\n  key: \"1sy9ra\"\n}], [\"path\", {\n  d: \"M14 13V8.5C14 7 15 7 15 5a3 3 0 0 0-3-3c-1.66 0-3 1-3 3s1 2 1 3.5V13\",\n  key: \"cnxgux\"\n}]]);\nexport { Stamp as default };", "map": {"version": 3, "names": ["Stamp", "createLucideIcon", "d", "key"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\stamp.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Stamp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAyMmgxNCIgLz4KICA8cGF0aCBkPSJNMTkuMjcgMTMuNzNBMi41IDIuNSAwIDAgMCAxNy41IDEzaC0xMUEyLjUgMi41IDAgMCAwIDQgMTUuNVYxN2ExIDEgMCAwIDAgMSAxaDE0YTEgMSAwIDAgMCAxLTF2LTEuNWMwLS42Ni0uMjYtMS4zLS43My0xLjc3WiIgLz4KICA8cGF0aCBkPSJNMTQgMTNWOC41QzE0IDcgMTUgNyAxNSA1YTMgMyAwIDAgMC0zLTNjLTEuNjYgMC0zIDEtMyAzczEgMiAxIDMuNVYxMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/stamp\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Stamp = createLucideIcon('Stamp', [\n  ['path', { d: 'M5 22h14', key: 'ehvnwv' }],\n  [\n    'path',\n    {\n      d: 'M19.27 13.73A2.5 2.5 0 0 0 17.5 13h-11A2.5 2.5 0 0 0 4 15.5V17a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1v-1.5c0-.66-.26-1.3-.73-1.77Z',\n      key: '1sy9ra',\n    },\n  ],\n  [\n    'path',\n    { d: 'M14 13V8.5C14 7 15 7 15 5a3 3 0 0 0-3-3c-1.66 0-3 1-3 3s1 2 1 3.5V13', key: 'cnxgux' },\n  ],\n]);\n\nexport default Stamp;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EAAED,CAAA,EAAG,sEAAwE;EAAAC,GAAA,EAAK;AAAS,EAC7F,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}