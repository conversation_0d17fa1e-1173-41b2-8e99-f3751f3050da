import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { X, Settings } from 'lucide-react';

const ConfigPanel = ({ selectedNode, onConfigUpdate, onClose }) => {
  const [config, setConfig] = useState({});

  // Update local config when selected node changes
  useEffect(() => {
    if (selectedNode?.data?.config) {
      setConfig(selectedNode.data.config);
    } else {
      setConfig({});
    }
  }, [selectedNode]);

  // Handle config field changes
  const handleConfigChange = (field, value) => {
    const newConfig = { ...config, [field]: value };
    setConfig(newConfig);
    
    if (selectedNode) {
      onConfigUpdate(selectedNode.id, newConfig);
    }
  };

  // Render configuration fields based on node type
  const renderConfigFields = () => {
    if (!selectedNode) return null;

    switch (selectedNode.type) {
      case 'userQuery':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">
                Placeholder Text
              </label>
              <Input
                value={config.placeholder || ''}
                onChange={(e) => handleConfigChange('placeholder', e.target.value)}
                placeholder="Enter placeholder text..."
                className="mt-1"
              />
            </div>
          </div>
        );

      case 'knowledgeBase':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">
                Document ID
              </label>
              <Input
                type="number"
                value={config.documentId || ''}
                onChange={(e) => handleConfigChange('documentId', parseInt(e.target.value) || null)}
                placeholder="Enter document ID..."
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Leave empty to search all documents
              </p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-foreground">
                Max Results
              </label>
              <Input
                type="number"
                value={config.maxResults || 5}
                onChange={(e) => handleConfigChange('maxResults', parseInt(e.target.value) || 5)}
                min="1"
                max="20"
                className="mt-1"
              />
            </div>
          </div>
        );

      case 'llmEngine':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">
                Model
              </label>
              <select
                value={config.model || 'gpt-3.5-turbo'}
                onChange={(e) => handleConfigChange('model', e.target.value)}
                className="mt-1 w-full h-10 px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="gpt-4">GPT-4</option>
                <option value="gpt-4-turbo">GPT-4 Turbo</option>
              </select>
            </div>
            
            <div>
              <label className="text-sm font-medium text-foreground">
                Temperature ({config.temperature || 0.7})
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={config.temperature || 0.7}
                onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
                className="mt-1 w-full"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Focused</span>
                <span>Creative</span>
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-foreground">
                Max Tokens
              </label>
              <Input
                type="number"
                value={config.maxTokens || 1000}
                onChange={(e) => handleConfigChange('maxTokens', parseInt(e.target.value) || 1000)}
                min="1"
                max="4000"
                className="mt-1"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="enableWebSearch"
                checked={config.enableWebSearch || false}
                onChange={(e) => handleConfigChange('enableWebSearch', e.target.checked)}
                className="rounded border-input"
              />
              <label htmlFor="enableWebSearch" className="text-sm font-medium text-foreground">
                Enable Web Search
              </label>
            </div>
          </div>
        );

      case 'output':
        return (
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-foreground">
                Output Format
              </label>
              <select
                value={config.format || 'text'}
                onChange={(e) => handleConfigChange('format', e.target.value)}
                className="mt-1 w-full h-10 px-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="text">Plain Text</option>
                <option value="markdown">Markdown</option>
                <option value="html">HTML</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showSources"
                checked={config.showSources !== false}
                onChange={(e) => handleConfigChange('showSources', e.target.checked)}
                className="rounded border-input"
              />
              <label htmlFor="showSources" className="text-sm font-medium text-foreground">
                Show Sources
              </label>
            </div>
          </div>
        );

      default:
        return (
          <p className="text-sm text-muted-foreground">
            No configuration options available for this node type.
          </p>
        );
    }
  };

  if (!selectedNode) {
    return (
      <div className="w-80 xl:w-96 bg-background border-l border-border p-4 shadow-lg">
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <Settings className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              Node Configuration
            </h3>
            <p className="text-sm text-muted-foreground">
              Select a node to configure its properties
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 xl:w-96 bg-background border-l border-border flex flex-col shadow-lg">
      {/* Header */}
      <div className="p-4 border-b border-border flex items-center justify-between">
        <div className="min-w-0 flex-1">
          <h3 className="text-lg font-semibold text-foreground truncate">
            Configure Node
          </h3>
          <p className="text-sm text-muted-foreground truncate">
            {selectedNode.data.label}
          </p>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="flex-shrink-0 lg:hidden"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Configuration Form */}
      <div className="flex-1 p-4 overflow-y-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Properties</CardTitle>
            <CardDescription>
              Configure the behavior of this node
            </CardDescription>
          </CardHeader>
          <CardContent>
            {renderConfigFields()}
          </CardContent>
        </Card>
      </div>

      {/* Node Info */}
      <div className="p-4 border-t border-border">
        <Card>
          <CardContent className="p-3">
            <div className="text-xs text-muted-foreground space-y-1">
              <p><strong>Node ID:</strong> {selectedNode.id}</p>
              <p><strong>Type:</strong> {selectedNode.type}</p>
              <p><strong>Position:</strong> ({Math.round(selectedNode.position.x)}, {Math.round(selectedNode.position.y)})</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ConfigPanel;
