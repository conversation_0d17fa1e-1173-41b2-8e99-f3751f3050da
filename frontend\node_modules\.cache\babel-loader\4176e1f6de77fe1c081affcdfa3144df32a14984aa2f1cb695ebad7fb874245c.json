{"ast": null, "code": "export default function (node) {\n  return node.ownerDocument && node.ownerDocument.defaultView // node is a Node\n  || node.document && node // node is a Window\n  || node.defaultView; // node is a Document\n}", "map": {"version": 3, "names": ["node", "ownerDocument", "defaultView", "document"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/window.js"], "sourcesContent": ["export default function(node) {\n  return (node.ownerDocument && node.ownerDocument.defaultView) // node is a Node\n      || (node.document && node) // node is a Window\n      || node.defaultView; // node is a Document\n}\n"], "mappings": "AAAA,eAAe,UAASA,IAAI,EAAE;EAC5B,OAAQA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACC,aAAa,CAACC,WAAW,CAAE;EAAA,GACtDF,IAAI,CAACG,QAAQ,IAAIH,IAAK,CAAC;EAAA,GACxBA,IAAI,CAACE,WAAW,CAAC,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}