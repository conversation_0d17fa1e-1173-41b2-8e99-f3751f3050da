# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/genai_workflow

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# ChromaDB Configuration
CHROMA_PERSIST_DIRECTORY=./chroma_db

# Security Configuration
SECRET_KEY=your-secret-key-change-in-production

# File Upload Configuration
UPLOAD_DIRECTORY=./uploads
MAX_FILE_SIZE=10485760

# Application Configuration
DEBUG=false
