#!/usr/bin/env python3
"""
PostgreSQL Database Setup Script for GenAI Workflow Builder
This script sets up PostgreSQL database using Docker Compose or manual installation
"""

import os
import sys
import subprocess
import time
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_docker():
    """Check if Docker is available"""
    try:
        subprocess.run(["docker", "--version"], capture_output=True, check=True)
        subprocess.run(["docker-compose", "--version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def setup_with_docker():
    """Set up PostgreSQL using Docker Compose"""
    print("🐳 Setting up PostgreSQL with Docker Compose...")

    try:
        # Start PostgreSQL container
        print("🚀 Starting PostgreSQL container...")
        result = subprocess.run(
            ["docker-compose", "up", "-d", "postgres"],
            capture_output=True,
            text=True,
            check=True
        )

        print("⏳ Waiting for PostgreSQL to be ready...")
        time.sleep(10)  # Give PostgreSQL time to start

        # Check if container is running
        result = subprocess.run(
            ["docker-compose", "ps", "postgres"],
            capture_output=True,
            text=True
        )

        if "Up" in result.stdout:
            print("✅ PostgreSQL container is running!")
            return True
        else:
            print("❌ PostgreSQL container failed to start")
            print(result.stdout)
            return False

    except subprocess.CalledProcessError as e:
        print(f"❌ Docker Compose error: {e}")
        print(f"Output: {e.stdout}")
        print(f"Error: {e.stderr}")
        return False

def setup_manual_postgres():
    """Set up PostgreSQL manually (requires PostgreSQL to be installed)"""
    print("🔧 Setting up PostgreSQL manually...")

    # Database connection parameters
    host = "localhost"
    port = "5432"
    user = "postgres"
    password = "postgres123"  # Updated password
    database_name = "genai_workflow"

    try:
        # Connect to PostgreSQL server
        print(f"📡 Connecting to PostgreSQL server at {host}:{port}")
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database="postgres"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()

        # Check if database exists
        cursor.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s", (database_name,))
        exists = cursor.fetchone()

        if not exists:
            print(f"🗄️  Creating database '{database_name}'...")
            cursor.execute(f'CREATE DATABASE "{database_name}"')
            print(f"✅ Database '{database_name}' created successfully!")
        else:
            print(f"✅ Database '{database_name}' already exists!")

        cursor.close()
        conn.close()

        # Test connection to the new database
        print(f"🔍 Testing connection to '{database_name}'...")
        test_conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database_name
        )
        test_conn.close()
        print("✅ Database connection test successful!")

        return True

    except psycopg2.Error as e:
        print(f"❌ PostgreSQL Error: {e}")
        return False

def test_database_connection():
    """Test the database connection with the configured URL"""
    try:
        # Load the database URL from .env
        database_url = os.getenv("DATABASE_URL", "postgresql://postgres:postgres123@localhost:5432/genai_workflow")

        print(f"🔍 Testing database connection...")
        print(f"🔗 Database URL: {database_url}")

        # Parse the URL to get connection parameters
        import urllib.parse as urlparse
        url = urlparse.urlparse(database_url)

        conn = psycopg2.connect(
            host=url.hostname,
            port=url.port,
            user=url.username,
            password=url.password,
            database=url.path[1:]  # Remove leading slash
        )

        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✅ Connected to PostgreSQL: {version[0]}")

        cursor.close()
        conn.close()
        return True

    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False

def main():
    print("🚀 GenAI Workflow Builder - PostgreSQL Setup")
    print("=" * 60)

    # Check if Docker is available
    has_docker = check_docker()

    if has_docker:
        print("🐳 Docker detected! Using Docker Compose for PostgreSQL setup...")
        if setup_with_docker():
            print("✅ PostgreSQL setup with Docker completed!")
        else:
            print("❌ Docker setup failed, trying manual setup...")
            if not setup_manual_postgres():
                print("❌ Manual setup also failed!")
                sys.exit(1)
    else:
        print("📦 Docker not found. Setting up PostgreSQL manually...")
        print("⚠️  Make sure PostgreSQL is installed and running!")
        if not setup_manual_postgres():
            print("❌ Manual PostgreSQL setup failed!")
            print("\n💡 Options:")
            print("1. Install Docker and Docker Compose, then run this script again")
            print("2. Install PostgreSQL manually and ensure it's running")
            print("3. Update the connection parameters in this script")
            sys.exit(1)

    # Test the connection
    if test_database_connection():
        print("\n🎉 PostgreSQL setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Install backend dependencies: cd backend && pip install -r requirements.txt")
        print("2. Start the backend server: cd backend && python -m uvicorn app.main:app --reload")
        print("3. Start the frontend: cd frontend && npm start")
        print("\n🔗 Database Access:")
        print("- PostgreSQL: localhost:5432")
        print("- pgAdmin (if using Docker): http://localhost:5050")
        print("  - Email: <EMAIL>")
        print("  - Password: admin123")
    else:
        print("\n❌ Database setup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
