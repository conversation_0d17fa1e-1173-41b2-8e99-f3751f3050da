#!/usr/bin/env python3
"""
Test script to verify the PDF-to-LLM pipeline is working correctly.
This script will:
1. Check if documents exist
2. Generate embeddings if needed
3. Test vector search
4. Test the complete pipeline
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_documents():
    """Check what documents are available"""
    print("🔍 Checking available documents...")
    response = requests.get(f"{BASE_URL}/api/documents/")
    if response.status_code == 200:
        docs = response.json()
        print(f"📄 Found {len(docs)} documents:")
        for doc in docs:
            print(f"   ID: {doc['id']}, File: {doc['original_filename']}, Embeddings: {doc.get('embeddings_generated', False)}")
        return docs
    else:
        print(f"❌ Failed to get documents: {response.status_code}")
        return []

def generate_embeddings(document_id):
    """Generate embeddings for a document"""
    print(f"🧠 Generating embeddings for document {document_id}...")
    data = {
        "document_id": document_id,
        "chunk_size": 1000,
        "chunk_overlap": 200
    }
    response = requests.post(f"{BASE_URL}/api/embeddings/generate", json=data)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Embeddings generated: {result}")
        return True
    else:
        print(f"❌ Failed to generate embeddings: {response.status_code} - {response.text}")
        return False

def test_vector_search(document_id, query="What is my name?"):
    """Test vector search functionality"""
    print(f"🔍 Testing vector search for document {document_id} with query: '{query}'")
    params = {
        "query": query,
        "document_id": document_id,
        "n_results": 3
    }
    response = requests.get(f"{BASE_URL}/api/embeddings/search", params=params)
    if response.status_code == 200:
        result = response.json()
        print(f"📊 Search results: {result['results_count']} chunks found")
        for i, chunk in enumerate(result['results'][:2]):  # Show first 2 chunks
            print(f"   Chunk {i+1}: score={chunk.get('score', 0):.3f}, text='{chunk.get('text', '')[:100]}...'")
        return result['results_count'] > 0
    else:
        print(f"❌ Vector search failed: {response.status_code} - {response.text}")
        return False

def test_pipeline(document_id, query="What is my name?"):
    """Test the complete pipeline"""
    print(f"🏥 Testing complete pipeline for document {document_id}...")
    response = requests.get(f"{BASE_URL}/api/embeddings/test-pipeline/{document_id}?test_query={query}")
    if response.status_code == 200:
        result = response.json()
        diagnosis = result['diagnosis']
        
        print("📊 Pipeline Health Check:")
        print(f"   📄 PDF Extracted: {'✅' if diagnosis['pdf_extracted'] else '❌'}")
        print(f"   🧠 Embeddings Ready: {'✅' if diagnosis['embeddings_ready'] else '❌'}")
        print(f"   🔍 Search Working: {'✅' if diagnosis['search_working'] else '❌'}")
        print(f"   💬 LLM Would Have Context: {'✅' if diagnosis['llm_would_have_context'] else '❌'}")
        print(f"   🏥 Pipeline Healthy: {'✅' if diagnosis['pipeline_healthy'] else '❌'}")
        
        if result['llm_context']['context_preview']:
            print(f"\n📝 Context Preview:\n{result['llm_context']['context_preview'][:300]}...")
        
        return diagnosis['pipeline_healthy']
    else:
        print(f"❌ Pipeline test failed: {response.status_code} - {response.text}")
        return False

def test_workflow_execution(doc_id, query="What is my name?"):
    """Test complete workflow execution"""
    print(f"🚀 Testing workflow execution with query: '{query}' using document {doc_id}")
    
    # Create a simple workflow
    workflow = {
        "nodes": [
            {
                "id": "1",
                "type": "user_query",
                "position": {"x": 100, "y": 100},
                "data": {"config": {}}
            },
            {
                "id": "2",
                "type": "knowledge_base",
                "position": {"x": 200, "y": 100},
                "data": {"config": {"selectedDocuments": [doc_id], "maxResults": 5}}
            },
            {
                "id": "3",
                "type": "llm_engine", 
                "position": {"x": 300, "y": 100},
                "data": {"config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "maxTokens": 1000}}
            },
            {
                "id": "4",
                "type": "output",
                "position": {"x": 400, "y": 100}, 
                "data": {"config": {}}
            }
        ],
        "connections": [
            {"source": "1", "target": "2"},
            {"source": "2", "target": "3"},
            {"source": "3", "target": "4"}
        ]
    }
    
    data = {
        "user_id": "test-user",
        "query": query,
        "workflow": workflow
    }
    
    response = requests.post(f"{BASE_URL}/api/workflow/execute", json=data)
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Workflow executed successfully!")
        print(f"📝 Response: {result.get('response', 'No response')[:200]}...")
        return True
    else:
        print(f"❌ Workflow execution failed: {response.status_code} - {response.text}")
        return False

def main():
    print("🧪 PDF-to-LLM Pipeline Test Suite")
    print("=" * 50)
    
    # Step 1: Check documents
    docs = test_documents()
    if not docs:
        print("❌ No documents found. Please upload a PDF first.")
        return
    
    # Use the first document
    doc_id = docs[0]['id']
    print(f"\n🎯 Testing with document ID: {doc_id}")
    
    # Step 2: Generate embeddings if needed
    if not docs[0].get('embeddings_generated', False):
        print("\n📋 Embeddings not generated yet, generating now...")
        if not generate_embeddings(doc_id):
            print("❌ Cannot proceed without embeddings")
            return
    else:
        print("\n✅ Embeddings already generated")
    
    # Step 3: Test vector search
    print("\n" + "="*30)
    if not test_vector_search(doc_id):
        print("❌ Vector search failed")
        return
    
    # Step 4: Test complete pipeline
    print("\n" + "="*30)
    if not test_pipeline(doc_id):
        print("❌ Pipeline test failed")
        return
    
    # Step 5: Test workflow execution (requires OpenAI API key)
    print("\n" + "="*30)
    print("⚠️  Workflow execution test requires OpenAI API key")
    try:
        test_workflow_execution(doc_id)
    except Exception as e:
        print(f"⚠️  Workflow test skipped: {e}")
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    main()
