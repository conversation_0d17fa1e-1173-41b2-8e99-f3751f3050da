{"ast": null, "code": "import { tpmt } from \"./math.js\";\nexport function expIn(t) {\n  return tpmt(1 - +t);\n}\nexport function expOut(t) {\n  return 1 - tpmt(t);\n}\nexport function expInOut(t) {\n  return ((t *= 2) <= 1 ? tpmt(1 - t) : 2 - tpmt(t - 1)) / 2;\n}", "map": {"version": 3, "names": ["tpmt", "expIn", "t", "expOut", "expInOut"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-ease/src/exp.js"], "sourcesContent": ["import {tpmt} from \"./math.js\";\n\nexport function expIn(t) {\n  return tpmt(1 - +t);\n}\n\nexport function expOut(t) {\n  return 1 - tpmt(t);\n}\n\nexport function expInOut(t) {\n  return ((t *= 2) <= 1 ? tpmt(1 - t) : 2 - tpmt(t - 1)) / 2;\n}\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,WAAW;AAE9B,OAAO,SAASC,KAAKA,CAACC,CAAC,EAAE;EACvB,OAAOF,IAAI,CAAC,CAAC,GAAG,CAACE,CAAC,CAAC;AACrB;AAEA,OAAO,SAASC,MAAMA,CAACD,CAAC,EAAE;EACxB,OAAO,CAAC,GAAGF,IAAI,CAACE,CAAC,CAAC;AACpB;AAEA,OAAO,SAASE,QAAQA,CAACF,CAAC,EAAE;EAC1B,OAAO,CAAC,CAACA,CAAC,IAAI,CAAC,KAAK,CAAC,GAAGF,IAAI,CAAC,CAAC,GAAGE,CAAC,CAAC,GAAG,CAAC,GAAGF,IAAI,CAACE,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}