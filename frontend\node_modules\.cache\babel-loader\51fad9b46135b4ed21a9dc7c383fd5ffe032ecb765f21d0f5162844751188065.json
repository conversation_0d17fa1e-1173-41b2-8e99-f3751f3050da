{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Siren = createLucideIcon(\"Siren\", [[\"path\", {\n  d: \"M7 12a5 5 0 0 1 5-5v0a5 5 0 0 1 5 5v6H7v-6Z\",\n  key: \"rmc51c\"\n}], [\"path\", {\n  d: \"M5 20a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2H5v-2Z\",\n  key: \"yyvmjy\"\n}], [\"path\", {\n  d: \"M21 12h1\",\n  key: \"jtio3y\"\n}], [\"path\", {\n  d: \"M18.5 4.5 18 5\",\n  key: \"g5sp9y\"\n}], [\"path\", {\n  d: \"M2 12h1\",\n  key: \"1uaihz\"\n}], [\"path\", {\n  d: \"M12 2v1\",\n  key: \"11qlp1\"\n}], [\"path\", {\n  d: \"m4.929 4.929.707.707\",\n  key: \"1i51kw\"\n}], [\"path\", {\n  d: \"M12 12v6\",\n  key: \"3ahymv\"\n}]]);\nexport { <PERSON><PERSON> as default };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "createLucideIcon", "d", "key"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\siren.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Siren\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyAxMmE1IDUgMCAwIDEgNS01djBhNSA1IDAgMCAxIDUgNXY2SDd2LTZaIiAvPgogIDxwYXRoIGQ9Ik01IDIwYTIgMiAwIDAgMSAyLTJoMTBhMiAyIDAgMCAxIDIgMnYySDV2LTJaIiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmgxIiAvPgogIDxwYXRoIGQ9Ik0xOC41IDQuNSAxOCA1IiAvPgogIDxwYXRoIGQ9Ik0yIDEyaDEiIC8+CiAgPHBhdGggZD0iTTEyIDJ2MSIgLz4KICA8cGF0aCBkPSJtNC45MjkgNC45MjkuNzA3LjcwNyIgLz4KICA8cGF0aCBkPSJNMTIgMTJ2NiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/siren\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Siren = createLucideIcon('Siren', [\n  ['path', { d: 'M7 12a5 5 0 0 1 5-5v0a5 5 0 0 1 5 5v6H7v-6Z', key: 'rmc51c' }],\n  ['path', { d: 'M5 20a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v2H5v-2Z', key: 'yyvmjy' }],\n  ['path', { d: 'M21 12h1', key: 'jtio3y' }],\n  ['path', { d: 'M18.5 4.5 18 5', key: 'g5sp9y' }],\n  ['path', { d: 'M2 12h1', key: '1uaihz' }],\n  ['path', { d: 'M12 2v1', key: '11qlp1' }],\n  ['path', { d: 'm4.929 4.929.707.707', key: '1i51kw' }],\n  ['path', { d: 'M12 12v6', key: '3ahymv' }],\n]);\n\nexport default Siren;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,MAAQ;EAAED,CAAA,EAAG,8CAAgD;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7E,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}