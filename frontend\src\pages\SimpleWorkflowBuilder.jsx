import React, { useState } from 'react';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { 
  MessageCircle, 
  Database, 
  Brain, 
  Monitor,
  Save,
  Trash2,
  Play,
  Plus,
  Settings
} from 'lucide-react';

const SimpleWorkflowBuilder = () => {
  const [workflow, setWorkflow] = useState({
    nodes: [
      { id: 1, type: 'userQuery', label: 'User Query', config: { placeholder: 'Enter your question...' } },
      { id: 2, type: 'knowledgeBase', label: 'Knowledge Base', config: { documentId: null, maxResults: 5 } },
      { id: 3, type: 'llmEngine', label: 'LLM Engine', config: { model: 'gpt-3.5-turbo', temperature: 0.7 } },
      { id: 4, type: 'output', label: 'Output', config: { format: 'text', showSources: true } }
    ]
  });

  const [selectedNode, setSelectedNode] = useState(null);

  const nodeTypes = [
    {
      type: 'userQuery',
      label: 'User Query',
      description: 'Entry point for user questions',
      icon: MessageCircle,
      color: 'bg-blue-500',
    },
    {
      type: 'knowledgeBase',
      label: 'Knowledge Base',
      description: 'Upload PDFs and search context',
      icon: Database,
      color: 'bg-green-500',
    },
    {
      type: 'llmEngine',
      label: 'LLM Engine',
      description: 'AI model for generating responses',
      icon: Brain,
      color: 'bg-yellow-500',
    },
    {
      type: 'output',
      label: 'Output',
      description: 'Display final AI response',
      icon: Monitor,
      color: 'bg-red-500',
    },
  ];

  const addNode = (type) => {
    const newNode = {
      id: Date.now(),
      type,
      label: nodeTypes.find(nt => nt.type === type)?.label || 'New Node',
      config: getDefaultConfig(type)
    };
    setWorkflow(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode]
    }));
  };

  const getDefaultConfig = (type) => {
    const configs = {
      userQuery: { placeholder: 'Enter your question...' },
      knowledgeBase: { documentId: null, maxResults: 5 },
      llmEngine: { model: 'gpt-3.5-turbo', temperature: 0.7, maxTokens: 1000 },
      output: { format: 'text', showSources: true },
    };
    return configs[type] || {};
  };

  const updateNodeConfig = (nodeId, newConfig) => {
    setWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.map(node => 
        node.id === nodeId ? { ...node, config: { ...node.config, ...newConfig } } : node
      )
    }));
  };

  const removeNode = (nodeId) => {
    setWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.filter(node => node.id !== nodeId)
    }));
    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
    }
  };

  const clearWorkflow = () => {
    setWorkflow({ nodes: [] });
    setSelectedNode(null);
  };

  const saveWorkflow = () => {
    localStorage.setItem('workflow', JSON.stringify(workflow));
    alert('Workflow saved successfully!');
  };

  const executeWorkflow = () => {
    console.log('Executing workflow:', workflow);
    alert('Workflow execution started! Check the Chat interface for results.');
  };

  return (
    <div className="flex h-full bg-background">
      {/* Sidebar */}
      <div className="w-80 xl:w-96 bg-background border-r border-border flex flex-col shadow-lg">
        {/* Header */}
        <div className="p-4 border-b border-border">
          <h2 className="text-lg font-semibold text-foreground">Workflow Builder</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Build your AI workflow step by step
          </p>
        </div>

        {/* Node Types */}
        <div className="flex-1 p-4 space-y-3 overflow-y-auto">
          <h3 className="text-sm font-medium text-foreground mb-2">Available Components</h3>
          {nodeTypes.map((nodeType) => {
            const Icon = nodeType.icon;
            
            return (
              <Card
                key={nodeType.type}
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => addNode(nodeType.type)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${nodeType.color} text-white flex-shrink-0`}>
                      <Icon className="w-4 h-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-foreground text-sm">
                        {nodeType.label}
                      </h3>
                      <p className="text-xs text-muted-foreground mt-1">
                        {nodeType.description}
                      </p>
                    </div>
                    <Plus className="w-4 h-4 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Actions */}
        <div className="p-4 border-t border-border space-y-2">
          <Button onClick={executeWorkflow} className="w-full" size="sm">
            <Play className="w-4 h-4 mr-2" />
            Execute Workflow
          </Button>
          
          <div className="flex space-x-2">
            <Button onClick={saveWorkflow} variant="outline" size="sm" className="flex-1">
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
            
            <Button onClick={clearWorkflow} variant="outline" size="sm" className="flex-1">
              <Trash2 className="w-4 h-4 mr-2" />
              Clear
            </Button>
          </div>
        </div>
      </div>

      {/* Main Workflow Area */}
      <div className="flex-1 flex flex-col">
        {/* Workflow Canvas */}
        <div className="flex-1 p-6 overflow-auto">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-xl font-semibold mb-6">Current Workflow</h2>
            
            {workflow.nodes.length === 0 ? (
              <div className="text-center py-12">
                <Settings className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">
                  No Components Added
                </h3>
                <p className="text-sm text-muted-foreground">
                  Click on components in the sidebar to add them to your workflow
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {workflow.nodes.map((node, index) => {
                  const nodeType = nodeTypes.find(nt => nt.type === node.type);
                  const Icon = nodeType?.icon || Settings;
                  
                  return (
                    <div key={node.id} className="relative">
                      {index > 0 && (
                        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-0.5 h-4 bg-border"></div>
                      )}
                      
                      <Card 
                        className={`cursor-pointer transition-all ${
                          selectedNode?.id === node.id ? 'ring-2 ring-primary' : ''
                        }`}
                        onClick={() => setSelectedNode(node)}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className={`p-2 rounded-lg ${nodeType?.color || 'bg-gray-500'} text-white`}>
                                <Icon className="w-4 h-4" />
                              </div>
                              <div>
                                <CardTitle className="text-base">{node.label}</CardTitle>
                                <CardDescription>{nodeType?.description}</CardDescription>
                              </div>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                removeNode(node.id);
                              }}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </CardHeader>
                        
                        <CardContent>
                          <div className="text-sm text-muted-foreground">
                            {Object.entries(node.config).map(([key, value]) => (
                              <div key={key} className="flex justify-between">
                                <span className="capitalize">{key}:</span>
                                <span>{String(value)}</span>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                      
                      {index < workflow.nodes.length - 1 && (
                        <div className="flex justify-center py-2">
                          <div className="w-0.5 h-6 bg-border"></div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Configuration Panel */}
      {selectedNode && (
        <div className="w-80 xl:w-96 bg-background border-l border-border flex flex-col shadow-lg">
          <div className="p-4 border-b border-border">
            <h3 className="text-lg font-semibold text-foreground">Configure Node</h3>
            <p className="text-sm text-muted-foreground">{selectedNode.label}</p>
          </div>
          
          <div className="flex-1 p-4 overflow-y-auto">
            <div className="space-y-4">
              {Object.entries(selectedNode.config).map(([key, value]) => (
                <div key={key}>
                  <label className="text-sm font-medium text-foreground capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </label>
                  <Input
                    value={value || ''}
                    onChange={(e) => updateNodeConfig(selectedNode.id, { [key]: e.target.value })}
                    className="mt-1"
                    placeholder={`Enter ${key}...`}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleWorkflowBuilder;
