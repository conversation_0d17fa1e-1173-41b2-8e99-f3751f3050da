{"ast": null, "code": "// Given something array like (or null), returns something that is strictly an\n// array. This is used to ensure that array-like objects passed to d3.selectAll\n// or selection.selectAll are converted into proper arrays when creating a\n// selection; we don’t ever want to create a selection backed by a live\n// HTMLCollection or NodeList. However, note that selection.selectAll will use a\n// static NodeList as a group, since it safely derived from querySelectorAll.\nexport default function array(x) {\n  return x == null ? [] : Array.isArray(x) ? x : Array.from(x);\n}", "map": {"version": 3, "names": ["array", "x", "Array", "isArray", "from"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/array.js"], "sourcesContent": ["// Given something array like (or null), returns something that is strictly an\n// array. This is used to ensure that array-like objects passed to d3.selectAll\n// or selection.selectAll are converted into proper arrays when creating a\n// selection; we don’t ever want to create a selection backed by a live\n// HTMLCollection or NodeList. However, note that selection.selectAll will use a\n// static NodeList as a group, since it safely derived from querySelectorAll.\nexport default function array(x) {\n  return x == null ? [] : Array.isArray(x) ? x : Array.from(x);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,KAAKA,CAACC,CAAC,EAAE;EAC/B,OAAOA,CAAC,IAAI,IAAI,GAAG,EAAE,GAAGC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,GAAGA,CAAC,GAAGC,KAAK,CAACE,IAAI,CAACH,CAAC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}