import React, { memo, useState, useEffect } from 'react';
import { <PERSON><PERSON>, Position, useReactFlow } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Textarea } from '../../ui/textarea';
import { Monitor, Copy, RotateCcw, CheckCircle } from 'lucide-react';

const OutputNode = ({ id, data, selected }) => {
  const { setNodes } = useReactFlow();
  const [response, setResponse] = useState(data?.config?.response || '');
  const [showSources, setShowSources] = useState(data?.config?.showSources || false);
  const [copied, setCopied] = useState(false);

  // Update node data when config changes
  useEffect(() => {
    const updateNodeData = () => {
      setNodes((nodes) =>
        nodes.map((node) =>
          node.id === id
            ? {
                ...node,
                data: {
                  ...node.data,
                  config: {
                    ...node.data.config,
                    response,
                    showSources,
                  },
                },
              }
            : node
        )
      );
    };

    const debounceTimer = setTimeout(updateNodeData, 300);
    return () => clearTimeout(debounceTimer);
  }, [response, showSources, id, setNodes]);

  const handleCopy = async () => {
    if (response) {
      try {
        await navigator.clipboard.writeText(response);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy text: ', err);
      }
    }
  };

  const handleReset = () => {
    setResponse('');
  };

  return (
    <Card className={`min-w-[320px] max-w-[400px] ${selected ? 'ring-2 ring-gray-500' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-1.5 rounded-lg bg-gray-500 text-white">
              <Monitor className="w-4 h-4" />
            </div>
            <CardTitle className="text-sm">Output</CardTitle>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}
              disabled={!response}
              className="h-6 w-6 p-0"
            >
              {copied ? <CheckCircle className="w-3 h-3 text-green-500" /> : <Copy className="w-3 h-3" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleReset}
              disabled={!response}
              className="h-6 w-6 p-0"
            >
              <RotateCcw className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0 space-y-3">
        {/* Response Display */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-700">
            Generated Response
          </label>
          <Textarea
            placeholder="AI response will appear here..."
            value={response}
            onChange={(e) => setResponse(e.target.value)}
            className="text-sm resize-none min-h-[120px]"
            readOnly={false} // Allow manual editing for testing
          />
        </div>

        {/* Show Sources Toggle */}
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id={`showSources-${id}`}
            checked={showSources}
            onChange={(e) => setShowSources(e.target.checked)}
            className="rounded border-gray-300 text-gray-600 focus:ring-gray-500"
          />
          <label htmlFor={`showSources-${id}`} className="text-xs text-gray-700">
            Show source references
          </label>
        </div>

        {/* Status */}
        <div className="bg-gray-50 p-2 rounded text-xs">
          <div className="font-medium text-gray-800 mb-1">Status:</div>
          <div className="text-gray-700 space-y-1">
            <div>Response: {response ? `${response.length} characters` : 'Empty'}</div>
            <div>Sources: {showSources ? 'Enabled' : 'Disabled'}</div>
          </div>
        </div>
      </CardContent>

      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="response"
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(OutputNode);
