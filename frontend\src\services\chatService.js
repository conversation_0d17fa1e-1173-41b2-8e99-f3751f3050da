import { apiGet, apiPost, apiDelete, handleApiError } from './api';

/**
 * Chat Service
 * Handles all chat-related API operations including message storage, history, and management
 */

/**
 * Save a chat message to the backend
 * @param {Object} params - Message parameters
 * @param {string} params.userId - ID of the user
 * @param {string} params.query - User's query
 * @param {string} params.response - AI's response
 * @param {number} params.documentId - Optional document ID
 * @param {Object} params.workflowConfig - Optional workflow configuration
 * @returns {Promise<Object>} Save response
 */
export const saveChatMessage = async (params) => {
  try {
    const { userId, query, response, documentId = null, workflowConfig = null } = params;
    
    // Validate required parameters
    if (!userId) {
      throw new Error('User ID is required');
    }
    
    if (!query || query.trim().length === 0) {
      throw new Error('Query is required');
    }
    
    if (!response || response.trim().length === 0) {
      throw new Error('Response is required');
    }
    
    const messageData = {
      user_id: userId,
      query: query.trim(),
      response: response.trim(),
    };
    
    if (documentId) {
      messageData.document_id = documentId;
    }
    
    if (workflowConfig) {
      messageData.workflow_config = workflowConfig;
    }
    
    const apiResponse = await apiPost('/chat/save', messageData);
    
    return {
      success: true,
      data: apiResponse,
      message: 'Chat message saved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get chat history for a user
 * @param {Object} params - Query parameters
 * @param {string} params.userId - ID of the user
 * @param {number} params.skip - Number of messages to skip
 * @param {number} params.limit - Maximum number of messages to return
 * @param {number} params.documentId - Optional document ID filter
 * @param {number} params.days - Optional filter by number of days back
 * @returns {Promise<Object>} Chat history
 */
export const getChatHistory = async (params) => {
  try {
    const { userId, skip = 0, limit = 50, documentId = null, days = null } = params;
    
    if (!userId) {
      throw new Error('User ID is required');
    }
    
    const queryParams = { skip, limit };
    
    if (documentId) {
      queryParams.document_id = documentId;
    }
    
    if (days) {
      queryParams.days = days;
    }
    
    const response = await apiGet(`/chat/history/${userId}`, queryParams);
    
    return {
      success: true,
      data: response,
      message: 'Chat history retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get a specific chat message by ID
 * @param {number} messageId - ID of the message
 * @returns {Promise<Object>} Message details
 */
export const getChatMessage = async (messageId) => {
  try {
    if (!messageId) {
      throw new Error('Message ID is required');
    }
    
    const response = await apiGet(`/chat/message/${messageId}`);
    
    return {
      success: true,
      data: response,
      message: 'Chat message retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Delete a specific chat message
 * @param {number} messageId - ID of the message to delete
 * @returns {Promise<Object>} Deletion response
 */
export const deleteChatMessage = async (messageId) => {
  try {
    if (!messageId) {
      throw new Error('Message ID is required');
    }
    
    const response = await apiDelete(`/chat/message/${messageId}`);
    
    return {
      success: true,
      data: response,
      message: 'Chat message deleted successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Clear chat history for a user
 * @param {Object} params - Clear parameters
 * @param {string} params.userId - ID of the user
 * @param {number} params.documentId - Optional document ID filter
 * @param {number} params.days - Optional filter to clear messages older than N days
 * @returns {Promise<Object>} Clear response
 */
export const clearChatHistory = async (params) => {
  try {
    const { userId, documentId = null, days = null } = params;
    
    if (!userId) {
      throw new Error('User ID is required');
    }
    
    const queryParams = {};
    
    if (documentId) {
      queryParams.document_id = documentId;
    }
    
    if (days) {
      queryParams.days = days;
    }
    
    const response = await apiDelete(`/chat/history/${userId}`, queryParams);
    
    return {
      success: true,
      data: response,
      message: 'Chat history cleared successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get chat statistics for a user
 * @param {string} userId - ID of the user
 * @returns {Promise<Object>} User chat statistics
 */
export const getUserChatStats = async (userId) => {
  try {
    if (!userId) {
      throw new Error('User ID is required');
    }
    
    const response = await apiGet(`/chat/stats/${userId}`);
    
    return {
      success: true,
      data: response,
      message: 'User chat statistics retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get global chat statistics
 * @returns {Promise<Object>} Global chat statistics
 */
export const getGlobalChatStats = async () => {
  try {
    const response = await apiGet('/chat/stats');
    
    return {
      success: true,
      data: response,
      message: 'Global chat statistics retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Format chat messages for display
 * @param {Array} messages - Raw chat messages from API
 * @returns {Array} Formatted messages
 */
export const formatChatMessages = (messages) => {
  if (!Array.isArray(messages)) {
    return [];
  }
  
  return messages.map(message => ({
    id: message.id,
    type: 'user',
    content: message.query,
    timestamp: new Date(message.timestamp),
    response: {
      id: `${message.id}_response`,
      type: 'ai',
      content: message.response,
      timestamp: new Date(message.timestamp),
    },
    documentId: message.document_id,
    workflowConfig: message.workflow_config ? JSON.parse(message.workflow_config) : null,
  }));
};

/**
 * Convert chat messages to conversation format
 * @param {Array} messages - Formatted chat messages
 * @returns {Array} Conversation messages
 */
export const toConversationFormat = (messages) => {
  const conversation = [];
  
  messages.forEach(message => {
    // Add user message
    conversation.push({
      id: message.id,
      type: 'user',
      content: message.content,
      timestamp: message.timestamp,
    });
    
    // Add AI response
    if (message.response) {
      conversation.push({
        id: message.response.id,
        type: 'ai',
        content: message.response.content,
        timestamp: message.response.timestamp,
      });
    }
  });
  
  return conversation.sort((a, b) => a.timestamp - b.timestamp);
};

/**
 * Search chat history
 * @param {Array} messages - Chat messages to search
 * @param {string} searchTerm - Term to search for
 * @returns {Array} Filtered messages
 */
export const searchChatHistory = (messages, searchTerm) => {
  if (!searchTerm || searchTerm.trim().length === 0) {
    return messages;
  }
  
  const term = searchTerm.toLowerCase().trim();
  
  return messages.filter(message => 
    message.content.toLowerCase().includes(term) ||
    (message.response && message.response.content.toLowerCase().includes(term))
  );
};

/**
 * Get chat statistics from messages
 * @param {Array} messages - Chat messages
 * @returns {Object} Statistics
 */
export const getChatStatistics = (messages) => {
  if (!Array.isArray(messages) || messages.length === 0) {
    return {
      totalMessages: 0,
      totalConversations: 0,
      averageResponseLength: 0,
      averageQueryLength: 0,
    };
  }
  
  const totalMessages = messages.length;
  const totalQueryLength = messages.reduce((sum, msg) => sum + msg.content.length, 0);
  const totalResponseLength = messages.reduce((sum, msg) => 
    sum + (msg.response ? msg.response.content.length : 0), 0
  );
  
  return {
    totalMessages,
    totalConversations: totalMessages,
    averageQueryLength: Math.round(totalQueryLength / totalMessages),
    averageResponseLength: Math.round(totalResponseLength / totalMessages),
  };
};

export default {
  saveChatMessage,
  getChatHistory,
  getChatMessage,
  deleteChatMessage,
  clearChatHistory,
  getUserChatStats,
  getGlobalChatStats,
  formatChatMessages,
  toConversationFormat,
  searchChatHistory,
  getChatStatistics,
};
