{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst FileImage = createLucideIcon(\"FileImage\", [[\"path\", {\n  d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\",\n  key: \"1nnpy2\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"13\",\n  r: \"2\",\n  key: \"6v46hv\"\n}], [\"path\", {\n  d: \"m20 17-1.09-1.09a2 2 0 0 0-2.82 0L10 22\",\n  key: \"17vly1\"\n}]]);\nexport { FileImage as default };", "map": {"version": 3, "names": ["FileImage", "createLucideIcon", "d", "key", "points", "cx", "cy", "r"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\file-image.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileImage\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWNy41TDE0LjUgMnoiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPGNpcmNsZSBjeD0iMTAiIGN5PSIxMyIgcj0iMiIgLz4KICA8cGF0aCBkPSJtMjAgMTctMS4wOS0xLjA5YTIgMiAwIDAgMC0yLjgyIDBMMTAgMjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-image\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileImage = createLucideIcon('FileImage', [\n  [\n    'path',\n    { d: 'M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z', key: '1nnpy2' },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['circle', { cx: '10', cy: '13', r: '2', key: '6v46hv' }],\n  ['path', { d: 'm20 17-1.09-1.09a2 2 0 0 0-2.82 0L10 22', key: '17vly1' }],\n]);\n\nexport default FileImage;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CACE,QACA;EAAEC,CAAA,EAAG,uEAAyE;EAAAC,GAAA,EAAK;AAAS,EAC9F,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,QAAU;EAAEE,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKJ,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}