{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Tally5 = createLucideIcon(\"Tally5\", [[\"path\", {\n  d: \"M4 4v16\",\n  key: \"6qkkli\"\n}], [\"path\", {\n  d: \"M9 4v16\",\n  key: \"81ygyz\"\n}], [\"path\", {\n  d: \"M14 4v16\",\n  key: \"12vmem\"\n}], [\"path\", {\n  d: \"M19 4v16\",\n  key: \"8ij5ei\"\n}], [\"path\", {\n  d: \"M22 6 2 18\",\n  key: \"h9moai\"\n}]]);\nexport { Tally5 as default };", "map": {"version": 3, "names": ["Tally5", "createLucideIcon", "d", "key"], "sources": ["D:\\assignment for AI planet\\aiplanet\\frontend\\node_modules\\lucide-react\\src\\icons\\tally-5.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Tally5\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCA0djE2IiAvPgogIDxwYXRoIGQ9Ik05IDR2MTYiIC8+CiAgPHBhdGggZD0iTTE0IDR2MTYiIC8+CiAgPHBhdGggZD0iTTE5IDR2MTYiIC8+CiAgPHBhdGggZD0iTTIyIDYgMiAxOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/tally-5\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tally5 = createLucideIcon('Tally5', [\n  ['path', { d: 'M4 4v16', key: '6qkkli' }],\n  ['path', { d: 'M9 4v16', key: '81ygyz' }],\n  ['path', { d: 'M14 4v16', key: '12vmem' }],\n  ['path', { d: 'M19 4v16', key: '8ij5ei' }],\n  ['path', { d: 'M22 6 2 18', key: 'h9moai' }],\n]);\n\nexport default Tally5;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}