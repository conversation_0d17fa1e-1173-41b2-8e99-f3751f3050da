import React, { memo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'reactflow';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Brain, Settings, Globe, Zap } from 'lucide-react';

const LLMEngineNode = ({ data, selected }) => {
  return (
    <Card className={`min-w-[200px] ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-1.5 rounded-lg bg-yellow-500 text-white">
              <Brain className="w-4 h-4" />
            </div>
            <CardTitle className="text-sm">LLM Engine</CardTitle>
          </div>
          <Settings className="w-4 h-4 text-muted-foreground" />
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">
            AI model for generating responses
          </div>
          
          <div className="bg-muted/50 p-2 rounded text-xs">
            <div className="font-medium">Configuration:</div>
            <div>Model: {data.config?.model || 'gpt-3.5-turbo'}</div>
            <div>Temperature: {data.config?.temperature || 0.7}</div>
            <div>Max Tokens: {data.config?.maxTokens || 1000}</div>
            
            {data.config?.enableWebSearch && (
              <div className="flex items-center space-x-1 mt-1">
                <Globe className="w-3 h-3" />
                <span>Web Search Enabled</span>
              </div>
            )}
            
            <div className="flex items-center space-x-1 mt-1">
              <Zap className="w-3 h-3" />
              <span>OpenAI GPT</span>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Input Handles */}
      <Handle
        type="target"
        position={Position.Left}
        id="query"
        style={{ top: '30%' }}
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />
      
      <Handle
        type="target"
        position={Position.Left}
        id="context"
        style={{ top: '70%' }}
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />

      {/* Output Handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="response"
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />
    </Card>
  );
};

export default memo(LLMEngineNode);
