import React from 'react';
import { Handle, Position } from 'reactflow';
import { Card, CardContent } from '../ui/card';
import { Monitor } from 'lucide-react';

const OutputNode = ({ data, selected }) => {
  return (
    <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-red-500 text-white rounded-lg">
            <Monitor className="w-4 h-4" />
          </div>
          <div className="flex-1">
            <h3 className="font-medium text-foreground text-sm">
              {data.label}
            </h3>
            <p className="text-xs text-muted-foreground mt-1">
              Display final AI response
            </p>
          </div>
        </div>
        
        <div className="mt-3 space-y-2">
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Format:</span>
            <span className="text-foreground">
              {data.config?.format || 'text'}
            </span>
          </div>
          {data.config?.showSources !== false && (
            <div className="text-xs text-green-600">
              ✓ Show Sources
            </div>
          )}
        </div>
        
        <div className="mt-3">
          <div className="text-xs text-muted-foreground mb-1">Preview:</div>
          <div className="p-2 bg-muted rounded text-xs">
            AI response will appear here...
          </div>
        </div>
      </CardContent>
      
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </Card>
  );
};

export default OutputNode;
