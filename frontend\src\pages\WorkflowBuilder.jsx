import React, { useState, useCallback, useRef } from 'react';
import React<PERSON>low, {
  ReactFlowProvider,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
} from 'reactflow';
import 'reactflow/dist/style.css';

import Sidebar from '../components/layout/Sidebar';
import ConfigPanel from '../components/layout/ConfigPanel';
import UserQueryNode from '../components/nodes/UserQueryNode';
import KnowledgeBaseNode from '../components/nodes/KnowledgeBaseNode';
import LLMEngineNode from '../components/nodes/LLMEngineNode';
import OutputNode from '../components/nodes/OutputNode';

// Define custom node types
const nodeTypes = {
  userQuery: UserQueryNode,
  knowledgeBase: KnowledgeBaseNode,
  llmEngine: LLMEngineNode,
  output: OutputNode,
};

// Initial nodes for demonstration
const initialNodes = [
  {
    id: '1',
    type: 'userQuery',
    position: { x: 100, y: 100 },
    data: { label: 'User Query' },
  },
];

const initialEdges = [];

const WorkflowBuilder = () => {
  const reactFlowWrapper = useRef(null);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);

  // Handle connection between nodes
  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // Handle drag over for drop functionality
  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  // Handle drop of new nodes
  const onDrop = useCallback(
    (event) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      // Check if the dropped element is valid
      if (typeof type === 'undefined' || !type) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode = {
        id: `${type}-${Date.now()}`,
        type,
        position,
        data: { 
          label: getNodeLabel(type),
          config: getDefaultConfig(type)
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  // Get node label based on type
  const getNodeLabel = (type) => {
    const labels = {
      userQuery: 'User Query',
      knowledgeBase: 'Knowledge Base',
      llmEngine: 'LLM Engine',
      output: 'Output',
    };
    return labels[type] || 'Unknown Node';
  };

  // Get default configuration for node types
  const getDefaultConfig = (type) => {
    const configs = {
      userQuery: {
        placeholder: 'Enter your question...',
      },
      knowledgeBase: {
        documentId: null,
        maxResults: 5,
      },
      llmEngine: {
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 1000,
        enableWebSearch: false,
      },
      output: {
        format: 'text',
        showSources: true,
      },
    };
    return configs[type] || {};
  };

  // Handle node selection
  const onNodeClick = useCallback((event, node) => {
    setSelectedNode(node);
  }, []);

  // Handle node configuration update
  const onNodeConfigUpdate = useCallback((nodeId, newConfig) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, config: newConfig } }
          : node
      )
    );
  }, [setNodes]);

  // Clear workflow
  const clearWorkflow = useCallback(() => {
    setNodes([]);
    setEdges([]);
    setSelectedNode(null);
  }, [setNodes, setEdges]);

  // Save workflow
  const saveWorkflow = useCallback(() => {
    const workflow = {
      nodes: nodes.map(node => ({
        id: node.id,
        type: node.type,
        position: node.position,
        data: node.data,
      })),
      edges: edges.map(edge => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        sourceHandle: edge.sourceHandle,
        targetHandle: edge.targetHandle,
      })),
    };
    
    console.log('Workflow saved:', workflow);
    // Here you would typically save to backend or localStorage
    localStorage.setItem('workflow', JSON.stringify(workflow));
  }, [nodes, edges]);

  return (
    <div className="flex h-full bg-background">
      {/* Sidebar - Hidden on mobile, collapsible on tablet */}
      <div className="hidden lg:block">
        <Sidebar onClearWorkflow={clearWorkflow} onSaveWorkflow={saveWorkflow} />
      </div>

      {/* Main Canvas */}
      <div className="flex-1 relative min-w-0">
        <ReactFlowProvider>
          <div className="w-full h-full" ref={reactFlowWrapper}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onInit={setReactFlowInstance}
              onDrop={onDrop}
              onDragOver={onDragOver}
              onNodeClick={onNodeClick}
              nodeTypes={nodeTypes}
              fitView
              className="bg-background"
            >
              <Controls className="bg-background border border-border shadow-lg" />
              <MiniMap
                className="bg-background border border-border shadow-lg"
                nodeColor={(node) => {
                  const colors = {
                    userQuery: '#3b82f6',
                    knowledgeBase: '#10b981',
                    llmEngine: '#f59e0b',
                    output: '#ef4444',
                  };
                  return colors[node.type] || '#6b7280';
                }}
              />
              <Background
                variant={BackgroundVariant.Dots}
                gap={20}
                size={1}
                className="bg-background"
              />
            </ReactFlow>
          </div>
        </ReactFlowProvider>
      </div>

      {/* Configuration Panel - Hidden on mobile when no node selected */}
      <div className={`${selectedNode ? 'block' : 'hidden lg:block'}`}>
        <ConfigPanel
          selectedNode={selectedNode}
          onConfigUpdate={onNodeConfigUpdate}
          onClose={() => setSelectedNode(null)}
        />
      </div>
    </div>
  );
};

export default WorkflowBuilder;
