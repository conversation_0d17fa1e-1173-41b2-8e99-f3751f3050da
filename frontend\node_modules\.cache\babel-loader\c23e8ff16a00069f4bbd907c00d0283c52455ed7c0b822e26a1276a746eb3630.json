{"ast": null, "code": "function empty() {\n  return [];\n}\nexport default function (selector) {\n  return selector == null ? empty : function () {\n    return this.querySelectorAll(selector);\n  };\n}", "map": {"version": 3, "names": ["empty", "selector", "querySelectorAll"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/selectorAll.js"], "sourcesContent": ["function empty() {\n  return [];\n}\n\nexport default function(selector) {\n  return selector == null ? empty : function() {\n    return this.querySelectorAll(selector);\n  };\n}\n"], "mappings": "AAAA,SAASA,KAAKA,CAAA,EAAG;EACf,OAAO,EAAE;AACX;AAEA,eAAe,UAASC,QAAQ,EAAE;EAChC,OAAOA,QAAQ,IAAI,IAAI,GAAGD,KAAK,GAAG,YAAW;IAC3C,OAAO,IAAI,CAACE,gBAAgB,CAACD,QAAQ,CAAC;EACxC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}