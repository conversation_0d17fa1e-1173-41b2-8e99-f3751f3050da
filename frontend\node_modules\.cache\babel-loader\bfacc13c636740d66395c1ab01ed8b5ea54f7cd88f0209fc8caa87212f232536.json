{"ast": null, "code": "import defaultView from \"../window.js\";\nfunction styleRemove(name) {\n  return function () {\n    this.style.removeProperty(name);\n  };\n}\nfunction styleConstant(name, value, priority) {\n  return function () {\n    this.style.setProperty(name, value, priority);\n  };\n}\nfunction styleFunction(name, value, priority) {\n  return function () {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);else this.style.setProperty(name, v, priority);\n  };\n}\nexport default function (name, value, priority) {\n  return arguments.length > 1 ? this.each((value == null ? styleRemove : typeof value === \"function\" ? styleFunction : styleConstant)(name, value, priority == null ? \"\" : priority)) : styleValue(this.node(), name);\n}\nexport function styleValue(node, name) {\n  return node.style.getPropertyValue(name) || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}", "map": {"version": 3, "names": ["defaultView", "styleRemove", "name", "style", "removeProperty", "styleConstant", "value", "priority", "setProperty", "styleFunction", "v", "apply", "arguments", "length", "each", "styleValue", "node", "getPropertyValue", "getComputedStyle"], "sources": ["D:/assignment for AI planet/aiplanet/node_modules/d3-selection/src/selection/style.js"], "sourcesContent": ["import defaultView from \"../window.js\";\n\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\n\nfunction styleConstant(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\n\nfunction styleFunction(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) this.style.removeProperty(name);\n    else this.style.setProperty(name, v, priority);\n  };\n}\n\nexport default function(name, value, priority) {\n  return arguments.length > 1\n      ? this.each((value == null\n            ? styleRemove : typeof value === \"function\"\n            ? styleFunction\n            : styleConstant)(name, value, priority == null ? \"\" : priority))\n      : styleValue(this.node(), name);\n}\n\nexport function styleValue(node, name) {\n  return node.style.getPropertyValue(name)\n      || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,cAAc;AAEtC,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,OAAO,YAAW;IAChB,IAAI,CAACC,KAAK,CAACC,cAAc,CAACF,IAAI,CAAC;EACjC,CAAC;AACH;AAEA,SAASG,aAAaA,CAACH,IAAI,EAAEI,KAAK,EAAEC,QAAQ,EAAE;EAC5C,OAAO,YAAW;IAChB,IAAI,CAACJ,KAAK,CAACK,WAAW,CAACN,IAAI,EAAEI,KAAK,EAAEC,QAAQ,CAAC;EAC/C,CAAC;AACH;AAEA,SAASE,aAAaA,CAACP,IAAI,EAAEI,KAAK,EAAEC,QAAQ,EAAE;EAC5C,OAAO,YAAW;IAChB,IAAIG,CAAC,GAAGJ,KAAK,CAACK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAIF,CAAC,IAAI,IAAI,EAAE,IAAI,CAACP,KAAK,CAACC,cAAc,CAACF,IAAI,CAAC,CAAC,KAC1C,IAAI,CAACC,KAAK,CAACK,WAAW,CAACN,IAAI,EAAEQ,CAAC,EAAEH,QAAQ,CAAC;EAChD,CAAC;AACH;AAEA,eAAe,UAASL,IAAI,EAAEI,KAAK,EAAEC,QAAQ,EAAE;EAC7C,OAAOK,SAAS,CAACC,MAAM,GAAG,CAAC,GACrB,IAAI,CAACC,IAAI,CAAC,CAACR,KAAK,IAAI,IAAI,GAClBL,WAAW,GAAG,OAAOK,KAAK,KAAK,UAAU,GACzCG,aAAa,GACbJ,aAAa,EAAEH,IAAI,EAAEI,KAAK,EAAEC,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ,CAAC,CAAC,GACpEQ,UAAU,CAAC,IAAI,CAACC,IAAI,CAAC,CAAC,EAAEd,IAAI,CAAC;AACrC;AAEA,OAAO,SAASa,UAAUA,CAACC,IAAI,EAAEd,IAAI,EAAE;EACrC,OAAOc,IAAI,CAACb,KAAK,CAACc,gBAAgB,CAACf,IAAI,CAAC,IACjCF,WAAW,CAACgB,IAAI,CAAC,CAACE,gBAAgB,CAACF,IAAI,EAAE,IAAI,CAAC,CAACC,gBAAgB,CAACf,IAAI,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}