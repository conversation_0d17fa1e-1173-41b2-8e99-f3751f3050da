# =============================================================================
# GenAI Workflow Builder - Docker Setup Script (Windows)
# =============================================================================

Write-Host "🚀 Setting up GenAI Workflow Builder with Docker..." -ForegroundColor Green

# Check if Docker is installed
try {
    docker --version | Out-Null
} catch {
    Write-Host "❌ Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is installed
try {
    docker-compose --version | Out-Null
} catch {
    Write-Host "❌ Docker Compose is not installed. Please install Docker Compose first." -ForegroundColor Red
    exit 1
}

# Create .env file if it doesn't exist
if (!(Test-Path ".env.docker")) {
    Write-Host "📝 Creating .env.docker file..." -ForegroundColor Yellow
    Copy-Item ".env.docker.example" ".env.docker" -ErrorAction SilentlyContinue
    Write-Host "⚠️  Please edit .env.docker and add your OpenAI API key!" -ForegroundColor Yellow
    Write-Host "   OPENAI_API_KEY=your_openai_api_key_here" -ForegroundColor Cyan
}

# Create uploads directory
New-Item -ItemType Directory -Force -Path "uploads" | Out-Null
New-Item -ItemType Directory -Force -Path "backend/uploads" | Out-Null

Write-Host "🐳 Starting Docker services..." -ForegroundColor Green

# Start only infrastructure services first
Write-Host "📦 Starting PostgreSQL and Qdrant..." -ForegroundColor Blue
docker-compose up -d postgres qdrant

# Wait for services to be healthy
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check if services are healthy
Write-Host "🔍 Checking service health..." -ForegroundColor Blue
docker-compose ps

Write-Host "✅ Infrastructure services are running!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Service URLs:" -ForegroundColor Cyan
Write-Host "   🐘 PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "   🔍 Qdrant: http://localhost:6333" -ForegroundColor White
Write-Host "   🔧 pgAdmin: http://localhost:5050 (<EMAIL> / admin123)" -ForegroundColor White
Write-Host ""
Write-Host "🚀 To start the full application stack:" -ForegroundColor Green
Write-Host "   docker-compose --profile full-stack up -d" -ForegroundColor Cyan
Write-Host ""
Write-Host "🛑 To stop all services:" -ForegroundColor Yellow
Write-Host "   docker-compose down" -ForegroundColor Cyan
Write-Host ""
Write-Host "🗑️  To remove all data (CAUTION!):" -ForegroundColor Red
Write-Host "   docker-compose down -v" -ForegroundColor Cyan
