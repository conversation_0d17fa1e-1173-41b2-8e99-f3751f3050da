import React from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { 
  MessageCircle, 
  Database, 
  Brain, 
  Monitor,
  Save,
  Trash2,
  Play
} from 'lucide-react';

const Sidebar = ({ onClearWorkflow, onSaveWorkflow }) => {
  // Node types configuration
  const nodeTypes = [
    {
      type: 'userQuery',
      label: 'User Query',
      description: 'Entry point for user questions',
      icon: MessageCircle,
      color: 'bg-blue-500',
    },
    {
      type: 'knowledgeBase',
      label: 'Knowledge Base',
      description: 'Upload PDFs and search context',
      icon: Database,
      color: 'bg-green-500',
    },
    {
      type: 'llmEngine',
      label: 'LLM Engine',
      description: 'AI model for generating responses',
      icon: Brain,
      color: 'bg-yellow-500',
    },
    {
      type: 'output',
      label: 'Output',
      description: 'Display final AI response',
      icon: Monitor,
      color: 'bg-red-500',
    },
  ];

  // Handle drag start for node types
  const onDragStart = (event, nodeType) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };

  // Execute workflow (placeholder)
  const executeWorkflow = () => {
    console.log('Executing workflow...');
    // This would trigger workflow execution
  };

  return (
    <div className="w-80 xl:w-96 bg-background border-r border-border flex flex-col shadow-lg">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <h2 className="text-lg font-semibold text-foreground">Workflow Components</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Drag components to the canvas to build your workflow
        </p>
      </div>

      {/* Node Types */}
      <div className="flex-1 p-4 space-y-3 overflow-y-auto">
        {nodeTypes.map((nodeType) => {
          const Icon = nodeType.icon;
          
          return (
            <Card
              key={nodeType.type}
              className="cursor-grab active:cursor-grabbing hover:shadow-md transition-shadow"
              draggable
              onDragStart={(event) => onDragStart(event, nodeType.type)}
            >
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${nodeType.color} text-white flex-shrink-0`}>
                    <Icon className="w-4 h-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-foreground text-sm">
                      {nodeType.label}
                    </h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      {nodeType.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Workflow Actions */}
      <div className="p-4 border-t border-border space-y-2">
        <Button 
          onClick={executeWorkflow}
          className="w-full"
          size="sm"
        >
          <Play className="w-4 h-4 mr-2" />
          Execute Workflow
        </Button>
        
        <div className="flex space-x-2">
          <Button 
            onClick={onSaveWorkflow}
            variant="outline"
            size="sm"
            className="flex-1"
          >
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          
          <Button 
            onClick={onClearWorkflow}
            variant="outline"
            size="sm"
            className="flex-1"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Clear
          </Button>
        </div>
      </div>

      {/* Instructions */}
      <div className="p-4 bg-muted/50">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Quick Start</CardTitle>
          </CardHeader>
          <CardContent className="text-xs text-muted-foreground space-y-1">
            <p>1. Drag components to canvas</p>
            <p>2. Connect nodes with edges</p>
            <p>3. Configure node settings</p>
            <p>4. Execute your workflow</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Sidebar;
