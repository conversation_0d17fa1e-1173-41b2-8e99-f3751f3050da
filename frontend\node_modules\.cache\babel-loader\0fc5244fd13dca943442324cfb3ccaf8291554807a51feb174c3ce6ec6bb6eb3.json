{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\aiplanet\\\\frontend\\\\src\\\\pages\\\\ChatUI.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '../components/ui/button';\nimport { Input } from '../components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Send, Loader2, User, Bot, Settings, MessageSquare } from 'lucide-react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatUI = () => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [userId] = useState('user-' + Date.now()); // Simple user ID generation\n  const messagesEndRef = useRef(null);\n\n  // Sample workflow configuration (would come from WorkflowBuilder)\n  const sampleWorkflow = {\n    nodes: [{\n      id: 1,\n      type: 'userQuery',\n      label: 'User Query',\n      config: {\n        placeholder: 'Ask me anything...'\n      }\n    }, {\n      id: 2,\n      type: 'knowledgeBase',\n      label: 'Knowledge Base',\n      config: {\n        documentId: null,\n        maxResults: 5\n      }\n    }, {\n      id: 3,\n      type: 'llmEngine',\n      label: 'LLM Engine',\n      config: {\n        model: 'gpt-3.5-turbo',\n        temperature: 0.7,\n        maxTokens: 1000,\n        enableWebSearch: false\n      }\n    }, {\n      id: 4,\n      type: 'output',\n      label: 'Output',\n      config: {\n        format: 'text',\n        showSources: true\n      }\n    }]\n  };\n\n  // Scroll to bottom when new messages are added\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n    try {\n      // Call workflow execution API\n      const response = await axios.post('http://localhost:8000/api/workflow/execute', {\n        user_id: userId,\n        query: inputMessage,\n        workflow: sampleWorkflow\n      });\n      const aiMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: response.data.response,\n        timestamp: new Date(),\n        executionTime: response.data.execution_time_ms\n      };\n      setMessages(prev => [...prev, aiMessage]);\n\n      // Save chat message to backend\n      await axios.post('http://localhost:8000/api/chat/save', {\n        user_id: userId,\n        query: inputMessage,\n        response: response.data.response,\n        workflow_config: sampleWorkflow\n      });\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: 'Sorry, I encountered an error while processing your request. Please try again.',\n        timestamp: new Date(),\n        isError: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle Enter key press\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Load chat history (placeholder)\n  const loadChatHistory = async () => {\n    try {\n      const response = await axios.get(`http://localhost:8000/api/chat/history/${userId}`);\n      const history = response.data.map(msg => ({\n        id: msg.id,\n        type: 'user',\n        content: msg.query,\n        timestamp: new Date(msg.timestamp)\n      })).concat(response.data.map(msg => ({\n        id: msg.id + '_response',\n        type: 'ai',\n        content: msg.response,\n        timestamp: new Date(msg.timestamp)\n      })));\n      setMessages(history);\n    } catch (error) {\n      console.error('Error loading chat history:', error);\n    }\n  };\n\n  // Clear chat\n  const clearChat = () => {\n    setMessages([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-full bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:flex w-80 xl:w-96 bg-background border-r border-border flex-col shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-foreground\",\n          children: \"Chat Interface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-muted-foreground mt-1\",\n          children: \"Ask questions and get AI-powered responses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-4 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: loadChatHistory,\n          variant: \"outline\",\n          className: \"w-full justify-start\",\n          children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), \"Load Chat History\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: clearChat,\n          variant: \"outline\",\n          className: \"w-full justify-start\",\n          children: [/*#__PURE__*/_jsxDEV(Settings, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), \"Clear Chat\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-border\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            className: \"pb-2\",\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"text-sm\",\n              children: \"Current Workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            className: \"text-xs text-muted-foreground space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 User Query \\u2192 Knowledge Base\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 Knowledge Base \\u2192 LLM Engine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 LLM Engine \\u2192 Output\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-600 mt-2\",\n              children: \"\\u2713 Ready to chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n        children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Bot, {\n              className: \"w-12 h-12 text-muted-foreground mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-foreground mb-2\",\n              children: \"Welcome to GenAI Workflow Builder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-muted-foreground max-w-md\",\n              children: \"Start a conversation by typing your question below. The AI will process your query through the configured workflow.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this) : messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `max-w-[85%] sm:max-w-[70%] rounded-lg p-3 sm:p-4 ${message.type === 'user' ? 'bg-primary text-primary-foreground' : message.isError ? 'bg-destructive text-destructive-foreground' : 'bg-muted text-foreground'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(User, {\n                  className: \"w-4 h-4 mt-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(Bot, {\n                  className: \"w-4 h-4 mt-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm whitespace-pre-wrap\",\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mt-2 text-xs opacity-70\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: message.timestamp.toLocaleTimeString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this), message.executionTime && /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [message.executionTime, \"ms\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-start\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-muted text-foreground rounded-lg p-4 max-w-[70%]\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Bot, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"AI is thinking...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-border p-3 sm:p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            value: inputMessage,\n            onChange: e => setInputMessage(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Type your message here...\",\n            disabled: isLoading,\n            className: \"flex-1 text-sm sm:text-base\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSendMessage,\n            disabled: !inputMessage.trim() || isLoading,\n            size: \"icon\",\n            className: \"flex-shrink-0\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(Loader2, {\n              className: \"w-4 h-4 animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Send, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"hidden sm:inline\",\n            children: \"Press Enter to send, Shift+Enter for new line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sm:hidden\",\n            children: \"Enter to send\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [inputMessage.length, \"/1000\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatUI, \"kxxk/xjqouK+DjkpGUk9Fkapzbw=\");\n_c = ChatUI;\nexport default ChatUI;\nvar _c;\n$RefreshReg$(_c, \"ChatUI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON>", "Input", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Send", "Loader2", "User", "Bot", "Settings", "MessageSquare", "axios", "jsxDEV", "_jsxDEV", "ChatUI", "_s", "messages", "setMessages", "inputMessage", "setInputMessage", "isLoading", "setIsLoading", "userId", "Date", "now", "messagesEndRef", "sampleWorkflow", "nodes", "id", "type", "label", "config", "placeholder", "documentId", "maxResults", "model", "temperature", "maxTokens", "enableWebSearch", "format", "showSources", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "trim", "userMessage", "content", "timestamp", "prev", "response", "post", "user_id", "query", "workflow", "aiMessage", "data", "executionTime", "execution_time_ms", "workflow_config", "error", "console", "errorMessage", "isError", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "loadChatHistory", "get", "history", "map", "msg", "concat", "clearChat", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "variant", "length", "message", "toLocaleTimeString", "ref", "value", "onChange", "target", "onKeyPress", "disabled", "size", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/aiplanet/frontend/src/pages/ChatUI.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '../components/ui/button';\nimport { Input } from '../components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport {\n  Send,\n  Loader2,\n  User,\n  Bot,\n  Settings,\n  MessageSquare\n} from 'lucide-react';\nimport axios from 'axios';\n\nconst ChatUI = () => {\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [userId] = useState('user-' + Date.now()); // Simple user ID generation\n  const messagesEndRef = useRef(null);\n\n  // Sample workflow configuration (would come from WorkflowBuilder)\n  const sampleWorkflow = {\n    nodes: [\n      { id: 1, type: 'userQuery', label: 'User Query', config: { placeholder: 'Ask me anything...' } },\n      { id: 2, type: 'knowledgeBase', label: 'Knowledge Base', config: { documentId: null, maxResults: 5 } },\n      { id: 3, type: 'llmEngine', label: 'LLM Engine', config: { model: 'gpt-3.5-turbo', temperature: 0.7, maxTokens: 1000, enableWebSearch: false } },\n      { id: 4, type: 'output', label: 'Output', config: { format: 'text', showSources: true } }\n    ]\n  };\n\n  // Scroll to bottom when new messages are added\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    try {\n      // Call workflow execution API\n      const response = await axios.post('http://localhost:8000/api/workflow/execute', {\n        user_id: userId,\n        query: inputMessage,\n        workflow: sampleWorkflow\n      });\n\n      const aiMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: response.data.response,\n        timestamp: new Date(),\n        executionTime: response.data.execution_time_ms,\n      };\n\n      setMessages(prev => [...prev, aiMessage]);\n\n      // Save chat message to backend\n      await axios.post('http://localhost:8000/api/chat/save', {\n        user_id: userId,\n        query: inputMessage,\n        response: response.data.response,\n        workflow_config: sampleWorkflow\n      });\n\n    } catch (error) {\n      console.error('Error sending message:', error);\n      \n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: 'Sorry, I encountered an error while processing your request. Please try again.',\n        timestamp: new Date(),\n        isError: true,\n      };\n\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle Enter key press\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Load chat history (placeholder)\n  const loadChatHistory = async () => {\n    try {\n      const response = await axios.get(`http://localhost:8000/api/chat/history/${userId}`);\n      const history = response.data.map(msg => ({\n        id: msg.id,\n        type: 'user',\n        content: msg.query,\n        timestamp: new Date(msg.timestamp),\n      })).concat(response.data.map(msg => ({\n        id: msg.id + '_response',\n        type: 'ai',\n        content: msg.response,\n        timestamp: new Date(msg.timestamp),\n      })));\n      \n      setMessages(history);\n    } catch (error) {\n      console.error('Error loading chat history:', error);\n    }\n  };\n\n  // Clear chat\n  const clearChat = () => {\n    setMessages([]);\n  };\n\n  return (\n    <div className=\"flex h-full bg-background\">\n      {/* Chat Sidebar - Hidden on mobile */}\n      <div className=\"hidden lg:flex w-80 xl:w-96 bg-background border-r border-border flex-col shadow-lg\">\n        <div className=\"p-4 border-b border-border\">\n          <h2 className=\"text-lg font-semibold text-foreground\">Chat Interface</h2>\n          <p className=\"text-sm text-muted-foreground mt-1\">\n            Ask questions and get AI-powered responses\n          </p>\n        </div>\n\n        <div className=\"flex-1 p-4 space-y-3\">\n          <Button \n            onClick={loadChatHistory}\n            variant=\"outline\" \n            className=\"w-full justify-start\"\n          >\n            <MessageSquare className=\"w-4 h-4 mr-2\" />\n            Load Chat History\n          </Button>\n          \n          <Button \n            onClick={clearChat}\n            variant=\"outline\" \n            className=\"w-full justify-start\"\n          >\n            <Settings className=\"w-4 h-4 mr-2\" />\n            Clear Chat\n          </Button>\n        </div>\n\n        <div className=\"p-4 border-t border-border\">\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm\">Current Workflow</CardTitle>\n            </CardHeader>\n            <CardContent className=\"text-xs text-muted-foreground space-y-1\">\n              <p>• User Query → Knowledge Base</p>\n              <p>• Knowledge Base → LLM Engine</p>\n              <p>• LLM Engine → Output</p>\n              <p className=\"text-green-600 mt-2\">✓ Ready to chat</p>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Main Chat Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Chat Messages */}\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n          {messages.length === 0 ? (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center\">\n                <Bot className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-foreground mb-2\">\n                  Welcome to GenAI Workflow Builder\n                </h3>\n                <p className=\"text-sm text-muted-foreground max-w-md\">\n                  Start a conversation by typing your question below. \n                  The AI will process your query through the configured workflow.\n                </p>\n              </div>\n            </div>\n          ) : (\n            messages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-[85%] sm:max-w-[70%] rounded-lg p-3 sm:p-4 ${\n                    message.type === 'user'\n                      ? 'bg-primary text-primary-foreground'\n                      : message.isError\n                      ? 'bg-destructive text-destructive-foreground'\n                      : 'bg-muted text-foreground'\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-2\">\n                    <div className=\"flex-shrink-0\">\n                      {message.type === 'user' ? (\n                        <User className=\"w-4 h-4 mt-0.5\" />\n                      ) : (\n                        <Bot className=\"w-4 h-4 mt-0.5\" />\n                      )}\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n                      <div className=\"flex items-center justify-between mt-2 text-xs opacity-70\">\n                        <span>\n                          {message.timestamp.toLocaleTimeString()}\n                        </span>\n                        {message.executionTime && (\n                          <span>\n                            {message.executionTime}ms\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))\n          )}\n          \n          {isLoading && (\n            <div className=\"flex justify-start\">\n              <div className=\"bg-muted text-foreground rounded-lg p-4 max-w-[70%]\">\n                <div className=\"flex items-center space-x-2\">\n                  <Bot className=\"w-4 h-4\" />\n                  <div className=\"flex items-center space-x-1\">\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span className=\"text-sm\">AI is thinking...</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Message Input */}\n        <div className=\"border-t border-border p-3 sm:p-4\">\n          <div className=\"flex space-x-2\">\n            <Input\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Type your message here...\"\n              disabled={isLoading}\n              className=\"flex-1 text-sm sm:text-base\"\n            />\n            <Button\n              onClick={handleSendMessage}\n              disabled={!inputMessage.trim() || isLoading}\n              size=\"icon\"\n              className=\"flex-shrink-0\"\n            >\n              {isLoading ? (\n                <Loader2 className=\"w-4 h-4 animate-spin\" />\n              ) : (\n                <Send className=\"w-4 h-4\" />\n              )}\n            </Button>\n          </div>\n\n          <div className=\"flex items-center justify-between mt-2 text-xs text-muted-foreground\">\n            <span className=\"hidden sm:inline\">Press Enter to send, Shift+Enter for new line</span>\n            <span className=\"sm:hidden\">Enter to send</span>\n            <span>{inputMessage.length}/1000</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ChatUI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,uBAAuB;AAChF,SACEC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,QAAQ,EACRC,aAAa,QACR,cAAc;AACrB,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,MAAM,CAAC,GAAG1B,QAAQ,CAAC,OAAO,GAAG2B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,MAAMC,cAAc,GAAG5B,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM6B,cAAc,GAAG;IACrBC,KAAK,EAAE,CACL;MAAEC,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,YAAY;MAAEC,MAAM,EAAE;QAAEC,WAAW,EAAE;MAAqB;IAAE,CAAC,EAChG;MAAEJ,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,eAAe;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,MAAM,EAAE;QAAEE,UAAU,EAAE,IAAI;QAAEC,UAAU,EAAE;MAAE;IAAE,CAAC,EACtG;MAAEN,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,YAAY;MAAEC,MAAM,EAAE;QAAEI,KAAK,EAAE,eAAe;QAAEC,WAAW,EAAE,GAAG;QAAEC,SAAS,EAAE,IAAI;QAAEC,eAAe,EAAE;MAAM;IAAE,CAAC,EAChJ;MAAEV,EAAE,EAAE,CAAC;MAAEC,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,QAAQ;MAAEC,MAAM,EAAE;QAAEQ,MAAM,EAAE,MAAM;QAAEC,WAAW,EAAE;MAAK;IAAE,CAAC;EAE7F,CAAC;;EAED;EACA1C,SAAS,CAAC,MAAM;IACd2C,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACzB,QAAQ,CAAC,CAAC;EAEd,MAAMyB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAjB,cAAc,CAACkB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC5B,YAAY,CAAC6B,IAAI,CAAC,CAAC,IAAI3B,SAAS,EAAE;IAEvC,MAAM4B,WAAW,GAAG;MAClBpB,EAAE,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC;MACdK,IAAI,EAAE,MAAM;MACZoB,OAAO,EAAE/B,YAAY;MACrBgC,SAAS,EAAE,IAAI3B,IAAI,CAAC;IACtB,CAAC;IAEDN,WAAW,CAACkC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,WAAW,CAAC,CAAC;IAC3C7B,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM+B,QAAQ,GAAG,MAAMzC,KAAK,CAAC0C,IAAI,CAAC,4CAA4C,EAAE;QAC9EC,OAAO,EAAEhC,MAAM;QACfiC,KAAK,EAAErC,YAAY;QACnBsC,QAAQ,EAAE9B;MACZ,CAAC,CAAC;MAEF,MAAM+B,SAAS,GAAG;QAChB7B,EAAE,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBK,IAAI,EAAE,IAAI;QACVoB,OAAO,EAAEG,QAAQ,CAACM,IAAI,CAACN,QAAQ;QAC/BF,SAAS,EAAE,IAAI3B,IAAI,CAAC,CAAC;QACrBoC,aAAa,EAAEP,QAAQ,CAACM,IAAI,CAACE;MAC/B,CAAC;MAED3C,WAAW,CAACkC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,SAAS,CAAC,CAAC;;MAEzC;MACA,MAAM9C,KAAK,CAAC0C,IAAI,CAAC,qCAAqC,EAAE;QACtDC,OAAO,EAAEhC,MAAM;QACfiC,KAAK,EAAErC,YAAY;QACnBkC,QAAQ,EAAEA,QAAQ,CAACM,IAAI,CAACN,QAAQ;QAChCS,eAAe,EAAEnC;MACnB,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOoC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAE9C,MAAME,YAAY,GAAG;QACnBpC,EAAE,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBK,IAAI,EAAE,IAAI;QACVoB,OAAO,EAAE,gFAAgF;QACzFC,SAAS,EAAE,IAAI3B,IAAI,CAAC,CAAC;QACrB0C,OAAO,EAAE;MACX,CAAC;MAEDhD,WAAW,CAACkC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEa,YAAY,CAAC,CAAC;IAC9C,CAAC,SAAS;MACR3C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM6C,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBxB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMyB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMzC,KAAK,CAAC6D,GAAG,CAAC,0CAA0ClD,MAAM,EAAE,CAAC;MACpF,MAAMmD,OAAO,GAAGrB,QAAQ,CAACM,IAAI,CAACgB,GAAG,CAACC,GAAG,KAAK;QACxC/C,EAAE,EAAE+C,GAAG,CAAC/C,EAAE;QACVC,IAAI,EAAE,MAAM;QACZoB,OAAO,EAAE0B,GAAG,CAACpB,KAAK;QAClBL,SAAS,EAAE,IAAI3B,IAAI,CAACoD,GAAG,CAACzB,SAAS;MACnC,CAAC,CAAC,CAAC,CAAC0B,MAAM,CAACxB,QAAQ,CAACM,IAAI,CAACgB,GAAG,CAACC,GAAG,KAAK;QACnC/C,EAAE,EAAE+C,GAAG,CAAC/C,EAAE,GAAG,WAAW;QACxBC,IAAI,EAAE,IAAI;QACVoB,OAAO,EAAE0B,GAAG,CAACvB,QAAQ;QACrBF,SAAS,EAAE,IAAI3B,IAAI,CAACoD,GAAG,CAACzB,SAAS;MACnC,CAAC,CAAC,CAAC,CAAC;MAEJjC,WAAW,CAACwD,OAAO,CAAC;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMe,SAAS,GAAGA,CAAA,KAAM;IACtB5D,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,oBACEJ,OAAA;IAAKiE,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAExClE,OAAA;MAAKiE,SAAS,EAAC,qFAAqF;MAAAC,QAAA,gBAClGlE,OAAA;QAAKiE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzClE,OAAA;UAAIiE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEtE,OAAA;UAAGiE,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnClE,OAAA,CAACd,MAAM;UACLqF,OAAO,EAAEb,eAAgB;UACzBc,OAAO,EAAC,SAAS;UACjBP,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAEhClE,OAAA,CAACH,aAAa;YAACoE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETtE,OAAA,CAACd,MAAM;UACLqF,OAAO,EAAEP,SAAU;UACnBQ,OAAO,EAAC,SAAS;UACjBP,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAEhClE,OAAA,CAACJ,QAAQ;YAACqE,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtE,OAAA;QAAKiE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzClE,OAAA,CAACZ,IAAI;UAAA8E,QAAA,gBACHlE,OAAA,CAACV,UAAU;YAAC2E,SAAS,EAAC,MAAM;YAAAC,QAAA,eAC1BlE,OAAA,CAACT,SAAS;cAAC0E,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACbtE,OAAA,CAACX,WAAW;YAAC4E,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBAC9DlE,OAAA;cAAAkE,QAAA,EAAG;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpCtE,OAAA;cAAAkE,QAAA,EAAG;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpCtE,OAAA;cAAAkE,QAAA,EAAG;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5BtE,OAAA;cAAGiE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtE,OAAA;MAAKiE,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnClE,OAAA;QAAKiE,SAAS,EAAC,sCAAsC;QAAAC,QAAA,GAClD/D,QAAQ,CAACsE,MAAM,KAAK,CAAC,gBACpBzE,OAAA;UAAKiE,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDlE,OAAA;YAAKiE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlE,OAAA,CAACL,GAAG;cAACsE,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChEtE,OAAA;cAAIiE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtE,OAAA;cAAGiE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAGtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GAENnE,QAAQ,CAAC0D,GAAG,CAAEa,OAAO,iBACnB1E,OAAA;UAEEiE,SAAS,EAAE,QAAQS,OAAO,CAAC1D,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAe,EAAG;UAAAkD,QAAA,eAE/ElE,OAAA;YACEiE,SAAS,EAAE,oDACTS,OAAO,CAAC1D,IAAI,KAAK,MAAM,GACnB,oCAAoC,GACpC0D,OAAO,CAACtB,OAAO,GACf,4CAA4C,GAC5C,0BAA0B,EAC7B;YAAAc,QAAA,eAEHlE,OAAA;cAAKiE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzClE,OAAA;gBAAKiE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BQ,OAAO,CAAC1D,IAAI,KAAK,MAAM,gBACtBhB,OAAA,CAACN,IAAI;kBAACuE,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEnCtE,OAAA,CAACL,GAAG;kBAACsE,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAClC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtE,OAAA;gBAAKiE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBlE,OAAA;kBAAGiE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEQ,OAAO,CAACtC;gBAAO;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChEtE,OAAA;kBAAKiE,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,gBACxElE,OAAA;oBAAAkE,QAAA,EACGQ,OAAO,CAACrC,SAAS,CAACsC,kBAAkB,CAAC;kBAAC;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,EACNI,OAAO,CAAC5B,aAAa,iBACpB9C,OAAA;oBAAAkE,QAAA,GACGQ,OAAO,CAAC5B,aAAa,EAAC,IACzB;kBAAA;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAlCDI,OAAO,CAAC3D,EAAE;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmCZ,CACN,CACF,EAEA/D,SAAS,iBACRP,OAAA;UAAKiE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjClE,OAAA;YAAKiE,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClElE,OAAA;cAAKiE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1ClE,OAAA,CAACL,GAAG;gBAACsE,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3BtE,OAAA;gBAAKiE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1ClE,OAAA,CAACP,OAAO;kBAACwE,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CtE,OAAA;kBAAMiE,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDtE,OAAA;UAAK4E,GAAG,EAAEhE;QAAe;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGNtE,OAAA;QAAKiE,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDlE,OAAA;UAAKiE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlE,OAAA,CAACb,KAAK;YACJ0F,KAAK,EAAExE,YAAa;YACpByE,QAAQ,EAAGxB,CAAC,IAAKhD,eAAe,CAACgD,CAAC,CAACyB,MAAM,CAACF,KAAK,CAAE;YACjDG,UAAU,EAAE3B,cAAe;YAC3BlC,WAAW,EAAC,2BAA2B;YACvC8D,QAAQ,EAAE1E,SAAU;YACpB0D,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACFtE,OAAA,CAACd,MAAM;YACLqF,OAAO,EAAEtC,iBAAkB;YAC3BgD,QAAQ,EAAE,CAAC5E,YAAY,CAAC6B,IAAI,CAAC,CAAC,IAAI3B,SAAU;YAC5C2E,IAAI,EAAC,MAAM;YACXjB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAExB3D,SAAS,gBACRP,OAAA,CAACP,OAAO;cAACwE,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE5CtE,OAAA,CAACR,IAAI;cAACyE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC5B;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENtE,OAAA;UAAKiE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFlE,OAAA;YAAMiE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvFtE,OAAA;YAAMiE,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDtE,OAAA;YAAAkE,QAAA,GAAO7D,YAAY,CAACoE,MAAM,EAAC,OAAK;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpE,EAAA,CAnRID,MAAM;AAAAkF,EAAA,GAANlF,MAAM;AAqRZ,eAAeA,MAAM;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}