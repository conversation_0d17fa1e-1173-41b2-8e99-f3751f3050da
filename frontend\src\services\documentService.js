import { apiGet, apiPost, apiDelete, apiUpload, handleApiError } from './api';

/**
 * Document Service
 * Handles all document-related API operations including upload, retrieval, and management
 */

/**
 * Upload a PDF document
 * @param {File} file - PDF file to upload
 * @param {Function} onProgress - Progress callback function
 * @returns {Promise<Object>} Upload response with document details
 */
export const uploadDocument = async (file, onProgress = null) => {
  try {
    // Validate file type
    if (!file.type.includes('pdf')) {
      throw new Error('Only PDF files are allowed');
    }
    
    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Error('File size must be less than 10MB');
    }
    
    const response = await apiUpload('/documents/upload', file, onProgress);
    
    return {
      success: true,
      data: response,
      message: 'Document uploaded successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get list of all documents
 * @param {Object} params - Query parameters
 * @param {number} params.skip - Number of documents to skip
 * @param {number} params.limit - Maximum number of documents to return
 * @returns {Promise<Object>} List of documents
 */
export const getDocuments = async (params = {}) => {
  try {
    const { skip = 0, limit = 100 } = params;
    
    const response = await apiGet('/documents/', { skip, limit });
    
    return {
      success: true,
      data: response,
      message: 'Documents retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get specific document by ID
 * @param {number} documentId - ID of the document
 * @returns {Promise<Object>} Document details
 */
export const getDocument = async (documentId) => {
  try {
    if (!documentId) {
      throw new Error('Document ID is required');
    }
    
    const response = await apiGet(`/documents/${documentId}`);
    
    return {
      success: true,
      data: response,
      message: 'Document retrieved successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Delete a document
 * @param {number} documentId - ID of the document to delete
 * @returns {Promise<Object>} Deletion response
 */
export const deleteDocument = async (documentId) => {
  try {
    if (!documentId) {
      throw new Error('Document ID is required');
    }
    
    const response = await apiDelete(`/documents/${documentId}`);
    
    return {
      success: true,
      data: response,
      message: 'Document deleted successfully',
    };
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Get document processing status
 * @param {number} documentId - ID of the document
 * @returns {Promise<Object>} Processing status
 */
export const getDocumentStatus = async (documentId) => {
  try {
    const result = await getDocument(documentId);
    
    if (result.success) {
      const document = result.data;
      return {
        success: true,
        data: {
          id: document.id,
          filename: document.original_filename,
          processed: document.processed,
          embeddings_generated: document.embeddings_generated,
          file_size: document.file_size,
          page_count: document.page_count,
          upload_timestamp: document.upload_timestamp,
        },
        message: 'Document status retrieved successfully',
      };
    }
    
    return result;
  } catch (error) {
    const errorInfo = handleApiError(error);
    return {
      success: false,
      error: errorInfo,
      message: errorInfo.message,
    };
  }
};

/**
 * Validate document before upload
 * @param {File} file - File to validate
 * @returns {Object} Validation result
 */
export const validateDocument = (file) => {
  const errors = [];
  
  // Check if file exists
  if (!file) {
    errors.push('No file selected');
  } else {
    // Check file type
    if (!file.type.includes('pdf')) {
      errors.push('Only PDF files are allowed');
    }
    
    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      errors.push('File size must be less than 10MB');
    }
    
    // Check file name
    if (file.name.length > 255) {
      errors.push('File name is too long (max 255 characters)');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get file extension from filename
 * @param {string} filename - Name of the file
 * @returns {string} File extension
 */
export const getFileExtension = (filename) => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * Check if document is ready for embedding generation
 * @param {Object} document - Document object
 * @returns {boolean} Whether document is ready
 */
export const isDocumentReady = (document) => {
  return document && document.processed && document.extracted_text;
};

export default {
  uploadDocument,
  getDocuments,
  getDocument,
  deleteDocument,
  getDocumentStatus,
  validateDocument,
  formatFileSize,
  getFileExtension,
  isDocumentReady,
};
