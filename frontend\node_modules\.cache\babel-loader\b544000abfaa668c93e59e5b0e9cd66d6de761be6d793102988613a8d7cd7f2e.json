{"ast": null, "code": "var _jsxFileName = \"D:\\\\assignment for AI planet\\\\aiplanet\\\\frontend\\\\src\\\\pages\\\\ChatUI.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '../components/ui/button';\nimport { Input } from '../components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport { Send, Loader2, User, Bot, Settings, MessageSquare } from 'lucide-react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatUI = () => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [userId] = useState('user-' + Date.now()); // Simple user ID generation\n  const messagesEndRef = useRef(null);\n\n  // Sample workflow configuration (would come from WorkflowBuilder)\n  const sampleWorkflow = {\n    nodes: [{\n      id: 'user-query-1',\n      type: 'userQuery',\n      position: {\n        x: 100,\n        y: 100\n      },\n      data: {\n        label: 'User Query',\n        config: {\n          placeholder: 'Ask me anything...'\n        }\n      }\n    }, {\n      id: 'knowledge-base-1',\n      type: 'knowledgeBase',\n      position: {\n        x: 400,\n        y: 100\n      },\n      data: {\n        label: 'Knowledge Base',\n        config: {\n          documentId: null,\n          maxResults: 5\n        }\n      }\n    }, {\n      id: 'llm-engine-1',\n      type: 'llmEngine',\n      position: {\n        x: 700,\n        y: 100\n      },\n      data: {\n        label: 'LLM Engine',\n        config: {\n          model: 'gpt-3.5-turbo',\n          temperature: 0.7,\n          maxTokens: 1000,\n          enableWebSearch: false\n        }\n      }\n    }, {\n      id: 'output-1',\n      type: 'output',\n      position: {\n        x: 1000,\n        y: 100\n      },\n      data: {\n        label: 'Output',\n        config: {\n          format: 'text',\n          showSources: true\n        }\n      }\n    }],\n    connections: [{\n      source: 'user-query-1',\n      target: 'knowledge-base-1'\n    }, {\n      source: 'user-query-1',\n      target: 'llm-engine-1',\n      targetHandle: 'query'\n    }, {\n      source: 'knowledge-base-1',\n      target: 'llm-engine-1',\n      targetHandle: 'context'\n    }, {\n      source: 'llm-engine-1',\n      target: 'output-1'\n    }]\n  };\n\n  // Scroll to bottom when new messages are added\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n    try {\n      // Call workflow execution API\n      const response = await axios.post('http://localhost:8000/api/workflow/execute', {\n        user_id: userId,\n        query: inputMessage,\n        workflow: sampleWorkflow\n      });\n      const aiMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: response.data.response,\n        timestamp: new Date(),\n        executionTime: response.data.execution_time_ms\n      };\n      setMessages(prev => [...prev, aiMessage]);\n\n      // Save chat message to backend\n      await axios.post('http://localhost:8000/api/chat/save', {\n        user_id: userId,\n        query: inputMessage,\n        response: response.data.response,\n        workflow_config: sampleWorkflow\n      });\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: 'Sorry, I encountered an error while processing your request. Please try again.',\n        timestamp: new Date(),\n        isError: true\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle Enter key press\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Load chat history (placeholder)\n  const loadChatHistory = async () => {\n    try {\n      const response = await axios.get(`http://localhost:8000/api/chat/history/${userId}`);\n      const history = response.data.map(msg => ({\n        id: msg.id,\n        type: 'user',\n        content: msg.query,\n        timestamp: new Date(msg.timestamp)\n      })).concat(response.data.map(msg => ({\n        id: msg.id + '_response',\n        type: 'ai',\n        content: msg.response,\n        timestamp: new Date(msg.timestamp)\n      })));\n      setMessages(history);\n    } catch (error) {\n      console.error('Error loading chat history:', error);\n    }\n  };\n\n  // Clear chat\n  const clearChat = () => {\n    setMessages([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-full bg-background\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hidden lg:flex w-80 xl:w-96 bg-background border-r border-border flex-col shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-b border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-foreground\",\n          children: \"Chat Interface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-muted-foreground mt-1\",\n          children: \"Ask questions and get AI-powered responses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-4 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: loadChatHistory,\n          variant: \"outline\",\n          className: \"w-full justify-start\",\n          children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), \"Load Chat History\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: clearChat,\n          variant: \"outline\",\n          className: \"w-full justify-start\",\n          children: [/*#__PURE__*/_jsxDEV(Settings, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), \"Clear Chat\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4 border-t border-border\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n            className: \"pb-2\",\n            children: /*#__PURE__*/_jsxDEV(CardTitle, {\n              className: \"text-sm\",\n              children: \"Current Workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            className: \"text-xs text-muted-foreground space-y-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 User Query \\u2192 Knowledge Base\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 Knowledge Base \\u2192 LLM Engine\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u2022 LLM Engine \\u2192 Output\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-600 mt-2\",\n              children: \"\\u2713 Ready to chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n        children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(Bot, {\n              className: \"w-12 h-12 text-muted-foreground mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium text-foreground mb-2\",\n              children: \"Welcome to GenAI Workflow Builder\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-muted-foreground max-w-md\",\n              children: \"Start a conversation by typing your question below. The AI will process your query through the configured workflow.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this) : messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `max-w-[85%] sm:max-w-[70%] rounded-lg p-3 sm:p-4 ${message.type === 'user' ? 'bg-primary text-primary-foreground' : message.isError ? 'bg-destructive text-destructive-foreground' : 'bg-muted text-foreground'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: message.type === 'user' ? /*#__PURE__*/_jsxDEV(User, {\n                  className: \"w-4 h-4 mt-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(Bot, {\n                  className: \"w-4 h-4 mt-0.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm whitespace-pre-wrap\",\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mt-2 text-xs opacity-70\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: message.timestamp.toLocaleTimeString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 25\n                  }, this), message.executionTime && /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [message.executionTime, \"ms\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-start\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-muted text-foreground rounded-lg p-4 max-w-[70%]\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Bot, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(Loader2, {\n                  className: \"w-4 h-4 animate-spin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm\",\n                  children: \"AI is thinking...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-border p-3 sm:p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            value: inputMessage,\n            onChange: e => setInputMessage(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Type your message here...\",\n            disabled: isLoading,\n            className: \"flex-1 text-sm sm:text-base\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleSendMessage,\n            disabled: !inputMessage.trim() || isLoading,\n            size: \"icon\",\n            className: \"flex-shrink-0\",\n            children: isLoading ? /*#__PURE__*/_jsxDEV(Loader2, {\n              className: \"w-4 h-4 animate-spin\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Send, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"hidden sm:inline\",\n            children: \"Press Enter to send, Shift+Enter for new line\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sm:hidden\",\n            children: \"Enter to send\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [inputMessage.length, \"/1000\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatUI, \"kxxk/xjqouK+DjkpGUk9Fkapzbw=\");\n_c = ChatUI;\nexport default ChatUI;\nvar _c;\n$RefreshReg$(_c, \"ChatUI\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON>", "Input", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "Send", "Loader2", "User", "Bot", "Settings", "MessageSquare", "axios", "jsxDEV", "_jsxDEV", "ChatUI", "_s", "messages", "setMessages", "inputMessage", "setInputMessage", "isLoading", "setIsLoading", "userId", "Date", "now", "messagesEndRef", "sampleWorkflow", "nodes", "id", "type", "position", "x", "y", "data", "label", "config", "placeholder", "documentId", "maxResults", "model", "temperature", "maxTokens", "enableWebSearch", "format", "showSources", "connections", "source", "target", "targetHandle", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "trim", "userMessage", "content", "timestamp", "prev", "response", "post", "user_id", "query", "workflow", "aiMessage", "executionTime", "execution_time_ms", "workflow_config", "error", "console", "errorMessage", "isError", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "loadChatHistory", "get", "history", "map", "msg", "concat", "clearChat", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "variant", "length", "message", "toLocaleTimeString", "ref", "value", "onChange", "onKeyPress", "disabled", "size", "_c", "$RefreshReg$"], "sources": ["D:/assignment for AI planet/aiplanet/frontend/src/pages/ChatUI.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '../components/ui/button';\nimport { Input } from '../components/ui/input';\nimport { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';\nimport {\n  Send,\n  Loader2,\n  User,\n  Bot,\n  Settings,\n  MessageSquare\n} from 'lucide-react';\nimport axios from 'axios';\n\nconst ChatUI = () => {\n  const [messages, setMessages] = useState([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [userId] = useState('user-' + Date.now()); // Simple user ID generation\n  const messagesEndRef = useRef(null);\n\n  // Sample workflow configuration (would come from WorkflowBuilder)\n  const sampleWorkflow = {\n    nodes: [\n      {\n        id: 'user-query-1',\n        type: 'userQuery',\n        position: { x: 100, y: 100 },\n        data: { label: 'User Query', config: { placeholder: 'Ask me anything...' } }\n      },\n      {\n        id: 'knowledge-base-1',\n        type: 'knowledgeBase',\n        position: { x: 400, y: 100 },\n        data: { label: 'Knowledge Base', config: { documentId: null, maxResults: 5 } }\n      },\n      {\n        id: 'llm-engine-1',\n        type: 'llmEngine',\n        position: { x: 700, y: 100 },\n        data: { \n          label: 'LLM Engine', \n          config: { \n            model: 'gpt-3.5-turbo', \n            temperature: 0.7, \n            maxTokens: 1000,\n            enableWebSearch: false\n          } \n        }\n      },\n      {\n        id: 'output-1',\n        type: 'output',\n        position: { x: 1000, y: 100 },\n        data: { label: 'Output', config: { format: 'text', showSources: true } }\n      }\n    ],\n    connections: [\n      { source: 'user-query-1', target: 'knowledge-base-1' },\n      { source: 'user-query-1', target: 'llm-engine-1', targetHandle: 'query' },\n      { source: 'knowledge-base-1', target: 'llm-engine-1', targetHandle: 'context' },\n      { source: 'llm-engine-1', target: 'output-1' }\n    ]\n  };\n\n  // Scroll to bottom when new messages are added\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  // Handle sending message\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date(),\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    try {\n      // Call workflow execution API\n      const response = await axios.post('http://localhost:8000/api/workflow/execute', {\n        user_id: userId,\n        query: inputMessage,\n        workflow: sampleWorkflow\n      });\n\n      const aiMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: response.data.response,\n        timestamp: new Date(),\n        executionTime: response.data.execution_time_ms,\n      };\n\n      setMessages(prev => [...prev, aiMessage]);\n\n      // Save chat message to backend\n      await axios.post('http://localhost:8000/api/chat/save', {\n        user_id: userId,\n        query: inputMessage,\n        response: response.data.response,\n        workflow_config: sampleWorkflow\n      });\n\n    } catch (error) {\n      console.error('Error sending message:', error);\n      \n      const errorMessage = {\n        id: Date.now() + 1,\n        type: 'ai',\n        content: 'Sorry, I encountered an error while processing your request. Please try again.',\n        timestamp: new Date(),\n        isError: true,\n      };\n\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Handle Enter key press\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // Load chat history (placeholder)\n  const loadChatHistory = async () => {\n    try {\n      const response = await axios.get(`http://localhost:8000/api/chat/history/${userId}`);\n      const history = response.data.map(msg => ({\n        id: msg.id,\n        type: 'user',\n        content: msg.query,\n        timestamp: new Date(msg.timestamp),\n      })).concat(response.data.map(msg => ({\n        id: msg.id + '_response',\n        type: 'ai',\n        content: msg.response,\n        timestamp: new Date(msg.timestamp),\n      })));\n      \n      setMessages(history);\n    } catch (error) {\n      console.error('Error loading chat history:', error);\n    }\n  };\n\n  // Clear chat\n  const clearChat = () => {\n    setMessages([]);\n  };\n\n  return (\n    <div className=\"flex h-full bg-background\">\n      {/* Chat Sidebar - Hidden on mobile */}\n      <div className=\"hidden lg:flex w-80 xl:w-96 bg-background border-r border-border flex-col shadow-lg\">\n        <div className=\"p-4 border-b border-border\">\n          <h2 className=\"text-lg font-semibold text-foreground\">Chat Interface</h2>\n          <p className=\"text-sm text-muted-foreground mt-1\">\n            Ask questions and get AI-powered responses\n          </p>\n        </div>\n\n        <div className=\"flex-1 p-4 space-y-3\">\n          <Button \n            onClick={loadChatHistory}\n            variant=\"outline\" \n            className=\"w-full justify-start\"\n          >\n            <MessageSquare className=\"w-4 h-4 mr-2\" />\n            Load Chat History\n          </Button>\n          \n          <Button \n            onClick={clearChat}\n            variant=\"outline\" \n            className=\"w-full justify-start\"\n          >\n            <Settings className=\"w-4 h-4 mr-2\" />\n            Clear Chat\n          </Button>\n        </div>\n\n        <div className=\"p-4 border-t border-border\">\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm\">Current Workflow</CardTitle>\n            </CardHeader>\n            <CardContent className=\"text-xs text-muted-foreground space-y-1\">\n              <p>• User Query → Knowledge Base</p>\n              <p>• Knowledge Base → LLM Engine</p>\n              <p>• LLM Engine → Output</p>\n              <p className=\"text-green-600 mt-2\">✓ Ready to chat</p>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Main Chat Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Chat Messages */}\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n          {messages.length === 0 ? (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center\">\n                <Bot className=\"w-12 h-12 text-muted-foreground mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium text-foreground mb-2\">\n                  Welcome to GenAI Workflow Builder\n                </h3>\n                <p className=\"text-sm text-muted-foreground max-w-md\">\n                  Start a conversation by typing your question below. \n                  The AI will process your query through the configured workflow.\n                </p>\n              </div>\n            </div>\n          ) : (\n            messages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-[85%] sm:max-w-[70%] rounded-lg p-3 sm:p-4 ${\n                    message.type === 'user'\n                      ? 'bg-primary text-primary-foreground'\n                      : message.isError\n                      ? 'bg-destructive text-destructive-foreground'\n                      : 'bg-muted text-foreground'\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-2\">\n                    <div className=\"flex-shrink-0\">\n                      {message.type === 'user' ? (\n                        <User className=\"w-4 h-4 mt-0.5\" />\n                      ) : (\n                        <Bot className=\"w-4 h-4 mt-0.5\" />\n                      )}\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n                      <div className=\"flex items-center justify-between mt-2 text-xs opacity-70\">\n                        <span>\n                          {message.timestamp.toLocaleTimeString()}\n                        </span>\n                        {message.executionTime && (\n                          <span>\n                            {message.executionTime}ms\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))\n          )}\n          \n          {isLoading && (\n            <div className=\"flex justify-start\">\n              <div className=\"bg-muted text-foreground rounded-lg p-4 max-w-[70%]\">\n                <div className=\"flex items-center space-x-2\">\n                  <Bot className=\"w-4 h-4\" />\n                  <div className=\"flex items-center space-x-1\">\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    <span className=\"text-sm\">AI is thinking...</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Message Input */}\n        <div className=\"border-t border-border p-3 sm:p-4\">\n          <div className=\"flex space-x-2\">\n            <Input\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Type your message here...\"\n              disabled={isLoading}\n              className=\"flex-1 text-sm sm:text-base\"\n            />\n            <Button\n              onClick={handleSendMessage}\n              disabled={!inputMessage.trim() || isLoading}\n              size=\"icon\"\n              className=\"flex-shrink-0\"\n            >\n              {isLoading ? (\n                <Loader2 className=\"w-4 h-4 animate-spin\" />\n              ) : (\n                <Send className=\"w-4 h-4\" />\n              )}\n            </Button>\n          </div>\n\n          <div className=\"flex items-center justify-between mt-2 text-xs text-muted-foreground\">\n            <span className=\"hidden sm:inline\">Press Enter to send, Shift+Enter for new line</span>\n            <span className=\"sm:hidden\">Enter to send</span>\n            <span>{inputMessage.length}/1000</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ChatUI;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,KAAK,QAAQ,wBAAwB;AAC9C,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,uBAAuB;AAChF,SACEC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,GAAG,EACHC,QAAQ,EACRC,aAAa,QACR,cAAc;AACrB,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0B,MAAM,CAAC,GAAG1B,QAAQ,CAAC,OAAO,GAAG2B,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,MAAMC,cAAc,GAAG5B,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAM6B,cAAc,GAAG;IACrBC,KAAK,EAAE,CACL;MACEC,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC;MAC5BC,IAAI,EAAE;QAAEC,KAAK,EAAE,YAAY;QAAEC,MAAM,EAAE;UAAEC,WAAW,EAAE;QAAqB;MAAE;IAC7E,CAAC,EACD;MACER,EAAE,EAAE,kBAAkB;MACtBC,IAAI,EAAE,eAAe;MACrBC,QAAQ,EAAE;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC;MAC5BC,IAAI,EAAE;QAAEC,KAAK,EAAE,gBAAgB;QAAEC,MAAM,EAAE;UAAEE,UAAU,EAAE,IAAI;UAAEC,UAAU,EAAE;QAAE;MAAE;IAC/E,CAAC,EACD;MACEV,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE;QAAEC,CAAC,EAAE,GAAG;QAAEC,CAAC,EAAE;MAAI,CAAC;MAC5BC,IAAI,EAAE;QACJC,KAAK,EAAE,YAAY;QACnBC,MAAM,EAAE;UACNI,KAAK,EAAE,eAAe;UACtBC,WAAW,EAAE,GAAG;UAChBC,SAAS,EAAE,IAAI;UACfC,eAAe,EAAE;QACnB;MACF;IACF,CAAC,EACD;MACEd,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE;QAAEC,CAAC,EAAE,IAAI;QAAEC,CAAC,EAAE;MAAI,CAAC;MAC7BC,IAAI,EAAE;QAAEC,KAAK,EAAE,QAAQ;QAAEC,MAAM,EAAE;UAAEQ,MAAM,EAAE,MAAM;UAAEC,WAAW,EAAE;QAAK;MAAE;IACzE,CAAC,CACF;IACDC,WAAW,EAAE,CACX;MAAEC,MAAM,EAAE,cAAc;MAAEC,MAAM,EAAE;IAAmB,CAAC,EACtD;MAAED,MAAM,EAAE,cAAc;MAAEC,MAAM,EAAE,cAAc;MAAEC,YAAY,EAAE;IAAQ,CAAC,EACzE;MAAEF,MAAM,EAAE,kBAAkB;MAAEC,MAAM,EAAE,cAAc;MAAEC,YAAY,EAAE;IAAU,CAAC,EAC/E;MAAEF,MAAM,EAAE,cAAc;MAAEC,MAAM,EAAE;IAAW,CAAC;EAElD,CAAC;;EAED;EACAjD,SAAS,CAAC,MAAM;IACdmD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACjC,QAAQ,CAAC,CAAC;EAEd,MAAMiC,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAzB,cAAc,CAAC0B,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACpC,YAAY,CAACqC,IAAI,CAAC,CAAC,IAAInC,SAAS,EAAE;IAEvC,MAAMoC,WAAW,GAAG;MAClB5B,EAAE,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC;MACdK,IAAI,EAAE,MAAM;MACZ4B,OAAO,EAAEvC,YAAY;MACrBwC,SAAS,EAAE,IAAInC,IAAI,CAAC;IACtB,CAAC;IAEDN,WAAW,CAAC0C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEH,WAAW,CAAC,CAAC;IAC3CrC,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAMuC,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,IAAI,CAAC,4CAA4C,EAAE;QAC9EC,OAAO,EAAExC,MAAM;QACfyC,KAAK,EAAE7C,YAAY;QACnB8C,QAAQ,EAAEtC;MACZ,CAAC,CAAC;MAEF,MAAMuC,SAAS,GAAG;QAChBrC,EAAE,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBK,IAAI,EAAE,IAAI;QACV4B,OAAO,EAAEG,QAAQ,CAAC3B,IAAI,CAAC2B,QAAQ;QAC/BF,SAAS,EAAE,IAAInC,IAAI,CAAC,CAAC;QACrB2C,aAAa,EAAEN,QAAQ,CAAC3B,IAAI,CAACkC;MAC/B,CAAC;MAEDlD,WAAW,CAAC0C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEM,SAAS,CAAC,CAAC;;MAEzC;MACA,MAAMtD,KAAK,CAACkD,IAAI,CAAC,qCAAqC,EAAE;QACtDC,OAAO,EAAExC,MAAM;QACfyC,KAAK,EAAE7C,YAAY;QACnB0C,QAAQ,EAAEA,QAAQ,CAAC3B,IAAI,CAAC2B,QAAQ;QAChCQ,eAAe,EAAE1C;MACnB,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAO2C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAE9C,MAAME,YAAY,GAAG;QACnB3C,EAAE,EAAEL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBK,IAAI,EAAE,IAAI;QACV4B,OAAO,EAAE,gFAAgF;QACzFC,SAAS,EAAE,IAAInC,IAAI,CAAC,CAAC;QACrBiD,OAAO,EAAE;MACX,CAAC;MAEDvD,WAAW,CAAC0C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEY,YAAY,CAAC,CAAC;IAC9C,CAAC,SAAS;MACRlD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMoD,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBvB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMwB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMjD,KAAK,CAACoE,GAAG,CAAC,0CAA0CzD,MAAM,EAAE,CAAC;MACpF,MAAM0D,OAAO,GAAGpB,QAAQ,CAAC3B,IAAI,CAACgD,GAAG,CAACC,GAAG,KAAK;QACxCtD,EAAE,EAAEsD,GAAG,CAACtD,EAAE;QACVC,IAAI,EAAE,MAAM;QACZ4B,OAAO,EAAEyB,GAAG,CAACnB,KAAK;QAClBL,SAAS,EAAE,IAAInC,IAAI,CAAC2D,GAAG,CAACxB,SAAS;MACnC,CAAC,CAAC,CAAC,CAACyB,MAAM,CAACvB,QAAQ,CAAC3B,IAAI,CAACgD,GAAG,CAACC,GAAG,KAAK;QACnCtD,EAAE,EAAEsD,GAAG,CAACtD,EAAE,GAAG,WAAW;QACxBC,IAAI,EAAE,IAAI;QACV4B,OAAO,EAAEyB,GAAG,CAACtB,QAAQ;QACrBF,SAAS,EAAE,IAAInC,IAAI,CAAC2D,GAAG,CAACxB,SAAS;MACnC,CAAC,CAAC,CAAC,CAAC;MAEJzC,WAAW,CAAC+D,OAAO,CAAC;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMe,SAAS,GAAGA,CAAA,KAAM;IACtBnE,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,oBACEJ,OAAA;IAAKwE,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAExCzE,OAAA;MAAKwE,SAAS,EAAC,qFAAqF;MAAAC,QAAA,gBAClGzE,OAAA;QAAKwE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACzCzE,OAAA;UAAIwE,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE7E,OAAA;UAAGwE,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEN7E,OAAA;QAAKwE,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCzE,OAAA,CAACd,MAAM;UACL4F,OAAO,EAAEb,eAAgB;UACzBc,OAAO,EAAC,SAAS;UACjBP,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAEhCzE,OAAA,CAACH,aAAa;YAAC2E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAE5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAET7E,OAAA,CAACd,MAAM;UACL4F,OAAO,EAAEP,SAAU;UACnBQ,OAAO,EAAC,SAAS;UACjBP,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAEhCzE,OAAA,CAACJ,QAAQ;YAAC4E,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,cAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN7E,OAAA;QAAKwE,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCzE,OAAA,CAACZ,IAAI;UAAAqF,QAAA,gBACHzE,OAAA,CAACV,UAAU;YAACkF,SAAS,EAAC,MAAM;YAAAC,QAAA,eAC1BzE,OAAA,CAACT,SAAS;cAACiF,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACb7E,OAAA,CAACX,WAAW;YAACmF,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBAC9DzE,OAAA;cAAAyE,QAAA,EAAG;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpC7E,OAAA;cAAAyE,QAAA,EAAG;YAA6B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpC7E,OAAA;cAAAyE,QAAA,EAAG;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC5B7E,OAAA;cAAGwE,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7E,OAAA;MAAKwE,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnCzE,OAAA;QAAKwE,SAAS,EAAC,sCAAsC;QAAAC,QAAA,GAClDtE,QAAQ,CAAC6E,MAAM,KAAK,CAAC,gBACpBhF,OAAA;UAAKwE,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtDzE,OAAA;YAAKwE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BzE,OAAA,CAACL,GAAG;cAAC6E,SAAS,EAAC;YAA8C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChE7E,OAAA;cAAIwE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7E,OAAA;cAAGwE,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAGtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,GAEN1E,QAAQ,CAACiE,GAAG,CAAEa,OAAO,iBACnBjF,OAAA;UAEEwE,SAAS,EAAE,QAAQS,OAAO,CAACjE,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAe,EAAG;UAAAyD,QAAA,eAE/EzE,OAAA;YACEwE,SAAS,EAAE,oDACTS,OAAO,CAACjE,IAAI,KAAK,MAAM,GACnB,oCAAoC,GACpCiE,OAAO,CAACtB,OAAO,GACf,4CAA4C,GAC5C,0BAA0B,EAC7B;YAAAc,QAAA,eAEHzE,OAAA;cAAKwE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCzE,OAAA;gBAAKwE,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAC3BQ,OAAO,CAACjE,IAAI,KAAK,MAAM,gBACtBhB,OAAA,CAACN,IAAI;kBAAC8E,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEnC7E,OAAA,CAACL,GAAG;kBAAC6E,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAClC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN7E,OAAA;gBAAKwE,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBzE,OAAA;kBAAGwE,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEQ,OAAO,CAACrC;gBAAO;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChE7E,OAAA;kBAAKwE,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,gBACxEzE,OAAA;oBAAAyE,QAAA,EACGQ,OAAO,CAACpC,SAAS,CAACqC,kBAAkB,CAAC;kBAAC;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC,CAAC,EACNI,OAAO,CAAC5B,aAAa,iBACpBrD,OAAA;oBAAAyE,QAAA,GACGQ,OAAO,CAAC5B,aAAa,EAAC,IACzB;kBAAA;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAlCDI,OAAO,CAAClE,EAAE;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmCZ,CACN,CACF,EAEAtE,SAAS,iBACRP,OAAA;UAAKwE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCzE,OAAA;YAAKwE,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClEzE,OAAA;cAAKwE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CzE,OAAA,CAACL,GAAG;gBAAC6E,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3B7E,OAAA;gBAAKwE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CzE,OAAA,CAACP,OAAO;kBAAC+E,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5C7E,OAAA;kBAAMwE,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAED7E,OAAA;UAAKmF,GAAG,EAAEvE;QAAe;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGN7E,OAAA;QAAKwE,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDzE,OAAA;UAAKwE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BzE,OAAA,CAACb,KAAK;YACJiG,KAAK,EAAE/E,YAAa;YACpBgF,QAAQ,EAAGxB,CAAC,IAAKvD,eAAe,CAACuD,CAAC,CAAC3B,MAAM,CAACkD,KAAK,CAAE;YACjDE,UAAU,EAAE1B,cAAe;YAC3BrC,WAAW,EAAC,2BAA2B;YACvCgE,QAAQ,EAAEhF,SAAU;YACpBiE,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC,eACF7E,OAAA,CAACd,MAAM;YACL4F,OAAO,EAAErC,iBAAkB;YAC3B8C,QAAQ,EAAE,CAAClF,YAAY,CAACqC,IAAI,CAAC,CAAC,IAAInC,SAAU;YAC5CiF,IAAI,EAAC,MAAM;YACXhB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAExBlE,SAAS,gBACRP,OAAA,CAACP,OAAO;cAAC+E,SAAS,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE5C7E,OAAA,CAACR,IAAI;cAACgF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC5B;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN7E,OAAA;UAAKwE,SAAS,EAAC,sEAAsE;UAAAC,QAAA,gBACnFzE,OAAA;YAAMwE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvF7E,OAAA;YAAMwE,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChD7E,OAAA;YAAAyE,QAAA,GAAOpE,YAAY,CAAC2E,MAAM,EAAC,OAAK;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CArTID,MAAM;AAAAwF,EAAA,GAANxF,MAAM;AAuTZ,eAAeA,MAAM;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}