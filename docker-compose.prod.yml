version: '3.8'

services:
  # PostgreSQL Database Service
  postgres:
    image: postgres:15-alpine
    container_name: genai_postgres_prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-genai_workflow}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - genai_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-genai_workflow}"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Qdrant Vector Database Service
  qdrant:
    image: qdrant/qdrant:v1.7.0
    container_name: genai_qdrant_prod
    restart: unless-stopped
    ports:
      - "${QDRANT_PORT:-6333}:6333"
      - "${QDRANT_GRPC_PORT:-6334}:6334"
    volumes:
      - qdrant_storage:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
      - QDRANT__LOG_LEVEL=INFO
    networks:
      - genai_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # FastAPI Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: genai_backend_prod
    restart: unless-stopped
    environment:
      # Database
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres123}@postgres:5432/${POSTGRES_DB:-genai_workflow}
      
      # OpenAI
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      
      # Web Search
      SERPAPI_API_KEY: ${SERPAPI_API_KEY:-}
      BRAVE_API_KEY: ${BRAVE_API_KEY:-}
      WEB_SEARCH_PROVIDER: ${WEB_SEARCH_PROVIDER:-none}
      
      # Vector Database
      QDRANT_HOST: qdrant
      QDRANT_PORT: 6333
      QDRANT_COLLECTION_NAME: document_embeddings
      
      # Storage
      UPLOAD_DIRECTORY: /app/uploads
      CHROMA_PERSIST_DIRECTORY: /app/chroma_db
      
      # Security
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-in-production}
      
      # Application
      ENVIRONMENT: production
      DEBUG: false
      LOG_LEVEL: INFO
      
      # CORS
      ALLOWED_ORIGINS: ${ALLOWED_ORIGINS:-http://localhost:3000,https://yourdomain.com}
      
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
      - backend_chroma:/app/chroma_db
    networks:
      - genai_network
    depends_on:
      postgres:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # React Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: genai_frontend_prod
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:8000}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    networks:
      - genai_network
    depends_on:
      - backend
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: genai_redis_prod
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - genai_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    profiles:
      - with-redis

  # Nginx Load Balancer (optional)
  nginx:
    image: nginx:alpine
    container_name: genai_nginx_prod
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - genai_network
    depends_on:
      - frontend
      - backend
    profiles:
      - with-nginx

volumes:
  postgres_data:
    driver: local
  qdrant_storage:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
  backend_chroma:
    driver: local
  redis_data:
    driver: local

networks:
  genai_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
