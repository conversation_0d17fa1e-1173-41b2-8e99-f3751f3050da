# Render Blueprint for GenAI Workflow Builder
# This file defines the complete infrastructure for production deployment

services:
  # PostgreSQL Database
  - type: pserv
    name: genai-workflow-db
    env: docker
    plan: starter
    dockerfilePath: ./database/Dockerfile
    envVars:
      - key: POSTGRES_DB
        value: genai_workflow
      - key: POSTGRES_USER
        value: genai_user
      - key: POSTGRES_PASSWORD
        generateValue: true
    disk:
      name: postgres-data
      mountPath: /var/lib/postgresql/data
      sizeGB: 10

  # FastAPI Backend Service
  - type: web
    name: genai-workflow-backend
    env: docker
    plan: starter
    dockerfilePath: ./backend/Dockerfile
    dockerContext: ./backend
    envVars:
      # Database Connection
      - key: DATABASE_URL
        fromService:
          type: pserv
          name: genai-workflow-db
          property: connectionString
      
      # Security
      - key: SECRET_KEY
        generateValue: true
      
      # CORS Configuration
      - key: CORS_ORIGINS
        value: '["https://genai-workflow-frontend.onrender.com", "http://localhost:3000"]'
      
      # Vector Database (using in-memory for starter plan)
      - key: VECTOR_STORE_TYPE
        value: "memory"
      
      # File Upload Configuration
      - key: UPLOAD_DIRECTORY
        value: "/app/uploads"
      - key: MAX_FILE_SIZE
        value: "10485760"
      
      # Application Configuration
      - key: DEBUG
        value: "false"
      - key: ENVIRONMENT
        value: "production"
    
    # Health check endpoint
    healthCheckPath: /health
    
    # Build command
    buildCommand: |
      pip install --no-cache-dir -r requirements.txt
    
    # Start command
    startCommand: |
      uvicorn app.main:app --host 0.0.0.0 --port $PORT --workers 1

  # React Frontend Service
  - type: web
    name: genai-workflow-frontend
    env: static
    buildCommand: |
      cd frontend
      npm ci
      npm run build
    staticPublishPath: ./frontend/build
    envVars:
      - key: REACT_APP_API_URL
        fromService:
          type: web
          name: genai-workflow-backend
          property: host
    
    # Custom headers for security
    headers:
      - path: /*
        name: X-Frame-Options
        value: DENY
      - path: /*
        name: X-Content-Type-Options
        value: nosniff
      - path: /*
        name: Referrer-Policy
        value: strict-origin-when-cross-origin

# Database initialization
databases:
  - name: genai-workflow-db
    databaseName: genai_workflow
    user: genai_user
    plan: starter
